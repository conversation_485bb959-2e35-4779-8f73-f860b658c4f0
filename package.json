{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "dev:mf": "SITE=modernfold nuxt dev", "dev:sf": "SITE=skyfold nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare && simple-git-hooks", "typecheck": "SITE=modernfold nuxt typecheck && SITE=skyfold nuxt typecheck", "build:storybook": "STORYBOOK_BUILD=true storybook build", "build-storybook": "STORYBOOK_BUILD=true storybook build", "clean-project": "rm -rf node_modules .nuxt .data .output", "storybook": "STORYBOOK_BUILD=true storybook dev -p 6006 --no-open", "lint": "eslint .", "lint:fix": "eslint . --fix --quiet", "simple-git-hooks": "simple-git-hooks", "test:unit": "vitest", "test:unit-coverage": "vitest --coverage", "test:unit-ui": "vitest --coverage --ui", "test:e2e": "playwright install && playwright test", "test:e2e-ui": "playwright install && playwright test --ui", "test:e2e-codegen-login": "playwright install && playwright codegen http://localhost:3000/ --save-storage=tests/e2e/auth.json", "test:e2e-codegen-authenticated": "playwright install && playwright codegen http://localhost:3000/ -o tests/e2e/codegen.spec.ts --load-storage=tests/e2e/auth.json", "storyblok": "storyblok", "storyblok:setup": "bun run layers_/integration/storyblok/cli/setup.ts"}, "dependencies": {"@algolia/requester-fetch": "4.23.2", "@atoms-studio/nuxt-swiftsearch": "0.9.0", "@digital-retex/twind-design-tokens": "^2.1.2", "@formkit/icons": "^1.6.9", "@formkit/nuxt": "^1.6.9", "@formkit/pro": "^0.127.21", "@nuxt/image": "^1.10.0", "@nuxthub/core": "0.8.17", "@sendgrid/mail": "^8.1.5", "@storyblok/richtext": "^3.2.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/vue-table": "^8.21.2", "@vueuse/core": "^13.0.0", "@vueuse/nuxt": "^13.0.0", "algoliasearch": "^5.23.4", "dayjs-nuxt": "^2.1.11", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-vue": "^8.5.2", "libphonenumber-js": "^1.12.10", "marked": "^15.0.8", "nuxt": "^3.15.4", "nuxt-auth-utils": "^0.5.20", "nuxt-i18n-micro": "^1.83.0", "pdfmake": "0.3.0-beta.18", "reka-ui": "^2.1.0", "storyblok-js-client": "^6.10.11", "valibot": "^1.0.0", "vue": "latest", "vue-router": "latest"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@nuxt/eslint": "^1.0.1", "@nuxt/icon": "1.10.3", "@nuxt/test-utils": "^3.17.0", "@nuxtjs/storybook": "8.3.3", "@playwright/test": "^1.50.1", "@storybook-vue/nuxt": "8.3.3", "@storybook/addon-a11y": "8.3.3", "@storybook/addon-essentials": "8.3.3", "@storybook/addon-interactions": "8.3.3", "@storybook/addon-links": "8.3.3", "@storybook/blocks": "8.3.3", "@storybook/builder-vite": "8.3.3", "@storybook/vue3": "8.3.3", "@tailwindcss/vite": "^4.0.7", "@total-typescript/ts-reset": "^0.6.1", "@types/bun": "^1.2.10", "@vitest/coverage-v8": "^3.0.6", "@vitest/ui": "^3.0.6", "@vue/test-utils": "^2.4.6", "chromatic": "^11.27.0", "eslint": "^9.20.1", "happy-dom": "^17.1.4", "lint-staged": "^15.4.3", "playwright-core": "^1.50.1", "simple-git-hooks": "^2.11.1", "storyblok": "^3.36.0", "storybook": "8.3.3", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.0.7", "typescript": "^5.7.3", "vitest": "^3.0.6", "vue-tsc": "^2.2.4", "wrangler": "^3.109.2"}, "lint-staged": {"*.{js,ts,mjs,vue}": ["bun lint:fix"]}, "simple-git-hooks": {"commit-msg": "bun commitlint --edit $1", "pre-commit": "bun lint-staged && bun typecheck"}, "patchedDependencies": {"@storybook-vue/nuxt@8.3.3": "patches/@<EMAIL>", "@atoms-studio/nuxt-swiftsearch@0.9.0": "patches/@<EMAIL>", "storyblok-js-client@6.10.11": "patches/<EMAIL>", "@algolia/client-search@5.23.4": "patches/@<EMAIL>", "@formkit/vue@1.6.9": "patches/@<EMAIL>"}, "overrides": {"@algolia/client-search": "5.23.4", "algoliasearch": "5.23.4", "vite": "6.0.9", "local-pkg": "1.1.1", "estree-walker": "2.0.2"}}