{"skyfold": {"index": {"title": "Skyfold"}, "portfolio": {"title": "Portfolio", "filtersButton": "Filters", "applyFilters": "Apply filters", "totalResults": "Total results", "numberOfWalls": "Number of walls", "print": "Printer friendly version", "noResults": "No results", "detail": {"backToPortfolio": "Back to portfolio", "projectSheet": "Project sheet"}}}, "forms": {"shared": {"actions": {"edit": "Edit"}}, "sampleOrder": {"shippingInformation": {"title": "General data", "fullname": {"label": "Full name", "placeholder": "Name and surname"}, "company": {"label": "Company", "placeholder": "Insert company name"}, "address": {"label": "Address", "placeholder": "Street address"}, "city": {"label": "City", "placeholder": "City or Town"}, "state": {"label": "State", "placeholder": "State"}, "postal": {"label": "Postal code", "placeholder": "Postal code"}, "country": {"label": "Country", "placeholder": "Select country", "errors": {"required": "Please select a country"}}, "prefix": {"label": "Prefix", "placeholder": "0"}, "number": {"label": "Number", "placeholder": "111-222-3333"}, "email": {"label": "Email", "placeholder": "Email"}, "po": {"label": "PO number"}, "special": {"label": "Special shipping instruction", "placeholder": "Notes and comments"}}, "shippingMethod": {"title": "Shipping method", "description": {"text": "Shipping information:", "list": ["<b>Non RUSH</b> orders are shipped via FedEx Ground (approx. 4-7 days) - no charge", "If <b>overnight rush</b> delivery is selected, your <b>shipping account number must be provided and you will be responsible for delivery charges.</b>", "NOTE: <b>Rush orders must be submitted before 2PM EST</b> in order to be shipped same day, otherwise the order will be shipped the next work day."]}, "information": {"label": "Shipping information", "errors": {"required": "Please select a shipping method"}, "help": {"account#Required": "Acct # required", "account#NotRequired": "No charge"}}, "carrier": {"label": "Carrier", "placeholder": "Select a carrier", "errors": {"required": "Please select a carrier"}}, "acct": {"label": "Acct #", "placeholder": "Acct"}}, "orderQuantity": {"chargedToYou": "Charged to you", "part": "Part#", "maxQuantity": "Max. Quantity", "description": "Description", "loadMore": "Load More ({quantity})", "quantity": "Quantity"}}, "infoRequired": "*Information required"}, "modernfold": {"index": {"title": "Modernfold"}}, "test": "design-system", "menu": {"back": "Back to {label}", "main": "Main Menu"}, "search": {"placeholder": "Search for something", "resultsNumber": "results of", "noResults": "No results", "seeAll": "See all results", "results": "results", "youSearchedFor": "You searched for", "goTo": "Go to {label}", "openSearch": "Open searchbar", "closeSearch": "Close searchbar", "portfolios": "Portfolios", "resources": "Resources"}, "atoms": {"folderButton": {"lastUpdate": "Last update:"}}, "molecules": {"switchCode": {"label": "Switch code"}}, "organisms": {"carouselCards": {"whatsNew": "What's new", "seeAll": "See all"}, "projectOfTheMonth": {"title": "Project of the month", "project": "Project", "scope": "<PERSON><PERSON>", "architect": "Architect", "specifyingDealer": "Specifying dealer", "dealer": "Dealer"}, "stepper": {"select": "Select step", "next": "Go to the next step", "previous": "Go to the previous step"}}, "portfolio": {"filters": {"title": "Filters", "reset": "Reset all", "advanced": "Advanced filters", "toggleAdvanced": "Toggle advanced filters accordion", "sort": "Sort by", "searchSomething": "Search something", "selectSomething": "Select something", "writeSomething": "Write something", "labels": {"brand": "Brand", "state": "State", "country__name": "Country", "client_name": "Client Name", "location": "Location", "product__name": "Product", "wall_types__name": "Wall Types", "project_type_sub_categories__name": "Project Type Subcategories", "finish_type": "Finish Type", "newRetrofit": "New/Retrofit", "award_winning": "Award Winning", "installation_date": "Installation Date", "architect_design": "Architect Design", "general_contractor": "General Contractor", "project_type__name": "Project Type", "dealer": "Dealer", "panel_finish": "Panel Finish", "industry_list": "Industry List"}}}}