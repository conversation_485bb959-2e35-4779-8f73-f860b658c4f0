diff --git a/dist/index.d.cts b/dist/index.d.cts
index 1191422ae932fdcb91a2683836b5fa055410d48e..8997d76766b7c288002fe9ce54008122513d7ef9 100644
--- a/dist/index.d.cts
+++ b/dist/index.d.cts
@@ -188,9 +188,9 @@ declare const FormKitSchema: vue.DefineComponent<{
 }, {}>;
 
 declare module 'vue' {
-    interface ComponentCustomProperties {
-        $formkit: FormKitVuePlugin;
-    }
+    // interface ComponentCustomProperties {
+    //     $formkit: FormKitVuePlugin;
+    // }
     interface GlobalComponents {
         FormKit: FormKitComponent;
         FormKitSchema: typeof FormKitSchema;
diff --git a/dist/index.d.mts b/dist/index.d.mts
index 1191422ae932fdcb91a2683836b5fa055410d48e..8997d76766b7c288002fe9ce54008122513d7ef9 100644
--- a/dist/index.d.mts
+++ b/dist/index.d.mts
@@ -188,9 +188,9 @@ declare const FormKitSchema: vue.DefineComponent<{
 }, {}>;
 
 declare module 'vue' {
-    interface ComponentCustomProperties {
-        $formkit: FormKitVuePlugin;
-    }
+    // interface ComponentCustomProperties {
+    //     $formkit: FormKitVuePlugin;
+    // }
     interface GlobalComponents {
         FormKit: FormKitComponent;
         FormKitSchema: typeof FormKitSchema;
