diff --git a/node_modules/storyblok-js-client/.bun-tag-881f974f6dc0f460 b/.bun-tag-881f974f6dc0f460
new file mode 100644
index 0000000000000000000000000000000000000000..e69de29bb2d1d6434b8b29ae775ad8c2e48c5391
diff --git a/node_modules/storyblok-js-client/.bun-tag-e585fd9949498502 b/.bun-tag-e585fd9949498502
new file mode 100644
index 0000000000000000000000000000000000000000..e69de29bb2d1d6434b8b29ae775ad8c2e48c5391
diff --git a/node_modules/storyblok-js-client/.bun-tag-fb9d80cfa37ab3d0 b/.bun-tag-fb9d80cfa37ab3d0
new file mode 100644
index 0000000000000000000000000000000000000000..e69de29bb2d1d6434b8b29ae775ad8c2e48c5391
diff --git a/dist/types/index.d.ts b/dist/types/index.d.ts
index 575a23600605040b7bccad5a444e84f1a4c4f1cf..b202ed4265510833c991b257c7db583e7dd4b3be 100644
--- a/dist/types/index.d.ts
+++ b/dist/types/index.d.ts
@@ -1,5 +1,5 @@
 import { default as RichTextResolver } from './richTextResolver';
-import { ICacheProvider, ISbConfig, ISbContentMangmntAPI, ISbCustomFetch, ISbLinksParams, ISbLinksResult, ISbResponseData, ISbResult, ISbStories, ISbStoriesParams, ISbStory, ISbStoryParams } from './interfaces';
+import { ICacheProvider, ISbConfig, ISbContentMangmntAPI, ISbCustomFetch, ISbLinksParams, ISbLinksResult, ISbResponseData, ISbResult, ISbStories, ISbStoriesParams, ISbStory, ISbStoryParams, ISbComponentType, ISbStoryData } from './interfaces';
 interface ComponentResolverFn {
     (...args: any): any;
 }
@@ -38,12 +38,13 @@ declare class Storyblok {
     private makeRequest;
     get(slug: 'cdn/links', params?: ISbLinksParams, fetchOptions?: ISbCustomFetch): Promise<ISbLinksResult>;
     get(slug: string, params?: ISbStoriesParams, fetchOptions?: ISbCustomFetch): Promise<ISbResult>;
+    getAll<Content = ISbComponentType<string> & { [index: string]: any }>(slug: 'cdn/stories', params: ISbStoriesParams, entity?: string, fetchOptions?: ISbCustomFetch): Promise<ISbStoryData<Content>[]>;
     getAll(slug: string, params: ISbStoriesParams, entity?: string, fetchOptions?: ISbCustomFetch): Promise<any[]>;
     post(slug: string, params: ISbStoriesParams | ISbContentMangmntAPI, fetchOptions?: ISbCustomFetch): Promise<ISbResponseData>;
     put(slug: string, params: ISbStoriesParams | ISbContentMangmntAPI, fetchOptions?: ISbCustomFetch): Promise<ISbResponseData>;
     delete(slug: string, params: ISbStoriesParams | ISbContentMangmntAPI, fetchOptions?: ISbCustomFetch): Promise<ISbResponseData>;
-    getStories(params: ISbStoriesParams, fetchOptions?: ISbCustomFetch): Promise<ISbStories>;
-    getStory(slug: string, params: ISbStoryParams, fetchOptions?: ISbCustomFetch): Promise<ISbStory>;
+    getStories<Content = ISbComponentType<string> & { [index: string]: any }>(params: ISbStoriesParams, fetchOptions?: ISbCustomFetch): Promise<ISbStories<Content>>;
+    getStory<Content = ISbComponentType<string> & { [index: string]: any }>(slug: string, params: ISbStoryParams, fetchOptions?: ISbCustomFetch): Promise<ISbStory<Content>>;
     private getToken;
     ejectInterceptor(): void;
     private _addResolveLevel;
