diff --git a/node_modules/@algolia/client-search/.bun-tag-e625aee0bd0d9666 b/.bun-tag-e625aee0bd0d9666
new file mode 100644
index 0000000000000000000000000000000000000000..e69de29bb2d1d6434b8b29ae775ad8c2e48c5391
diff --git a/dist/browser.d.ts b/dist/browser.d.ts
index a6933d39e6fe866094b6ab2e4685199442c97855..85357c74d57be16e2d7e2b720002e314e7133d4a 100644
--- a/dist/browser.d.ts
+++ b/dist/browser.d.ts
@@ -265,7 +265,7 @@ type RenderingContent = {
     widgets?: Widgets;
 };
 
-type BaseSearchResponse = Record<string, any> & {
+type BaseSearchResponse = {
     /**
      * A/B test ID. This is only included in the response for indices that are part of an A/B test.
      */
@@ -544,7 +544,7 @@ type Hit<T = Record<string, unknown>> = T & {
     _distinctSeqID?: number;
 };
 
-type SearchHits<T = Record<string, unknown>> = Record<string, any> & {
+type SearchHits<T = Record<string, unknown>> = {
     /**
      * Search results (hits).  Hits are records from your index that match the search criteria, augmented with additional attributes, such as, for highlighting.
      */
