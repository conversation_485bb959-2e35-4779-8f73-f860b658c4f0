diff --git a/node_modules/@atoms-studio/nuxt-swiftsearch/.bun-tag-37f061a2db0ec89 b/.bun-tag-37f061a2db0ec89
new file mode 100644
index 0000000000000000000000000000000000000000..e69de29bb2d1d6434b8b29ae775ad8c2e48c5391
diff --git a/node_modules/@atoms-studio/nuxt-swiftsearch/.bun-tag-832f4884f8f2fbb9 b/.bun-tag-832f4884f8f2fbb9
new file mode 100644
index 0000000000000000000000000000000000000000..e69de29bb2d1d6434b8b29ae775ad8c2e48c5391
diff --git a/dist/runtime/components/InstantSearch.vue b/dist/runtime/components/InstantSearch.vue
index 5d98d94a28c5ed0770b2e5ab5d02849444c772e2..6ee7a89d7713c205f4c6881f04623a23c395577f 100644
--- a/dist/runtime/components/InstantSearch.vue
+++ b/dist/runtime/components/InstantSearch.vue
@@ -28,12 +28,16 @@ const props = defineProps<{
 const { widgets: widgetsRef, middlewares } = toRefs(props);
 
 const searchInstance = import.meta.server
-  ? ref(instantsearch(props.configuration))
+  ? ref(instantsearch(props.configuration)) as Ref<InstantSearch>
   : // : shallowRef(instantsearch(props.configuration));
     useState(`instant_search_instance-${props.instanceKey ?? ""}`, () =>
       shallowRef(instantsearch(props.configuration)),
     );
 
+defineExpose({
+  searchInstance
+})
+
 provide<Ref<InstantSearch>>("searchInstance", searchInstance);
 
 const { setup } = useInstantSearch(searchInstance);
diff --git a/dist/runtime/components/RangeInput.vue b/dist/runtime/components/RangeInput.vue
index c3d32559427963c32b0b020f74dd83ffdf35e0a9..ae2b7dd64fe48fc2f097216730dc5b228c5a2931 100644
--- a/dist/runtime/components/RangeInput.vue
+++ b/dist/runtime/components/RangeInput.vue
@@ -28,8 +28,8 @@ const maxInput: Ref<number | undefined> = ref();
 const step = computed(() => 1 / Math.pow(10, props.precision));
 
 const values = computed(() => {
-  const [minValue, maxValue] = state.value.start;
-  const { min: minRange, max: maxRange } = state.value.range;
+  const [minValue, maxValue] = state.value!.start;
+  const { min: minRange, max: maxRange } = state.value!.range;
   return {
     min: minValue !== -Infinity && minValue !== minRange ? minValue : undefined,
     max: maxValue !== Infinity && maxValue !== maxRange ? maxValue : undefined,
@@ -45,7 +45,7 @@ const pick = (first: number | null | undefined, second: number | undefined) => {
 };
 
 const refine = (data: { min: number | undefined; max: number | undefined }) => {
-  state.value.refine([data.min, data.max]);
+  state.value!.refine([data.min, data.max]);
 };
 </script>
 <template>
diff --git a/dist/runtime/components/RefinementList.vue b/dist/runtime/components/RefinementList.vue
index e105b6123e321970281f96fd7c4b6919acc6672b..94224c028a11cd16668bc610292456dc1316d447 100644
--- a/dist/runtime/components/RefinementList.vue
+++ b/dist/runtime/components/RefinementList.vue
@@ -116,7 +116,7 @@ const state = computed(() => {
     : refinementsState.value[props.attribute];
 });
 const widgetParams = computed(
-  () => refinementsState.value[props.attribute].widgetParams,
+  () => refinementsState.value[props.attribute]!.widgetParams,
 );
 
 const suit = useSuit("RefinementList");
@@ -130,17 +130,17 @@ const searchForFacetValues = computed({
     return searchForFacetValuesQuery.value;
   },
   set(value) {
-    state.value.searchForItems(value);
+    state.value!.searchForItems(value);
     searchForFacetValuesQuery.value = value;
   },
 });
 
 const toggleShowMore = () => {
-  state.value.toggleShowMore();
+  state.value!.toggleShowMore();
 };
 
 const items = computed(() =>
-  state.value.items.map((item) =>
+  state.value!.items.map((item) =>
     Object.assign({}, item, {
       _highlightResult: { item: { value: item.highlighted } },
     }),
@@ -148,7 +148,7 @@ const items = computed(() =>
 );
 
 const refine = (value: string) => {
-  state.value.refine(value);
+  state.value!.refine(value);
   searchForFacetValuesQuery.value = "";
 };
 </script>
diff --git a/dist/runtime/components/ToggleRefinement.vue b/dist/runtime/components/ToggleRefinement.vue
index 4c1c7fc44406f07f3a0778c935ce824c8e9b7c6e..a2d7896a6414919594331ed5a34d75fd7ecb7449 100644
--- a/dist/runtime/components/ToggleRefinement.vue
+++ b/dist/runtime/components/ToggleRefinement.vue
@@ -41,7 +41,7 @@ const { state: refinementsState } = useAisWidget("toggleRefinement");
 
 const state = computed(() => refinementsState.value[props.attribute]);
 
-const widgetParams = computed(() => state.value.widgetParams);
+const widgetParams = computed(() => state.value!.widgetParams);
 
 const suit = useSuit("ToggleRefinement");
 
