// https://s00d.github.io/nuxt-i18n-micro/guide/storybook#%F0%9F%93%84-storybook-main-ts
import fs from 'node:fs'
import path from 'node:path'
import defu from 'defu'

interface TranslationStructure {
  [key: string]: TranslationStructure | TranslationValue
}
type TranslationValue = string | number | boolean | unknown | null

const locales = ['en', 'fr', 'es']
const roots = ['layers_/design-system', 'layers_/modernfold', 'layers_/skyfold']
const localesRoot = 'locales'

export const mergeTranslations = () => {
  const collectAllTranslations = (dir: string, locale: string): TranslationStructure => {
    let translations: TranslationStructure = {}

    const entries = fs.readdirSync(dir, { withFileTypes: true })
    for (const entry of entries) {
      const entryPath = path.join(dir, entry.name)
      if (entry.isDirectory()) {
        translations = defu(translations, collectAllTranslations(entryPath, locale))
      }
      else if (entry.isFile() && entry.name === `${locale}.json`) {
        const content = JSON.parse(fs.readFileSync(entryPath, 'utf-8'))
        translations = defu(translations, content)
      }
    }

    return translations
  }

  for (const locale of locales) {
    let localeTranslations = {}

    for (const root of roots) {
      const rootLocalesDir = path.join(__dirname, '../../', root, localesRoot)

      // 1. collect pages translations
      const rootLocalePagesDir = path.join(rootLocalesDir, 'pages')
      if (fs.existsSync(rootLocalePagesDir)) {
        const rootPagesMerged = collectAllTranslations(rootLocalePagesDir, locale)
        localeTranslations = defu(localeTranslations, rootPagesMerged)
      }

      // 2. collect general translations
      const rootGeneralLocaleFile = path.join(rootLocalesDir, `${locale}.json`)
      if (fs.existsSync(rootGeneralLocaleFile)) {
        const generalContent = JSON.parse(fs.readFileSync(rootGeneralLocaleFile, 'utf-8'))
        localeTranslations = defu(localeTranslations, generalContent)
      }
    }

    // 3. write merged translations
    const outputDir = path.join(__dirname, '../../storybook_locales', '/_locales/general')
    const outputPath = path.join(outputDir, `${locale}/data.json`)
    fs.mkdirSync(path.dirname(outputPath), { recursive: true })
    fs.writeFileSync(outputPath, JSON.stringify(localeTranslations, null, 2))
  }
}
