import { fileURLToPath } from 'url'
import type { StorybookConfig } from '@nuxtjs/storybook'
import { mergeConfig } from 'vite'
import { mergeTranslations } from './config/nuxt-i18n-micro'

if (import.meta.dev) {
  mergeTranslations()
}

const config: StorybookConfig = {
  stories: [
    '../app/components/**/*.stories.@(js|jsx|mjs|ts|tsx)',
    '../layers_/design-system/components/**/*.stories.@(js|jsx|mjs|ts|tsx)',
  ],
  addons: [
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@storybook/addon-interactions',
    '@storybook/addon-a11y',
  ],
  framework: {
    name: '@storybook-vue/nuxt',
    options: {},
  },
  staticDirs: ['../public', '../storybook_locales'],
  docs: {},
  core: {
    disableTelemetry: true,
  },
  async viteFinal(config) {
    return mergeConfig(config, {
      optimizeDeps: {
        include: ['dayjs', 'dayjs/plugin/updateLocale', 'dayjs/plugin/relativeTime', 'dayjs/plugin/utc'],
      },
      resolve: {
        alias: {
          'vue': 'vue/dist/vue.esm-bundler.js',
          '@design-system': fileURLToPath(
            new URL('../layers_/design-system', import.meta.url),
          ),
          '@root': fileURLToPath(new URL('../', import.meta.url)),
        },
      },
    })
  },
}
export default config
