import type { Preview } from '@storybook/vue3'
import { INITIAL_VIEWPORTS } from '@storybook/addon-viewport'
import { setup } from '@storybook/vue3'
import { plugin, defaultConfig } from '@formkit/vue'
import { h, Suspense } from 'vue'
import formkitConfig from '../layers_/forms/formkit.config'

const preview: Preview = {
  parameters: {
    chromatic: {
      disableSnapshot: true,
    },
    viewport: {
      viewports: INITIAL_VIEWPORTS,
    },
  },
  decorators: [
    story => ({
      setup: () => () => h(Suspense, {}, [h(story())]),
    }),
  ],
}

setup((app, _context) => {
  app.use(plugin, defaultConfig(formkitConfig()))
})

export default preview
