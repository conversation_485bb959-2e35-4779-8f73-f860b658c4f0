import * as v from 'valibot'

export const sites = ['modernfold', 'skyfold'] as const

const envSchema = v.object({
  SITE: v.union(sites.map(site => v.literal(site))),
  API_BASE_URL: v.string(),
  API_KEY: v.string(),
  STORYBLOK_ACCESS_TOKEN: v.string(),
  FORMKIT_PRO_KEY: v.string(),
  STORYBLOK_MANAGMENT_API_ACCESS_TOKEN: v.string(),
  STORYBLOK_SPACE_ID: v.string(),
  NUXT_SESSION_PASSWORD: v.string(),
  AUTH0_CLIENT_ID: v.string(),
  AUTH0_CLIENT_SECRET: v.string(),
  AUTH0_DOMAIN: v.string(),
  AUTH0_AUDIENCE: v.string(),
  BASE_URL: v.string(),
  SENDGRID_API_KEY: v.string(),
  AIA_FORM_MAIL_TO: v.string(),
  AIA_FORM_SENDGRID_TEMPLATE_ID: v.string(),
})

// @ts-expect-error wrong type override
process.env.BASE_URL ??= process.env.CF_PAGES_URL

export const env = v.parse(envSchema, process.env)

export type SiteType = typeof env.SITE
