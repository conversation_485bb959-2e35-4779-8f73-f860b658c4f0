<script setup lang="ts">
import type { MappedData } from '@integration/server/api/[locale]/storyblok/page/[...path].get'
import type { Breadcrumb } from '@design-system/components/Atoms/AtomsBreadcrumbs.props'

const { currentPathWithoutLocale } = useLanguage()
const { $getLocale, localePath } = useI18n()
const requestFetch = useRequestFetch()

const { data, error } = await useAsyncData(
  `storyblok-page-${currentPathWithoutLocale.value}`,
  async () => {
    const [page, breadcrumbs] = await Promise.all([
      requestFetch<MappedData>(`/api/${$getLocale()}/storyblok/page${currentPathWithoutLocale.value}`),
      requestFetch<Breadcrumb[]>(
        `/api/${$getLocale()}/storyblok/breadcrumbs/${currentPathWithoutLocale.value}`,
      )
        .then(res => res ?? [] as Breadcrumb[])
        .catch(() => [] as <PERSON><PERSON><PERSON>rumb[]),
    ])
    return { page, breadcrumbs }
  },
)

if (error.value?.statusCode === 403) {
  await navigateTo(localePath('/'))
}

if (error.value || !data.value) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Page Not Found',
    fatal: true,
  })
}

const { page, breadcrumbs } = data.value

definePageMeta({
  middleware: 'custom-layout',
})
</script>

<template>
  <div class="contents">
    <LazyPagesPortfolio v-if="page?.component === 'PagesPortfolio'" v-bind="page" />
    <LazyPagesPortfolioListingPage v-else-if="page?.component === 'PagesPortfolioListingPage'" />
    <LazyPagesResourceCategory v-else-if="page?.component === 'PagesResourceCategory'" v-bind="page" />
    <LazyPagesAiaForm v-else-if="page?.component === 'PagesAiaForm'" v-bind="page" :breadcrumbs />
  </div>
</template>
