{"skyfold": {"index": {"title": "Skyfold"}, "portfolio": {"title": "Portfolio", "filtersButton": "Filters", "applyFilters": "Apply filters", "totalResults": "Total results", "numberOfWalls": "Number of walls", "print": "Printer friendly version", "noResults": "No results", "detail": {"backToPortfolio": "Back to portfolio", "projectSheet": "Project sheet", "share": {"shareExt": "Share externally", "linkCopied": "Link copied!", "linkSuccess": "Link successfully copied to clipboard.", "close": "Ok, close"}, "productFamily": "Product Family", "skyfoldNumber": "Project Ref#", "projectType": "Project Type", "country": "Country", "stateProvince": "State/Province", "installationDate": "Installation Date", "architect": "Architect / Interior Designer", "generalContractor": "General Contractor", "newRetrofit": "New or Retrofit", "new": "New", "retrofit": "Retrofit", "wallsNumber": "Number of Walls", "width": "Largest Wall Width", "height": "Largest Wall Height", "feet": "feet", "wallTypes": "Wall Types", "panelFinish": "Panel Finish", "finishType": "Finish Type", "industry": "Industry", "location": "Location", "dealer": "Dealer"}}}, "footer": {"mini": {"copyrightStatement": "© Skyfold Inc. {year}. All rights reserved. No reproduction allowed without the consent of Skyfold Inc."}}, "forms": {"shared": {"actions": {"edit": "Edit"}, "cta": {"submit": "Submit"}}, "dealerInformation": {"dealer": {"title": "Dealer", "name": {"label": "Name", "placeholder": "Name"}, "email": {"label": "Email", "placeholder": "Email"}, "address": {"label": "Address", "placeholder": "Address"}, "prefix": {"label": "Prefix", "placeholder": "0"}, "number": {"label": "Number", "placeholder": "111-222-3333"}}, "salesperson": {"title": "Salesperson", "name": {"label": "Name", "placeholder": "Name"}, "email": {"label": "Email", "placeholder": "Email"}, "address": {"label": "Address", "placeholder": "Address"}, "prefix": {"label": "Prefix", "placeholder": "0"}, "number": {"label": "Number", "placeholder": "111-222-3333"}}}, "participants": {"title": "Participants", "addParticipant": "Add Participant", "partecipants_card": {"aiaMember": "AIA member", "certificateRequest": "Certificate request", "membershipNumber": "AIA Membership Number", "email": "Email address", "yes": "Yes", "no": "No", "remove": "Remove Participant", "edit": "Edit"}, "sortBy": {"lastAdded": "Last added", "firstAdded": "First added"}, "showAll": "Show all ({count})", "showLess": "Show less", "addPartecipantModal": {"title": "Add Participant", "editTitle": "Edit Participant", "aiaMember": "AIA member", "yes": "Yes", "certificateRequest": "Certificate request", "membershipNumber": "AIA Membership Number", "membershipNumberPlaceholder": "00-11-22-33", "fullName": "Full name of participant", "fullNamePlaceholder": "<PERSON>", "email": "E-mail address", "emailPlaceholder": "<EMAIL>", "save": "Save", "update": "Update"}}, "programCompletion": {"programProvided": "Program provided", "provided": {"title": "Provided", "conductorName": "Name of person that conducted the seminar", "placeholderConductorName": "Name and surname", "firmName": "Architectural firm name", "placeholderFirmName": "Firm name", "city": "City", "placeholderCity": "City name", "date": "Select a date", "sendTo": {"label": "Send to*", "requiredMessage": "Send to is required.", "options": {"dealer": "Dealer", "rsm": "RSM"}}, "street": "Street", "placeholderStreet": "Street address", "stateProvince": "State/Prov", "placeholderStateProvince": "State or Province name", "courseGiven": {"label": "Course given", "options": {"inPerson": "In person", "online": "Online"}}}, "documents": {"title": "Documents", "presentations": "AIA presentations", "speakerNotes": "Speaker notes", "evaluations": "Course evaluations", "summaries": "Program summaries"}}, "additionalInformation": {"title": "Additional Information", "email": {"label": "Carbon copy (CC)", "placeholder": "<EMAIL>", "remove": "Remove email"}, "comments": {"label": "Comments", "placeholder": "Notes and comments"}}}}