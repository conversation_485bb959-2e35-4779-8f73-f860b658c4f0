import * as v from 'valibot'
import SGMail from '@sendgrid/mail'
import dayjs from 'dayjs'

const ApiPartecipantSchema = v.object({
  aiaMember: v.boolean(),
  aiaNumber: v.optional(v.string()),
  participant: v.string(),
  certRequest: v.boolean(),
  participantEmail: v.string(),
})

const ApiFileSchema = v.object({
  fileBase64: v.string(),
  filename: v.string(),
  fileMimeType: v.string(),
})

const ApiAIAProgramCompletionSchema = v.object({
  programProvided: v.string(),
  submittedBy: v.string(),
  emailCurrentUser: v.string(),
  dealer: v.string(),
  ccTo: v.optional(v.array(v.string())),
  conductorName: v.string(),
  sendCertificateToDealer: v.boolean(),
  architectFirm: v.string(),
  street: v.string(),
  city: v.string(),
  state: v.string(),
  completed: v.string(),
  courseGiven: v.string(),
  partecipants: v.array(ApiPartecipantSchema),
  file: v.optional(ApiFileSchema),
})

export type AIAProgramCompletionSchema = v.InferOutput<typeof ApiAIAProgramCompletionSchema>
export type PartecipantRow = v.InferOutput<typeof ApiPartecipantSchema>

const booleanToString = (value: boolean) => value ? 'Yes' : 'No'

export default defineEventHandler(async (event) => {
  const logPrefix = '[API /aia/programCompletion]'

  const { sendgrid, aiaForm } = useRuntimeConfig()

  try {
    const body = await readValidatedBody(event, body => v.parse(ApiAIAProgramCompletionSchema, body))
    SGMail.setApiKey(sendgrid.apiKey)
    // For EU Data Resident sending (via an EU-pinned subuser), in the following step you must include setDataResidency -- does it still exist??
    // SGMail.setDataResidency('eu')

    const submissionDate = dayjs().format('YYYY-MM-DD HH:mm:ss')

    const attachments = body.file
      ? [{
          filename: body.file.filename,
          type: body.file.fileMimeType,
          disposition: 'attachment',
          content: body.file.fileBase64,
        }]
      : []

    const msg: SGMail.MailDataRequired = {
      // TODO: use emailCurrentUser when authorize domain on sendgrid
      from: '<EMAIL>', // body.emailCurrentUser
      to: aiaForm.mailTo,
      cc: body.ccTo,
      attachments,
      templateId: aiaForm.templateId,
      dynamicTemplateData: {
        submissionDate,
        programProvided: body.programProvided,
        submittedBy: body.submittedBy,
        dealer: body.dealer,
        ccTo: body.ccTo,
        conductorName: body.conductorName,
        sendCertificateToDealer: booleanToString(body.sendCertificateToDealer),
        architectFirm: body.architectFirm,
        street: body.street,
        city: body.city,
        state: body.state,
        completed: body.completed,
        courseGiven: body.courseGiven,
        partecipants: body.partecipants?.map(partecipant => ({
          ...partecipant,
          aiaMember: booleanToString(partecipant.aiaMember),
          certRequest: booleanToString(partecipant.certRequest),
        })),
      },
    }

    try {
      await SGMail.send(msg)
      return
    }
    catch (error) {
      console.error('Error', JSON.stringify(error))

      throw createError({
        statusCode: 500,
        statusMessage: `${logPrefix} Error sending mail`,
        data: {
          // @ts-expect-error error is unknown
          message: error?.response?.body?.errors?.[0]?.message,
        },
      })
    }
  }
  catch (error) {
    console.error('Error', JSON.stringify(error))
    throw createError({
      statusCode: 500,
      statusMessage: `${logPrefix} Error processing aia program completion`,
    })
  }
})
