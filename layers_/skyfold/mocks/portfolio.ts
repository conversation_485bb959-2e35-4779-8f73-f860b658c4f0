export const h1Text = '<PERSON><PERSON> Gandal Pordy & E<PERSON>'

export const slidesMock = [
  {
    title: 'Slide 1',
    alt: 'Description for Slide 1',
    url: 'https://picsum.photos/400/300',
  },
  {
    title: 'Slide 2',
    alt: 'Description for Slide 2',
    url: 'https://picsum.photos/400/300',
  },
  {
    title: 'Slide 3',
    alt: 'Description for Slide 3',
    url: 'https://picsum.photos/400/300',
  },
  {
    title: 'Slide 4',
    alt: 'Description for Slide 4',
    url: 'https://picsum.photos/400/300',
  },
  {
    title: 'Slide 5',
    alt: 'Description for Slide 5',
    url: 'https://picsum.photos/400/300',
  },
  {
    title: 'Slide 6',
    alt: 'Description for Slide 6',
    url: 'https://picsum.photos/400/300',
  },
]

export const attributesMock = Array.from({ length: 16 }, () => ({
  informationName: 'Information name',
  dataLabel: 'Data label',
}))

export const projectMock = {
  project: 'Texas A&M Mitchell Physics Bldg College Station, TX USA',
  scope: '2 Stepped Classic 51 Walls',
  architect: '<PERSON>',
  specifyingDealer: 'Styles, New York',
  dealer: 'Construction Architectural Products (TRW)',
  projectImage: {
    src: 'https://picsum.photos/400/300',
    alt: 'Description for the image',
  },
  longDescription: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullamco laboriosam, nisi ut aliquid ex ea commodi consequatur. Duis aute irure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
  descriptionImage: {
    src: 'https://picsum.photos/400/300',
    alt: 'Description for the image',
  },
}

export const video = {
  title: 'Video',
  url: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
}

export const slideShow = {
  title: 'Slide show',
  url: 'https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1',
}
