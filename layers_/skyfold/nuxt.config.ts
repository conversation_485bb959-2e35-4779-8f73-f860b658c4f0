// https://nuxt.com/docs/api/configuration/nuxt-config
import { fileURLToPath } from 'url'
import * as v from 'valibot'

const skyfoldEnvSchema = v.object({
  AIA_FORM_SENDGRID_TEMPLATE_ID: v.string(),
  AIA_FORM_MAIL_TO: v.string(),
})

export const skyfoldEnv = v.parse(skyfoldEnvSchema, process.env)

export default defineNuxtConfig({
  extends: ['../integration'],
  runtimeConfig: {
    aiaForm: {
      templateId: skyfoldEnv.AIA_FORM_SENDGRID_TEMPLATE_ID,
      mailTo: skyfoldEnv.AIA_FORM_MAIL_TO.split(','),
    },
  },
  alias: {
    '@skyfold': fileURLToPath(new URL('./', import.meta.url)),
    '@integration': fileURLToPath(
      new URL('../integration', import.meta.url),
    ),
    '@forms': fileURLToPath(
      new URL('../forms', import.meta.url),
    ),
    '@design-system': fileURLToPath(
      new URL('../design-system', import.meta.url),
    ),
  },
  nitro: {
    prerender: {
      ignore: [path => !path.startsWith('/_locales')],
    },
  },
})
