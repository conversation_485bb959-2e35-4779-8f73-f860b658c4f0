<script lang="ts" setup>
import type { PagesAiaFormWithBreadcrumbs } from './PagesAiaForm.props'

defineProps<PagesAiaFormWithBreadcrumbs>()
</script>

<template>
  <div class="container min-h-screen flex flex-col">
    <AtomsBreadcrumbs v-if="breadcrumbs?.length" :items="breadcrumbs" />
    <h1 v-if="title" class="headline-1 mb-padding-xs">
      {{ title }}
    </h1>
    <div class="flex-1 flex flex-col">
      <FormsAia
        :participants-description="participantsDescription"
        :program-options="programOptions"
        :program-description="programDescription"
        :program-documents="programDocuments"
      />
    </div>
  </div>
</template>
