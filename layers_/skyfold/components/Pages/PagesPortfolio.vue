<script setup lang="ts">
import type { CarouselImage } from '@design-system/components/Organisms/Carousel/OrganismsCarouselPortfolioSlides.props'
import type { Attribute } from '@design-system/components/Molecules/MoleculesAttributesBox.props'
import type { DealerStoryblok } from '@integration/storyblok/data/components'
import type { ISbStoryData } from 'storyblok-js-client'
import type { OrganismsProjectOfTheMonthProps } from '@design-system/components/Organisms/OrganismsProjectOfTheMonth.props'
import type { PagesPortfolioProps } from './PagesPortfolio.props'

const props = defineProps<PagesPortfolioProps>()

const { ts, localePath } = useI18n()

const copyLinkModal = defineModel<boolean>()

const carouselPhoto = computed<CarouselImage[]>(() => {
  if (!props.data.photos) return []
  return props.data?.photos.map(photo => ({
    url: photo.filename ?? '',
    alt: photo.alt ?? '',
    title: photo.title ?? '',
  }))
})

const projectOfTheMonth = computed<OrganismsProjectOfTheMonthProps | null>(() => {
  if (!props.data?.project_of_the_month) return null
  return {
    project: props.data.project_of_the_month.content.project ?? '-',
    scope: props.data.project_of_the_month.content.scope ?? '-',
    architect: props.data.project_of_the_month.content.architect ?? '-',
    specifyingDealer: props.data.project_of_the_month.content.specifying_dealer ?? '-',
    dealer: (props.data.project_of_the_month.content.dealer as ISbStoryData<DealerStoryblok>).content?.name ?? '-',
    projectImage: {
      src: props.data.project_of_the_month.content.image?.filename ?? '',
      alt: props.data.project_of_the_month.content.image?.alt ?? '-',
    },

  }
})

const attributes = computed<Attribute[]>(() => {
  if (!props.data) return []

  const {
    product,
    skyfold_number,
    project_type,
    country,
    state,
    installation_date,
    architect_design,
    general_contractor,
    newRetrofit,
    number_of_walls,
    largest_size_w,
    largest_size_h,
    wall_types,
    panel_finish,
    finish_type,
    industry_list,
    location,
    dealer,
  } = props.data

  const labels = {
    productFamily: ts('skyfold.portfolio.detail.productFamily'),
    skyfoldNumber: ts('skyfold.portfolio.detail.skyfoldNumber'),
    projectType: ts('skyfold.portfolio.detail.projectType'),
    country: ts('skyfold.portfolio.detail.country'),
    stateProvince: ts('skyfold.portfolio.detail.stateProvince'),
    installationDate: ts('skyfold.portfolio.detail.installationDate'),
    architect: ts('skyfold.portfolio.detail.architect'),
    generalContractor: ts('skyfold.portfolio.detail.generalContractor'),
    newRetrofit: ts('skyfold.portfolio.detail.newRetrofit'),
    new: ts('skyfold.portfolio.detail.new'),
    retrofit: ts('skyfold.portfolio.detail.retrofit'),
    wallsNumber: ts('skyfold.portfolio.detail.wallsNumber'),
    width: ts('skyfold.portfolio.detail.width'),
    height: ts('skyfold.portfolio.detail.height'),
    feet: ts('skyfold.portfolio.detail.feet'),
    wallTypes: ts('skyfold.portfolio.detail.wallTypes'),
    panelFinish: ts('skyfold.portfolio.detail.panelFinish'),
    finishType: ts('skyfold.portfolio.detail.finishType'),
    industry: ts('skyfold.portfolio.detail.industry'),
    location: ts('skyfold.portfolio.detail.location'),
    dealer: ts('skyfold.portfolio.detail.dealer'),
  }

  return [
    { label: labels.productFamily, value: product?.name ?? '-' },
    { label: labels.skyfoldNumber, value: skyfold_number ?? '-' },
    { label: labels.projectType, value: project_type?.name ?? '-' },
    { label: labels.country, value: country?.name ?? '-' },
    { label: labels.stateProvince, value: state ?? '-' },
    { label: labels.installationDate, value: installation_date ?? '-' },
    { label: labels.architect, value: architect_design ?? '-' },
    { label: labels.generalContractor, value: general_contractor ?? '-' },
    {
      label: labels.newRetrofit,
      value:
        newRetrofit === 'N'
          ? labels.new
          : newRetrofit === 'R'
            ? labels.retrofit
            : '-',
    },
    { label: labels.wallsNumber, value: number_of_walls ?? '-' },
    {
      label: labels.width,
      value: largest_size_w ? `${largest_size_w} ${labels.feet}` : '-',
    },
    {
      label: labels.height,
      value: largest_size_h ? `${largest_size_h} ${labels.feet}` : '-',
    },
    { label: labels.wallTypes, value: wall_types?.name ?? '-' },
    { label: labels.panelFinish, value: panel_finish ?? '-' },
    { label: labels.finishType, value: finish_type ?? '-' },
    { label: labels.industry, value: industry_list ?? '-' },
    { label: labels.location, value: location ?? '-' },
    { label: labels.dealer, value: dealer ?? '-' },
  ]
})

const downloadPdf = () => {
  window.open(props.data.pdf!.filename!, '_blank')
}

const shareExternally = async () => {
  try {
    await navigator.clipboard.writeText(window.location.href)
    copyLinkModal.value = true
  }
  catch (e) {
    console.error('Failed to copy URL:', e)
  }
}
</script>

<template>
  <div class="flex flex-col gap-6 bg-brand">
    <div class="container flex flex-col gap-2 pt-padding-xl md:pt-padding-xxl lg:pt-padding-xl">
      <div class="flex items-center gap-2">
        <AtomsLink :to="localePath('/portfolio')">
          <AtomsIcon name="NavArrowLeft" />
          {{ $ts('skyfold.portfolio.detail.backToPortfolio') }}
        </AtomsLink>
      </div>
      <h1 class="headline-1 text-neutral">
        {{ data.project_name }}
      </h1>
      <div class="flex items-center justify-between w-full">
        <div class="flex items-center gap-2">
          <AtomsLink v-if="data.pdf && data.pdf.filename" @click="downloadPdf">
            <AtomsIcon name="ExtPDFIcon" />
            {{ $ts('skyfold.portfolio.detail.projectSheet') }}
          </AtomsLink>
        </div>
        <div class="flex items-center gap-2">
          <AtomsLink @click="shareExternally">
            <AtomsIcon name="GenShareDots" />
            {{ $ts('skyfold.portfolio.detail.share.shareExt') }}
          </AtomsLink>
        </div>
      </div>
    </div>

    <div v-if="data.photos?.length" class="bg-neutral">
      <OrganismsCarouselPortfolio
        :slides="carouselPhoto"
        :has-autoplay="false"
        class="max-w-[1200px] mx-auto"
      />
    </div>

    <div class="bg-neutral">
      <MoleculesAttributesBox :attributes="attributes" />
    </div>

    <div v-if="projectOfTheMonth" class="bg-neutral">
      <OrganismsProjectOfTheMonth v-bind="projectOfTheMonth" />
    </div>

    <div v-if="data.video && data.video?.filename" class="bg-neutral">
      <OrganismsVideo title="Video" :url="data.video.filename" />
    </div>

    <div v-if="data.slide_show?.filename" class="bg-neutral">
      <OrganismsVideo title="Slideshow" :url="data.slide_show.filename" />
    </div>
  </div>
  <MoleculesDialog
    v-model="copyLinkModal"
    :title="$ts('skyfold.portfolio.detail.share.linkCopied')"
    :message="$ts('skyfold.portfolio.detail.share.linkSuccess')"
    icon="GenDuplicateIcon"
    content-class="min-w-88"
  >
    <template #actions>
      <AtomsButton
        anatomy="secondary"
        @click="copyLinkModal = false"
      >
        {{ $ts('skyfold.portfolio.detail.share.close') }}
      </AtomsButton>
    </template>
  </MoleculesDialog>
</template>
