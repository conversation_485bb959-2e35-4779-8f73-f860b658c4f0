import type { MappedContent } from '@integration/server/types/storyblok'
import type { PortfolioListingPageStoryblok } from '@integration/storyblok/data/components'
import type { WithRelationships } from '@integration/types/storyblok'

export const pagesPortfolioListingMap = (_: WithRelationships<PortfolioListingPageStoryblok>): MappedContent<'PagesPortfolioListingPage', object> => {
  return {
    component: 'PagesPortfolioListingPage',
  }
}
