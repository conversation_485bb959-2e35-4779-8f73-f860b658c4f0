import type { MappedContent } from '@integration/server/types/storyblok'
import type { AiaFormProgramCompletionStoryblok } from '@integration/storyblok/data/components'
import type { WithRelationships } from '@integration/types/storyblok'
import type { StoryblokRichTextNode } from '@storyblok/richtext'
import type { PagesAiaFormProps } from './PagesAiaForm.props'

export const pagesAiaFormProgramCompletionMap = (content: WithRelationships<AiaFormProgramCompletionStoryblok>): MappedContent<'PagesAiaForm', PagesAiaFormProps> => {
  return {
    component: 'PagesAiaForm',
    title: content.page_fields?.[0]?.title,
    programOptions: (content.program_options ?? []).map(option => ({
      label: option.label ?? '',
      value: option.value ?? '',
    })),
    participantsDescription: content.participants_description as StoryblokRichTextNode,
    programDescription: content.program_provided_description as <PERSON>blokRichTextNode,
    programDocuments: content.program_provided_documents ?? [],
  }
}
