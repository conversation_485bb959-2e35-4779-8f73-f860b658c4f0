import type { MappedContent } from '@integration/server/types/storyblok'
import type { PortfolioStoryblok } from '@integration/storyblok/data/components'
import type { WithRelationships } from '@integration/types/storyblok'
import type { PagesPortfolioProps } from './PagesPortfolio.props'

export const pagesPortfolioMap = (content: WithRelationships<PortfolioStoryblok>): MappedContent<'PagesPortfolio', PagesPortfolioProps> => {
  return {
    component: 'PagesPortfolio',
    data: content,
  }
}
