import type { BreadcrumbsProps } from '@integration/types/breadcrumbs'

import type { ProgramCompletionDocumentStoryblok } from '@integration/storyblok/data/components'
import type { StoryblokRichTextNode } from '@storyblok/richtext'
import type { Option } from '~/types/form'

export type PagesAiaFormProps = {
  title?: string
  programOptions: Option[]
  participantsDescription?: StoryblokRichTextNode
  programDescription?: StoryblokRichTextNode
  programDocuments?: ProgramCompletionDocumentStoryblok[]
}

export type PagesAiaFormWithBreadcrumbs = PagesAiaFormProps & BreadcrumbsProps
