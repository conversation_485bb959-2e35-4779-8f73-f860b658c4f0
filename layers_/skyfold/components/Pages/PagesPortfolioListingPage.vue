<script setup lang='ts'>
import type {
  IndexWidget,
  Widget,
  InstantSearchOptions,
} from 'instantsearch.js'
import { algoliasearch } from 'algoliasearch'
import { createFetchRequester } from '@algolia/requester-fetch'
import type { PortfolioAlgolia } from '@integration/algolia/indexes/portfolios'
import usePortfolioPdf from '@design-system/composables/usePortfolioPdf'
import type { RefinementListItem } from 'instantsearch.js/es/connectors/refinement-list/connectRefinementList'

const { $getLocale, localePath } = useI18n()

const searchClient = algoliasearch(
  useRuntimeConfig().public.algolia.applicationId,
  useRuntimeConfig().public.algolia.apiKey,
  // @ts-expect-error version mismatch?
  { requester: createFetchRequester() },
)

const configuration = computed<InstantSearchOptions>(() => ({
  indexName: `skyfold__portfolios__${$getLocale()}`,
  searchClient,
}))

const sortingItems = ref([
  { value: `skyfold__portfolios__${$getLocale()}`, label: 'Default' },
])
const hitsPerPage = 12
const widgets = ref<(Widget | IndexWidget)[]>([
  useAisHits({}),
  useAisSearchBox({}),
  useAisSortBy({
    items: sortingItems.value,
  }),
  useAisClearRefinements({}, 'all'),
  useAisMenu({
    attribute: 'product.name',
    sortBy: ['name:asc'],
  }),
  useAisMenu({
    attribute: 'project_type.name',
    sortBy: ['name:asc'],
  }),
  useAisMenu({
    attribute: 'finish_type',
    sortBy: ['name:asc'],
  }),

  // Accordion
  useAisRefinementList({
    attribute: 'special_finish',
    showMore: false,
    sortBy: ['name:asc'],
  }),
  // useAisMenu({
  //   attribute: 'content.finish_keywords',
  //   showMore: false,
  //   sortBy: ['name:asc'],
  // }),
  useAisMenu({
    attribute: 'brand',
    showMore: false,
    sortBy: ['name:asc'],
  }),
  useAisMenu({
    attribute: 'architect_design',
    showMore: false,
    sortBy: ['name:asc'],
  }),
  useAisMenu({
    attribute: 'dealer',
    showMore: false,
    sortBy: ['name:asc'],
  }),
  useAisMenu({
    attribute: 'country.name',
    showMore: false,
    sortBy: ['name:asc'],
  }),
  useAisMenu({
    attribute: 'state',
    showMore: false,
    sortBy: ['name:asc'],
  }),
  useAisMenu({
    attribute: 'location',
    showMore: false,
    sortBy: ['name:asc'],
  }),
  useAisRefinementList({
    attribute: 'number_of_walls',
    showMore: false,
    sortBy: ['name:asc'],
  }),

  useAisStats({}),
  useAisPagination({}),
  useAisConfigure({
    searchParameters: {
      hitsPerPage,
      distinct: true,
    },
  }),
])

const isMobileFiltersModalOpen = ref(false)

const totalWallsNumber = (items: RefinementListItem[]) => {
  return items.map(item => item.count).reduce((sum, n) => sum + n, 0)
}

const refineSlug = (slug: string) => {
  const parts = slug.split('/')
  const relativePath = parts.slice(-2).join('/')

  return localePath(relativePath)
}

const intantSearch = useTemplateRef('instantSearch')
const searchInstance = computed(() => intantSearch.value?.searchInstance)

const { downloadPdfMake, isDownloadingPdf } = usePortfolioPdf(searchInstance)

const handleChangePage = (refine: (page: number) => void, pageNumber: number) => {
  refine(pageNumber)
  scrollTo({ top: 0, behavior: 'smooth' })
}
</script>

<template>
  <div class="container">
    <AisInstantSearch
      ref="instantSearch"
      :widgets
      :configuration
      instance-key="portfolio"
    >
      <div class="grid-standard-full items-start bg-brand pt-6 pb-12 gap-y-xxl">
        <div class="col-span-full space-y-padding-xs">
          <div class="flex justify-between items-baseline">
            <h1 class="headline-1">
              {{ $ts("skyfold.portfolio.title") }}
            </h1>
            <!-- Filters mobile -->
            <div class="block lg:hidden">
              <MoleculesDialogWrapper @is-open="isMobileFiltersModalOpen = $event">
                <template #button>
                  <AtomsButton anatomy="primary" size="m">
                    <span> {{ $ts("skyfold.portfolio.filtersButton") }}</span>
                    <template #icon>
                      <AtomsIcon name="NavFilter" />
                    </template>
                  </AtomsButton>
                </template>
                <template #closeButton>
                  <AtomsButton anatomy="primary" size="l">
                    {{ $ts("skyfold.portfolio.applyFilters") }}
                  </AtomsButton>
                </template>
                <AlgoliaPortfolioFilters />
              </MoleculesDialogWrapper>
            </div>
          </div>

          <div class="flex flex-col gap-y-xs md:flex-row md:justify-between">
            <div>
              <AisStats>
                <template #default="{ nbHits }">
                  <p>{{ $t("skyfold.portfolio.totalResults") }} {{ nbHits }}</p>
                </template>
              </AisStats>
              <AisRefinementList attribute="number_of_walls">
                <template #default="{ items }">
                  <p>{{ $t("skyfold.portfolio.numberOfWalls") }} {{ totalWallsNumber(items) }} </p>
                </template>
              </AisRefinementList>
            </div>
            <AtomsLink :disabled="isDownloadingPdf" @click="downloadPdfMake">
              <AtomsIcon v-if="isDownloadingPdf" class="animate-spin" name="LoaderIcon" />
              <AtomsIcon v-else name="ExtPDFIcon" />
              <span class="pl-padding-xxs">{{ $t("skyfold.portfolio.print") }}</span>
            </AtomsLink>
          </div>
        </div>

        <div class="col-span-full md:col-span-3 lg:col-span-4 xl:col-span-3 space-y-padding-md">
          <AlgoliaFiltersSort name="sortPortfolio" />

          <!-- Filters desktop -->
          <AlgoliaPortfolioFilters v-if="!isMobileFiltersModalOpen" class="hidden lg:block" />
        </div>

        <AisHits v-slot="{ items }" class="col-span-full lg:col-span-8 xl:col-span-9 grid grid-cols-subgrid gap-y-lg ">
          <div
            v-for="(card, index) in items as unknown as PortfolioAlgolia[]"
            :key="index"
            class="col-span-full md:col-span-4 xl:col-span-3"
          >
            <NuxtLink :to="refineSlug(card.full_slug)" class="cursor-pointer">
              <OrganismsCard
                class="cursor-pointer"
                :image="{
                  src: card?.photos?.[0]?.filename ?? '',
                  alt: 'card image',
                  width: 270,
                  height: 200,
                }"
              >
                <AtomsTag v-if="card?.dealer" :label="card.dealer" />
                <OrganismsCardTitle v-if="card.project_name">
                  {{ card?.project_name }}
                </OrganismsCardTitle>
                <OrganismsCardSubtitle v-if="card?.architect_design">
                  {{ card.architect_design }}
                </OrganismsCardSubtitle>
                <OrganismsCardLocation v-if="card?.state">
                  {{ `${card?.state} ${card?.country?.name}` }}
                </OrganismsCardLocation>
                <time class="caption-1"> {{ card.installation_date }}</time>
              </OrganismsCard>
            </NuxtLink>
          </div>
          <p v-if="items.length === 0" class="col-span-full self-center place-self-center">
            {{ $ts("skyfold.portfolio.noResults") }}
          </p>
        </AisHits>

        <AisPagination
          class="col-span-full max-w-[720px] w-full m-auto"
          :show-first="true"
          :show-previous="true"
          :show-next="true"
          :show-last="true"
        >
          <template #default="{ refine, nbHits, nbPages, currentRefinement }">
            <MoleculesPagination
              v-show="nbPages"
              :model-value="currentRefinement + 1"
              size="M"
              :items-per-page="hitsPerPage"
              :total="nbHits"
              @click-on-page="(page: number) => handleChangePage(refine, page - 1)"
            />
          </template>
        </AisPagination>
      </div>
    </AisInstantSearch>
  </div>
</template>
