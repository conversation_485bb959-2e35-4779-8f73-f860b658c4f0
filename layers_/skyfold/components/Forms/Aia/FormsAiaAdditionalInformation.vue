<script lang="ts" setup>
import type { FormsAiaAdditionalInformationProps } from './FormsAiaAdditionalInformation.props'

const props = defineProps<FormsAiaAdditionalInformationProps>()

defineEmits<{
  submit: []
  edit: []
}>()

const data = defineModel<Record<string, unknown>>({ required: true })

const emailList = computed(() => {
  return data.value.ccEmailList ? data.value.ccEmailList.toString().split(',') : []
})

const formContext = useFormKitContextById(`${props.id}-email`)

const addEmail = () => {
  if (!formContext.value || !formContext.value.state.valid || String(data.value.Email).length === 0) return

  if (emailList.value.length) {
    data.value.ccEmailList += ',' + data.value.Email
  }
  else {
    data.value.ccEmailList = data.value.Email
  }
  data.value.Email = ''
}

const removeEmail = (email: string) => {
  data.value.ccEmailList = emailList.value
    .filter(e => e !== email)
    .join(',')
}
</script>

<template>
  <FormKit
    :id="id"
    v-model="data"
    :name="name"
    type="form"
    :actions="false"
    :disabled="readOnly"
    @submit="$emit('submit')"
  >
    <OrganismsFormCard
      :id="`${id}-additional-information`"
      :title="$ts('forms.additionalInformation.title')"
      header-gap="xs"
      :read-only="readOnly"
      @edit="$emit('edit')"
    >
      <div class="space-y-xl">
        <FormKit
          v-if="!readOnly"
          :id="`${id}-email`"
          type="email"
          name="Email"
          :label="$ts('forms.additionalInformation.email.label')"
          :placeholder="$ts('forms.additionalInformation.email.placeholder')"
          validation="email"
          :classes="{
            outer: 'max-w-full',
          }"
          @keydown.enter.stop.prevent="addEmail()"
        />
        <FormKit
          :id="`${id}-email`"
          type="hidden"
          name="ccEmailList"
        />
        <div class="flex flex-col gap-xl max-h-36 overflow-y-auto">
          <p v-if="readOnly && emailList.length" class="body-2 font-bold text-disabled">
            {{ $ts('forms.additionalInformation.email.label') }}
          </p>

          <div v-for="(email, index) in emailList" :key="email" class="flex items-center mr-padding-xxs">
            <span :class="[readOnly ? 'text-neutral -mt-5': 'text-accent', 'link-1 underline w-full']">{{ email }}</span>
            <AtomsButton
              v-if="!readOnly"
              :id="`remove-email-${index}`"
              anatomy="secondary"
              :aria-label="$ts('forms.additionalInformation.email.remove')"
              size="icon"
              @click="removeEmail(email)"
            >
              <template #icon>
                <AtomsIcon name="GenTrashIcon" />
              </template>
            </AtomsButton>
          </div>
        </div>
      </div>
      <FormKit
        v-if="!readOnly || (readOnly && data.Notes)"
        :id="`${id}-notes`"
        type="textarea"
        name="Notes"
        :label="$ts('forms.additionalInformation.comments.label')"
        :placeholder="$ts('forms.additionalInformation.comments.placeholder')"
        outer-class="min-h-60"
      />
    </OrganismsFormCard>
  </FormKit>
</template>
