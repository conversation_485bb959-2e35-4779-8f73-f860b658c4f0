<script lang="ts" setup>
import type { FormItem } from '@forms/types/types'
import type { ProgramCompletionDocumentStoryblok } from '@integration/storyblok/data/components'
import type { AiaFormData, AiaFormStepId } from '@skyfold/types/aiaForm'
import type { StoryblokRichTextNode } from '@storyblok/richtext'
import type { Option } from '~/types/form'

defineProps<{
  participantsDescription?: StoryblokRichTextNode
  programOptions?: Option[]
  programDescription?: StoryblokRichTextNode
  programDocuments?: ProgramCompletionDocumentStoryblok[]
}>()

const {
  formId,
  aiaFormSteps,
  state,
  init,
  updateStepValidity,
  submitStep,
  saveFormData,
  resetConfirmStep,
  reloadForm,
} = useAiaForm()

const steps: Ref<Array<FormItem<AiaFormStepId, AiaFormData>>> = computed(() => Object.values(state))

const overviewComponents: Partial<Record<AiaFormStepId, unknown>> = {
  'dealer-information': resolveComponent('FormsAiaDealerInformation'),
  'program-completion': resolveComponent('FormsAiaProgramCompletion'),
  'participants': resolveComponent('FormsAiaParticipants'),
  'additional-information': resolveComponent('FormsAiaAdditionalInformation'),
}

await init()

const getSubmitButtonState = (activeStep: AiaFormStepId) => {
  if (activeStep === 'confirm') {
    if (state.confirm.data.sending && !state.confirm.isCompleted) return 'loading'
    else return 'default'
  }

  return state[activeStep]?.isValid ? 'default' : 'disabled'
}
</script>

<template>
  <OrganismsStepper :steps="steps" @on-step-change="state['dealer-information'].editMode = false">
    <template
      #dealer-information="{ stepName, goToNextStep }"
    >
      <FormsValidationWrapper
        :form-id="`${formId}-${stepName}`"
        @validity-change="updateStepValidity(stepName, $event)"
      >
        <FormsAiaDealerInformation
          :id="`${formId}-${stepName}`"
          v-model:edit-mode="state[stepName].editMode"
          :initial-data="state[stepName].data"
          :name="stepName"
          @update:data="state[stepName].data = $event"
          @submit="goToNextStep"
        />
      </FormsValidationWrapper>
    </template>
    <template #program-completion="{ stepName, goToNextStep }">
      <FormsValidationWrapper
        :form-id="`${formId}-${stepName}`"
        @validity-change="updateStepValidity(stepName, $event)"
      >
        <FormsAiaProgramCompletion
          :id="`${formId}-${stepName}`"
          v-model="state[stepName].data"
          :name="stepName"
          :options="programOptions"
          :description="programDescription"
          :documents="programDocuments"
          @submit="goToNextStep"
        />
      </FormsValidationWrapper>
    </template>
    <template #participants="{ stepName, goToNextStep }">
      <FormsValidationWrapper
        :form-id="`${formId}-${stepName}`"
        @validity-change="updateStepValidity(stepName, $event)"
      >
        <FormsAiaParticipants
          :id="`${formId}-${stepName}`"
          v-model="state[stepName].data"
          :name="stepName"
          :description="participantsDescription"
          @submit="goToNextStep"
        />
      </FormsValidationWrapper>
    </template>
    <template #additional-information="{ stepName, goToNextStep }">
      <FormsValidationWrapper
        :form-id="`${formId}-${stepName}`"
        @validity-change="updateStepValidity(stepName, $event)"
      >
        <FormsAiaAdditionalInformation
          :id="`${formId}-${stepName}`"
          v-model="state[stepName].data"
          :name="stepName"
          @submit="goToNextStep"
        />
      </FormsValidationWrapper>
    </template>
    <template #confirm="{ goToStepByName }">
      <div class="flex flex-col gap-xxs">
        <template
          v-for="step in aiaFormSteps"
          :key="`${step.id}-review`"
        >
          <FormsAiaDealerInformation
            v-if="step.id === 'dealer-information'"
            :id="`${formId}-${step.id}`"
            v-model:edit-mode="state[step.id].editMode"
            :initial-data="state[step.id].data"
            :name="`${step.id}-review`"
            @update:data="state[step.id].data = $event"
          />
          <component
            :is="overviewComponents[step.id]"
            v-else
            :id="`${formId}-${step.id}-review`"
            :name="`${step.id}-review`"
            :model-value="state[step.id].data"
            :initial-data="state[step.id].data"
            :options="programOptions"
            read-only
            @edit="goToStepByName(step.id)"
          />
        </template>
      </div>
    </template>
    <template #actions="{ activeStep }">
      <div class="contents md:flex md:justify-between">
        <p
          class="caption-1 text-neutral my-4 shrink-0 md:mb-0"
        >
          {{ $ts('forms.infoRequired') }}
        </p>
        <div
          class="sticky mt-auto md:mt-4 max-md:bottom-0 container -mx-(--grid-system-spacing-x) w-[calc(100%+var(--grid-system-spacing-x)*2)] max-md:py-padding-md bg-neutral md:static md:flex md:justify-end md:mx-0 md:px-0 md:pb-0 md:bg-transparent
            md:w-full
            "
        >
          <AtomsButton
            v-if="(activeStep.id === 'confirm' || activeStep.id === 'dealer-information') && state['dealer-information'].editMode"
            type="submit"
            anatomy="primary"
            size="l"
            class="max-md:w-full md:min-w-85"
            @click="saveFormData('dealer-information', String(typeof state['dealer-information'].editMode === 'string' ? state['dealer-information'].editMode : ''))"
          >
            {{ $ts('forms.steps.save') }}
          </AtomsButton>
          <AtomsButton
            v-else
            type="submit"
            anatomy="primary"
            size="l"
            :state="getSubmitButtonState(activeStep.id ?? '')"
            class="max-md:w-full md:min-w-85"
            @click="submitStep(activeStep.id)"
          >
            {{ activeStep.id === 'confirm' ? $ts('forms.steps.confirm') : $ts('forms.steps.next') }}
          </AtomsButton>
        </div>
      </div>
    </template>
  </OrganismsStepper>
  <FormsConfirmationDialog
    v-if="state.confirm"
    v-model="state.confirm.isCompleted"
    :title="state.confirm.data.status === 'success' ? $ts(`forms.confirm.success.title`) : $ts(`forms.confirm.warning.title`)"
    :message="state.confirm.data.message?.toString() ?? ''"
    :status="state.confirm.data.status === 'success' ? 'success' : 'warning'"
  >
    <template #actions>
      <template v-if="state.confirm.data.status === 'success'">
        <AtomsButton
          anatomy="secondary"
          size="m"
          @click="reloadForm"
        >
          {{ $ts('forms.confirm.actions.new') }}
        </AtomsButton>
        <AtomsButton
          anatomy="primary"
          size="m"
          as="link"
          to="/"
        >
          {{ $ts('forms.confirm.actions.back') }}
        </AtomsButton>
      </template>
      <template v-else>
        <AtomsButton
          anatomy="secondary"
          size="m"
          @click="reloadForm"
        >
          {{ $ts('forms.confirm.actions.cancel') }}
        </AtomsButton>
        <AtomsButton
          anatomy="primary"
          size="m"
          @click="resetConfirmStep"
        >
          {{ $ts('forms.confirm.actions.retry') }}
        </AtomsButton>
      </template>
    </template>
  </FormsConfirmationDialog>
</template>
