import type { StoryblokRichTextNode } from '@storyblok/richtext'
import type { ProgramCompletionDocumentStoryblok } from '@integration/storyblok/data/components'
import type { Option } from '~/types/form'

export type FormsAiaProgramCompletionProps = {
  id: string
  name: string
  readOnly?: boolean
  options?: Option[]
  description?: StoryblokRichTextNode
  documents?: ProgramCompletionDocumentStoryblok[]
}
