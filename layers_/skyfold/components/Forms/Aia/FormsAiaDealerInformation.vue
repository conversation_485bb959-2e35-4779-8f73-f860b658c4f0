<script lang="ts" setup>
import type { FormsAiaDealerInformationProps } from './FormsAiaDealerInformation.props'
import OrganismsFormCardGroup from '~~/layers_/design-system/components/Organisms/Form/OrganismsFormCardGroup.vue'

const props = defineProps<FormsAiaDealerInformationProps>()

defineEmits<{
  (e: 'update:data', value: Record<string, unknown>): void
  (e: 'submit'): void
}>()

const data = ref<Record<string, unknown>>({ ...props.initialData })

const editMode = defineModel<boolean | string>('editMode', { default: false })

const isDealerReadOnly = computed(() => !editMode.value || (typeof editMode.value === 'string' && editMode.value !== 'dealer'))
const isSalesEditMode = computed(() => !editMode.value || (typeof editMode.value === 'string' && editMode.value !== 'sales'))
</script>

<template>
  <FormKit
    :id="id"
    :name="name"
    type="form"
    :actions="false"
    @submit="$emit('submit')"
  >
    <OrganismsFormCardGroup
      :id="`${id}-dealer`"
      v-model="data"
      :title="$ts('forms.dealerInformation.dealer.title')"
      header-gap="xs"
      :read-only="isDealerReadOnly"
      :show-edit-button="false"
    >
      <FormKit
        id="name"
        type="text"
        name="Name"
        :label="$ts('forms.dealerInformation.dealer.name.label')"
        :placeholder="$ts('forms.dealerInformation.dealer.name.placeholder')"
        validation="required"
        :label-class="isDealerReadOnly ? 'no-asterisk' : ''"
      />
      <MoleculesPhoneNumber
        id="phoneNumber"
        name="PhoneNumber"
        :prefix="{
          label: $ts('forms.dealerInformation.dealer.prefix.label'),
          placeholder: $ts('forms.dealerInformation.dealer.prefix.placeholder'),
        }"
        :number="{
          label: $ts('forms.dealerInformation.dealer.number.label'),
          placeholder: $ts('forms.dealerInformation.dealer.number.placeholder'),
        }"
        :disabled="isDealerReadOnly"
        validation="required"
      />
      <FormKit
        id="email"
        type="text"
        name="Email"
        :label="$ts('forms.dealerInformation.dealer.email.label')"
        :placeholder="$ts('forms.dealerInformation.dealer.email.placeholder')"
        validation="required|email"
        :label-class="isDealerReadOnly ? 'no-asterisk' : ''"
      />
      <FormKit
        id="address"
        type="text"
        name="Address"
        :label="$ts('forms.dealerInformation.dealer.address.label')"
        :placeholder="$ts('forms.dealerInformation.dealer.address.placeholder')"
        validation="required"
        :label-class="isDealerReadOnly ? 'no-asterisk' : ''"
      />
    </OrganismsFormCardGroup>
    <OrganismsFormCardGroup
      :id="`${id}-sales`"
      v-model="data"
      :title="$ts('forms.dealerInformation.salesperson.title')"
      header-gap="xs"
      :read-only="isSalesEditMode"
      :show-edit-button="false"
    >
      <FormKit
        id="salespersonName"
        type="text"
        name="SalespersonName"
        :label="$ts('forms.dealerInformation.salesperson.name.label')"
        :placeholder="$ts('forms.dealerInformation.salesperson.name.placeholder')"
        validation="required"
        :label-class="isDealerReadOnly ? 'no-asterisk' : ''"
      />
      <MoleculesPhoneNumber
        id="salespersonPhoneNumber"
        name="SalespersonPhoneNumber"
        :prefix="{
          label: $ts('forms.dealerInformation.salesperson.prefix.label'),
          placeholder: $ts('forms.dealerInformation.salesperson.prefix.placeholder'),
        }"
        :number="{
          label: $ts('forms.dealerInformation.salesperson.number.label'),
          placeholder: $ts('forms.dealerInformation.salesperson.number.placeholder'),
        }"
        :disabled="isSalesEditMode"
        validation="required"
      />
      <FormKit
        id="salespersonEmail"
        type="text"
        name="SalespersonEmail"
        :label="$ts('forms.dealerInformation.salesperson.email.label')"
        :placeholder="$ts('forms.dealerInformation.salesperson.email.placeholder')"
        validation="required|email"
        :label-class="isDealerReadOnly ? 'no-asterisk' : ''"
      />
      <FormKit
        id="salespersonAddress"
        type="text"
        name="SalespersonAddress"
        :label="$ts('forms.dealerInformation.salesperson.address.label')"
        :placeholder="$ts('forms.dealerInformation.salesperson.address.placeholder')"
        validation="required"
        :label-class="isDealerReadOnly ? 'no-asterisk' : ''"
      />
    </OrganismsFormCardGroup>
  </FormKit>
</template>
