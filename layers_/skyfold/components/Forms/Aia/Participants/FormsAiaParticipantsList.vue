<script lang="ts" setup>
import type { FormsAiaParticipantsListProps, SortType } from './FormsAiaParticipantsList.props'

const { $ts } = useI18n()

const props = withDefaults(defineProps<FormsAiaParticipantsListProps>(), {
  readOnly: false,
})

defineEmits<{
  edit: [timestamp: number]
  remove: [timestamp: number]
  add: []
}>()

const showAll = ref(false)
const maxDisplayed = 4
const sortBy = ref<SortType>('desc')

const sortOptions = [
  { label: $ts('forms.participants.sortBy.lastAdded'), value: 'desc' as SortType },
  { label: $ts('forms.participants.sortBy.firstAdded'), value: 'asc' as SortType },
]

const sortedParticipants = computed(() => {
  if (sortBy.value === 'asc') {
    return props.participants.toSorted((a, b) => (a.timestamp as number) - (b.timestamp as number))
  }
  else {
    return props.participants.toSorted((a, b) => (b.timestamp as number) - (a.timestamp as number))
  }
})

const displayedParticipants = computed(() => {
  if (showAll.value || sortedParticipants.value.length <= maxDisplayed) {
    return sortedParticipants.value
  }
  return sortedParticipants.value.slice(0, maxDisplayed)
})

const hasMore = computed(() => props.participants.length > maxDisplayed)

const toggleShowAll = () => {
  showAll.value = !showAll.value
}
</script>

<template>
  <div class="space-y-padding-xl">
    <div v-if="!readOnly" class="space-y-padding-xs">
      <h3 class="subtitle-3 font-semibold">
        {{ $ts('forms.participants.title') }}
      </h3>

      <div v-if="participants.length > 1" class="w-full md:w-80 md:ml-auto">
        <FormKit
          type="dropdown"
          :placeholder="$ts('portfolio.filters.sort')"
          :options="sortOptions"
          select-icon="NavChevronDown"
          selected-icon="NavDiamond"
          :classes="{
            selector: 'bg-transparent rounded-none border-0 border-b-1 !border-accent group-data-[expanded]:!border-hover',
            inner: '!shadow-none flex-col',
            selectIcon: '[&>svg]:w-6 [&>svg]:h-6 text-accent group-data-[expanded]:text-hover',
            selectedIcon: 'hidden',
            placeholder: 'section-1 uppercase !text-accent group-data-[expanded]:!text-hover !font-section-sm',
            option: 'text-accent group-hover/item:text-invert',
            listitem: 'group/item !bg-white hover:!bg-hover rounded-xs first:rounded-t-none pl-padding-md py-padding-xs',
            dropdownWrapper: 'border border-hover group-data-[expanded]:shadow-[0_15px_20px_rgba(0,0,0,0.05]',
          }"
          @input="(value?: SortType) => { sortBy = value ?? 'desc' }"
        >
          <template #selection="{ option }">
            <p class="section-1 uppercase text-accent group-data-[expanded]:text-hover h-full">
              <span>
                {{ $ts('portfolio.filters.sort') }} {{ option.label }}
              </span>
            </p>
          </template>
        </FormKit>
      </div>
    </div>

    <div v-if="participants.length > 0" class="space-y-4">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <OrganismsRecapCard
          v-for="(participant, index) in displayedParticipants"
          :key="index"
          :title="participant.fullName as string"
          icon="GenPerson"
          :list="[
            { label: $ts('forms.participants.partecipants_card.aiaMember'), value: participant.isAiaMember ? $ts('forms.participants.partecipants_card.yes') : $ts('forms.participants.partecipants_card.no') },
            { label: $ts('forms.participants.partecipants_card.certificateRequest'), value: participant.certificateRequest ? $ts('forms.participants.partecipants_card.yes') : $ts('forms.participants.partecipants_card.no') },
            ...(participant.isAiaMember ? [{ label: $ts('forms.participants.partecipants_card.membershipNumber'), value: participant.membershipNumber as string }] : []),
            { label: $ts('forms.participants.partecipants_card.email'), value: participant.email as string },
          ]"
        >
          <template #actions>
            <AtomsButton
              v-if="!readOnly"
              anatomy="secondary"
              size="icon"
              :on-click="() => $emit('remove', participant.timestamp as number)"
              type="button"
              :aria-label="$ts('forms.participants.partecipants_card.remove')"
            >
              <template #icon>
                <AtomsIcon name="GenTrashIcon" />
              </template>
            </AtomsButton>
            <AtomsButton
              v-if="!readOnly"
              anatomy="secondary"
              size="m"
              type="button"
              :aria-label="$ts('forms.participants.partecipants_card.edit')"
              :on-click="() => $emit('edit', participant.timestamp as number)"
            >
              <template #icon>
                <AtomsIcon name="GenEditIcon" />
              </template>
              {{ $ts('forms.participants.partecipants_card.edit') }}
            </AtomsButton>
          </template>
        </OrganismsRecapCard>
      </div>

      <div v-if="hasMore" class="flex justify-end">
        <AtomsLink
          anatomy="secondary"
          size="m"
          as="button"
          @click="toggleShowAll"
        >
          {{ showAll ? $ts('forms.participants.showLess') : $ts('forms.participants.showAll', { count: participants.length }) }}
        </AtomsLink>
      </div>
    </div>

    <AtomsLink
      v-if="!readOnly"
      anatomy="primary"
      size="m"
      as="button"
      @click="$emit('add')"
    >
      <AtomsIcon name="NavPlusIcon" />
      <span>
        {{ $ts('forms.participants.addParticipant') }}
      </span>
    </AtomsLink>
  </div>
</template>
