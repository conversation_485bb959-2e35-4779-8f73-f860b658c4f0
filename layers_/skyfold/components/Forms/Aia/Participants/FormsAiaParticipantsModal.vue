<script lang="ts" setup>
import type { FormsAiaPartecipantsModalProps } from './FormsAiaParticipantsModal.props'

withDefaults(defineProps<FormsAiaPartecipantsModalProps>(), {
  isEditing: false,
})

const emit = defineEmits<{
  save: []
}>()

const isOpen = defineModel<boolean>('open', { default: false })
const participant = defineModel<Record<string, unknown>>('participant', { required: true })

const handleFormSubmit = () => {
  emit('save')
}
</script>

<template>
  <MoleculesDialog
    v-model="isOpen"
    :title="isEditing ? $ts('forms.participants.addPartecipantModal.editTitle') : $ts('forms.participants.addPartecipantModal.title')"
    :with-close-button="true"
    content-class="w-full max-w-4xl max-md:h-full md:max-h-[90vh] overflow-y-auto gap-0!"
    inner-content-class="md:p-8! gap-xxs!"
  >
    <FormKit
      v-model="participant"
      type="form"
      :actions="false"
      form-class="grid grid-cols-1 md:grid-cols-2 gap-xl"
      @submit="handleFormSubmit"
    >
      <div class="space-y-padding-xs">
        <h4 class="body-2 font-bold">
          {{ $ts('forms.participants.addPartecipantModal.aiaMember') }}
        </h4>
        <FormKit
          type="toggle"
          name="isAiaMember"
          :label="$ts('forms.participants.addPartecipantModal.yes')"
        />
      </div>

      <div class="space-y-padding-xs">
        <h3 class="body-2 font-bold">
          {{ $ts('forms.participants.addPartecipantModal.certificateRequest') }}
        </h3>
        <FormKit
          type="toggle"
          name="certificateRequest"
          :label="$ts('forms.participants.addPartecipantModal.yes')"
        />
      </div>

      <FormKit
        type="text"
        name="membershipNumber"
        :placeholder="$ts('forms.participants.addPartecipantModal.membershipNumberPlaceholder')"
        :label="$ts('forms.participants.addPartecipantModal.membershipNumber')"
      />

      <FormKit
        type="text"
        name="fullName"
        :placeholder="$ts('forms.participants.addPartecipantModal.fullNamePlaceholder')"
        :label="$ts('forms.participants.addPartecipantModal.fullName')"
        validation="required"
      />

      <FormKit
        type="email"
        name="email"
        :placeholder="$ts('forms.participants.addPartecipantModal.emailPlaceholder')"
        :label="$ts('forms.participants.addPartecipantModal.email')"
        validation="email"
        :classes="{
          outer: 'md:col-span-2 max-w-none! max-md:mb-0',
        }"
      />

      <div class="md:col-span-2 flex justify-center">
        <AtomsButton
          anatomy="primary"
          class="w-full md:w-auto md:min-w-80"
          :state="(participant.fullName as string)?.trim() ? 'default' : 'disabled'"
        >
          {{ isEditing ? $ts('forms.participants.addPartecipantModal.update') : $ts('forms.participants.addPartecipantModal.save') }}
        </AtomsButton>
      </div>
    </FormKit>
  </MoleculesDialog>
</template>
