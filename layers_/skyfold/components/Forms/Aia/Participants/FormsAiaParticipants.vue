<script lang="ts" setup>
import type { FormsAiaParticipantsProps } from './FormsAiaParticipants.props'

withDefaults(defineProps<FormsAiaParticipantsProps>(), {
  readOnly: false,
  content: undefined,
})

defineEmits<{
  submit: []
  edit: []
}>()

const data = defineModel<Record<string, unknown>>({ required: true })
const { isModalOpen, participant, isEditing, openModal, saveParticipant, removeParticipant, editParticipant } = usePartecipantsModal(data)

const participants = computed(() => (data.value.participants as Record<string, unknown>[]) || [])
const hasAtLeastAParticipant = computed(() => participants.value.length > 0)

const uploadedFile = computed({
  get: () => data.value.uploadedFile as File | null,
  set: (value: File | null) => {
    data.value.uploadedFile = value
  },
})

const handleFileError = (error: string) => {
  console.error('File upload error:', error)
}
</script>

<template>
  <FormKit
    :id="id"
    v-model="data"
    :name="name"
    type="form"
    :actions="false"
    @submit="$emit('submit')"
  >
    <OrganismsFormCard
      :id="`${id}-participants`"
      :title="!readOnly ? undefined : $ts('forms.participants.title')"
      header-gap="xs"
      :read-only="readOnly"
      @edit="$emit('edit')"
    >
      <div :class="['col-span-2', readOnly ? 'space-y-2' : 'space-y-6']">
        <div v-if="!readOnly">
          <UtilsRichTextContent v-if="description" :content="description" />
        </div>

        <OrganismsFileUpload
          v-if="(!readOnly || uploadedFile)"
          v-model="uploadedFile"
          :accept="['text/csv']"
          :max-size="10485760"
          :disabled="readOnly"
          :read-only="readOnly"
          @error="handleFileError"
        />

        <div>
          <FormsAiaParticipantsList
            :participants="participants"
            :read-only="readOnly"
            @remove="removeParticipant"
            @edit="editParticipant"
            @add="openModal"
          />
        </div>
      </div>
      <FormKit
        type="hidden"
        name="_participants"
        :model-value="hasAtLeastAParticipant.toString()"
        validation="required|matches:true"
      />
    </OrganismsFormCard>
  </FormKit>

  <FormsAiaParticipantsModal
    v-model:open="isModalOpen"
    v-model:participant="participant"
    :is-editing="isEditing"
    @save="saveParticipant"
  />
</template>
