<script lang="ts" setup>
import type { FormsAiaProgramCompletionProps } from './FormsAiaProgramCompletition.props'

defineProps<FormsAiaProgramCompletionProps>()

defineEmits<{
  submit: []
  edit: []
}>()

const { ts } = useI18n()

const data = defineModel<Record<string, unknown>>({ required: true })

const sendToOptions = [
  {
    label: ts('forms.programCompletion.provided.sendTo.options.dealer'),
    value: 'dealer',
  },
  {
    label: ts('forms.programCompletion.provided.sendTo.options.rsm'),
    value: 'rsm',
  },
]

const courseGivenOptions = [
  {
    label: ts('forms.programCompletion.provided.courseGiven.options.inPerson'),
    value: 'in person',
  },
  {
    label: ts('forms.programCompletion.provided.courseGiven.options.online'),
    value: 'online',
  },
]
</script>

<template>
  <FormKit
    :id="id"
    v-model="data"
    :name="name"
    type="form"
    :actions="false"
    :disabled="readOnly"
    @submit="$emit('submit')"
  >
    <OrganismsFormCard
      :id="`${id}-program-provided`"
      header-gap="xl"
      :read-only="readOnly"
      :title="readOnly ? $ts('forms.programCompletion.programProvided') : undefined"
      @edit="$emit('edit')"
    >
      <template v-if="!readOnly" #header>
        <UtilsRichTextContent v-if="description" :content="description" />
      </template>
      <template #default>
        <FormKit
          id="program-provided-options"
          type="radio"
          name="ProgramProvided"
          :value="options?.length ? options[0]?.value : undefined"
          :label="!readOnly ? $ts('forms.programCompletion.programProvided') : ''"
          validation="required"
          :classes="{
            legend: 'text-subtitle-sm font-semibold',
          }"
          :options="options ?? []"
        />
      </template>
    </OrganismsFormCard>

    <OrganismsFormCard
      :id="`${id}-provided-by`"
      :title="$ts('forms.programCompletion.provided.title')"
      header-gap="xl"
      :read-only="readOnly"
      @edit="$emit('edit')"
    >
      <FormKit
        id="conductor-name"
        type="text"
        name="ConductorName"
        :label="$ts('forms.programCompletion.provided.conductorName')"
        :placeholder="$ts('forms.programCompletion.provided.placeholderConductorName')"
        validation="required"
      />
      <FormKit
        id="send-to"
        name="SendTo"
        type="checkbox"
        :label="$ts('forms.programCompletion.provided.sendTo.label')"
        decorator-icon="myCheckbox"
        :options="sendToOptions"
        validation="required"
        :validation-messages="{
          required: $ts('forms.programCompletion.provided.sendTo.requiredMessage'),
        }"
      />
      <FormKit
        id="architectural-firm"
        type="text"
        name="ArchitectFirm"
        :label="$ts('forms.programCompletion.provided.firmName')"
        :placeholder="$ts('forms.programCompletion.provided.placeholderFirmName')"
        validation="required"
      />
      <FormKit
        id="street"
        type="text"
        name="Street"
        :label="$ts('forms.programCompletion.provided.street')"
        :placeholder="$ts('forms.programCompletion.provided.placeholderStreet')"
        validation="required"
      />
      <FormKit
        id="city"
        type="text"
        name="City"
        :label="$ts('forms.programCompletion.provided.city')"
        :placeholder="$ts('forms.programCompletion.provided.placeholderCity')"
        validation="required"
      />
      <FormKit
        id="state-prov"
        type="text"
        name="StateProv"
        :label="$ts('forms.programCompletion.provided.stateProvince')"
        :placeholder="$ts('forms.programCompletion.provided.placeholderStateProvince')"
        validation="required"
      />
      <FormKit
        id="completion-date"
        name="CompletionDate"
        type="datepicker"
        :label="$ts('forms.programCompletion.provided.date')"
        format="YYYY/MM/DD"
        validation="required"
        required
      />
      <FormKit
        id="course-given"
        type="radio"
        name="CourseGiven"
        :label="!readOnly ? $ts('forms.programCompletion.provided.courseGiven.label') : ''"
        validation="required"
        :value="courseGivenOptions[0]?.value"
        :options="courseGivenOptions"
      />
    </OrganismsFormCard>

    <OrganismsFormCard
      v-if="documents?.length && !readOnly"
      :id="`${id}-documents`"
      :title="$ts('forms.programCompletion.documents.title')"
      header-gap="xl"
    >
      <div class="col-span-2 grid md:grid-cols-2 gap-xl">
        <div v-for="doc in documents" :key="doc._uid">
          <MoleculesButtonAsset
            v-if="doc.asset.filename"
            :title="doc.title"
            :file-url="doc.asset.filename"
            :file-type="iconFileType(doc.asset.filename)"
            :file-name="(doc.asset.meta_data?.title || doc.asset.title || doc.asset.filename?.split('/').pop() || '') as string"
            :file-date="doc.date"
          />
        </div>
      </div>
    </OrganismsFormCard>
  </FormKit>
</template>
