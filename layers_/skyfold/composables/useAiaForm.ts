import type { FormItem } from '@forms/types/types'
import type { AiaFormData, AiaFormStepId } from '@skyfold/types/aiaForm'
import { getNode, reset } from '@formkit/core'
import type { AIAProgramCompletionSchema } from '@skyfold/server/api/aia/programCompletion.post'
import { parsePhoneNumberWithError } from 'libphonenumber-js'

export const useAiaForm = () => {
  const { $ts } = useI18n()
  const { confirmGuardNavigation } = useNavigationGuard()
  const { userData, userMetadata } = useAuth0User()

  const formId = 'aia-form'

  const state = reactive<Record<AiaFormStepId, FormItem<AiaFormStepId, AiaFormData>>>({
    'dealer-information': {
      id: 'dealer-information',
      title: 'Dealer Information',
      data: {},
      isCompleted: false,
      isValid: false,
    },
    'program-completion': {
      id: 'program-completion',
      title: 'AIA/CES Program Completion',
      data: {},
      isCompleted: false,
      isValid: false,
    },
    'participants': {
      id: 'participants',
      title: 'Participants',
      data: {},
      isCompleted: false,
      isValid: false,
    },
    'additional-information': {
      id: 'additional-information',
      title: 'Additional Information',
      data: {},
      isCompleted: false,
      isValid: false,
    },
    'confirm': {
      id: 'confirm',
      title: 'Confirm',
      data: {},
      isValid: false,
    },
  })

  const aiaFormSteps = computed(() => Object.values(state).filter(step => step.id !== 'confirm'))

  async function init() {
    await fillDealerInformationWithDefault()
  }

  async function sendData() {
    state.confirm.data.sending = true
    try {
      await submitData(state)
      state.confirm.data.status = 'success'
      state.confirm.data.message = $ts('forms.confirm.success.message')
      confirmGuardNavigation(true)
    }
    catch (error) {
      state.confirm.data.status = 'warning'
      // @ts-expect-error error is unknown
      state.confirm.data.message = error?.data?.message ?? $ts('forms.confirm.warning.message')
    }
    finally {
      state.confirm.isCompleted = true
      state.confirm.data.sending = false
    }
  }

  function submitStep(stepId: AiaFormStepId) {
    state.confirm.isCompleted = false
    if (stepId === 'confirm' && state.confirm?.isValid) {
      sendData()
      return
    }

    const _id = `${formId}-${stepId}`

    const formkitNode = getNode(_id)

    if (!formkitNode || !formkitNode?.context) return

    state[stepId].isCompleted = formkitNode?.context?.state.valid
    submitForm(_id)
  }

  function updateStepValidity(stepId: AiaFormStepId, valid: boolean) {
    state[stepId].isValid = valid

    if (!valid) {
      state[stepId].isCompleted = valid
      if (state.confirm) state.confirm.isCompleted = false
    }
    else if (state.confirm) {
      state.confirm.isValid = Object.values(state)
        .filter(step => step.id !== 'confirm')
        .every(step => step.isValid)
    }
  }

  function saveFormData(stepId: AiaFormStepId, grupId?: string) {
    let id = `${formId}-${stepId}`

    if (typeof grupId === 'string') id = `${id}-${grupId}`

    const node = getNode(id)

    if (!node) {
      console.error(`Node ${id} not found`)
      return
    }

    if (node.context?.state.valid) {
      state[stepId].data = node.value as AiaFormData
      state[stepId].editMode = false
    }
    else {
      console.error(`Invalid form ${id}, cannot save data`)
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const submitData = async (state: any) => {
    const dealerInfo = state['dealer-information']?.data ?? {}
    const programCompletion = state['program-completion']?.data ?? {}
    const participants = state['participants']?.data ?? {}
    const addintionalInfo = state['additional-information']?.data ?? {}

    const sendTo = programCompletion?.SendTo ?? []
    const sendCertificateToDealer = sendTo.includes('dealer')

    const body: AIAProgramCompletionSchema = {
      programProvided: programCompletion.ProgramProvided ?? '',
      submittedBy: dealerInfo.Name ?? '',
      emailCurrentUser: dealerInfo.Email ?? '',
      dealer: dealerInfo.Name ?? '',
      ccTo: addintionalInfo.ccEmailList,
      conductorName: programCompletion.ConductorName ?? '',
      sendCertificateToDealer,
      architectFirm: programCompletion.ArchitectFirm ?? '',
      street: programCompletion.Street ?? '',
      city: programCompletion.City ?? '',
      state: programCompletion.StateProv ?? '',
      completed: programCompletion.CompletionDate
        ? new Date(programCompletion.CompletionDate).toISOString()
        : '',
      courseGiven: programCompletion.CourseGiven ?? '',
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      partecipants: participants.participants?.map((p: any) => ({
        aiaMember: !!p.isAiaMember,
        aiaNumber: p.membershipNumber,
        participant: p.fullName,
        certRequest: !!p.certificateRequest,
        participantEmail: p.email,
      })) ?? [],
      file: participants?.uploadedFile
        ? {
            fileBase64: await fileToBase64(participants.uploadedFile),
            filename: participants.uploadedFile.name,
            fileMimeType: participants.uploadedFile.type,
          }
        : undefined,
    }

    const response = await $fetch('/api/aia/programCompletion', {
      method: 'POST',
      body,
    })

    console.log(state, body)

    return response
  }

  async function fillDealerInformationWithDefault() {
    if (!userData.value || !userMetadata.value) {
      console.error('Failed to fill dealer information with default')
      return
    }

    const { email, name } = userData.value
    const { address } = userMetadata.value
    const _phoneNumber = { prefix: '', number: '' }
    let _salespersonName = ''
    let _salespersonEmail = ''
    let _salespersonAddress = ''
    const _salespersonPhoneNumber = { prefix: '', number: '' }

    try {
      const phoneNumber = parsePhoneNumberWithError(userMetadata.value.phone_number ?? '')

      _phoneNumber.prefix = phoneNumber.countryCallingCode.toString()
      _phoneNumber.number = phoneNumber.nationalNumber.toString()
    }
    catch (error) {
      console.error('Failed to parse phone number', error)
    }

    if (userMetadata.value?.salesperson_id) {
      const salesperson = await $fetch(`/api/auth0/users/${userMetadata.value.salesperson_id}`)

      _salespersonName = salesperson.name
      _salespersonEmail = salesperson.email
      _salespersonAddress = formatAddress(salesperson.user_metadata?.address)
      try {
        const _salespersonphoneNumber = parsePhoneNumberWithError(salesperson.user_metadata.phone_number ?? '')

        _salespersonPhoneNumber.prefix = _salespersonphoneNumber.countryCallingCode.toString()
        _salespersonPhoneNumber.number = _salespersonphoneNumber.nationalNumber.toString()
      }
      catch (error) {
        console.error('Failed to parse phone number', error)
      }
    }

    state['dealer-information'].data = {
      Name: name,
      Email: email,
      Address: formatAddress(address),
      PhoneNumber: _phoneNumber,
      SalespersonName: _salespersonName,
      SalespersonEmail: _salespersonEmail,
      SalespersonAddress: _salespersonAddress,
      SalespersonPhoneNumber: _salespersonPhoneNumber,
    }
  }

  function resetConfirmStatus() {
    state.confirm.data.status = ''
    state.confirm.data.message = ''
  }

  function resetConfirmStep(invalid?: boolean) {
    if (typeof invalid === 'boolean') state.confirm.isValid = !invalid
    state.confirm.isCompleted = false
    resetConfirmStatus()
  }

  async function resetData() {
    for (const step of aiaFormSteps.value) {
      if (step.id === 'dealer-information') {
        state['dealer-information'].isCompleted = false
        continue
      }

      const _id = `${formId}-${step.id}`
      step.isCompleted = false
      step.isValid = false
      reset(_id)
    }

    resetConfirmStep(true)
    await init()
  }

  function reloadForm() {
    window.location.reload()
  }

  watch(() => state.confirm.isCompleted, (value) => {
    if (!value) resetConfirmStatus()
  })

  watch(() => state['dealer-information'].editMode, (value) => {
    if (value) {
      state['dealer-information'].isValid = false
    }
  })

  return {
    formId,
    state,
    aiaFormSteps,
    init,
    submitStep,
    updateStepValidity,
    saveFormData,
    resetConfirmStep,
    resetData,
    reloadForm,
  }
}
