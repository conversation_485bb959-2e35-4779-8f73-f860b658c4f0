export const usePartecipantsModal = (data: Ref<Record<string, unknown>>) => {
  if (!data.value.participants) {
    data.value.participants = []
  }

  const isModalOpen = ref(false)

  const participant = ref<Record<string, unknown>>({
    isAiaMember: false,
    certificateRequest: false,
    membershipNumber: '',
    fullName: '',
    email: '',
  })

  const editingIndex = ref<number | null>(null)
  const isEditing = computed(() => editingIndex.value !== null)

  const openModal = (participantToEdit?: Record<string, unknown>) => {
    if (participantToEdit) {
      const participants = data.value.participants as Record<string, unknown>[]
      const index = participants.findIndex(p => p.timestamp === participantToEdit.timestamp)
      editingIndex.value = index
      participant.value = {
        isAiaMember: participantToEdit.isAiaMember,
        certificateRequest: participantToEdit.certificateRequest,
        membershipNumber: participantToEdit.membershipNumber,
        fullName: participantToEdit.fullName,
        email: participantToEdit.email,
      }
    }
    else {
      editingIndex.value = null
      participant.value = {
        isAiaMember: false,
        certificateRequest: false,
        membershipNumber: '',
        fullName: '',
        email: '',
      }
    }
    isModalOpen.value = true
  }

  const saveParticipant = () => {
    const participants = data.value.participants as Record<string, unknown>[]

    if (isEditing.value && editingIndex.value !== null) {
      const existingParticipant = participants[editingIndex.value]
      if (existingParticipant) {
        participants[editingIndex.value] = {
          ...participant.value,
          timestamp: existingParticipant.timestamp,
        }
      }
    }
    else {
      const newParticipant: Record<string, unknown> = {
        ...participant.value,
        timestamp: Date.now(),
      }
      participants.push(newParticipant)
    }

    isModalOpen.value = false
  }

  const removeParticipant = (timestamp: number) => {
    const participants = data.value.participants as Record<string, unknown>[]
    data.value.participants = participants.filter(p => p.timestamp !== timestamp)
  }

  const editParticipant = (timestamp: number) => {
    const participants = data.value.participants as Record<string, unknown>[]
    const participantToEdit = participants.find(p => p.timestamp === timestamp)
    if (participantToEdit) {
      openModal(participantToEdit)
    }
  }

  return {
    isModalOpen,
    participant,
    isEditing,
    openModal,
    saveParticipant,
    removeParticipant,
    editParticipant,
  }
}
