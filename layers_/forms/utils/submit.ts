import type { FormKitNode } from '@formkit/core'
import type { AcoustiSealCalculatorForm } from '@modernfold/server/types/comCalculator'

export const handleFormkitSubmit = <Data> (
  parseFormData: (formData: AcoustiSealCalculatorForm) => Data,
  submit: (data: Data) => void | Promise<void>,
  mapError: (err: unknown) => Parameters<FormKitNode['setErrors']>,
): (formData: AcoustiSealCalculatorForm, node?: FormKitNode) => Promise<void> => {
  return async (formData, node) => {
    try {
      node?.clearErrors()
      const data = parseFormData(formData)
      await submit(data)
    }
    catch (err) {
      const errors = mapError(err)
      node?.setErrors(...errors)
    }
  }
}
