import type { FormKitPlugin } from '@formkit/core'
import { undefine } from '@formkit/utils'

const validationIconPlugin: FormKitPlugin = (node) => {
  const appliableInputs = ['text', 'mask', 'tel']

  if (!appliableInputs.includes(node.context?.type ?? '')) return

  node.addProps({
    showValidationIcon: {
      default: false,
    },
  })

  const useValidationIcon = undefine(node.props.showValidationIcon)
    || node.props.showValidationIcon === 'true'
    || node.props.showValidationIcon === true

  if (useValidationIcon) {
    node.on('created', () => {
      if (node.props.definition?.schema) {
        const originalSchema = node.props.definition.schema
        // Ensure originalSchema is a function before proceeding
        if (typeof originalSchema !== 'function') return

        node.props.definition.schema = (sectionsSchema = {}) => {
          const extendedSchema = {
            ...sectionsSchema,
            suffixIcon: {
              if: '$showValidationIcon',
              attrs: {
                class: [
                  'inline-block bg-norepeat bg-center w-5 h-5',
                  `text-alert group-data-[invalid]:bg-[url(@design-system/assets/icons/GenAttentionIconError.svg)]`,
                  `text-accent group-data-[complete]:not-group-data-[invalid]:not-group-data-[empty]:not-peer-focus:bg-[url(@design-system/assets/icons/NavCheckIconSuccess.svg)]`,
                ],
              },
            },
          }

          return originalSchema(extendedSchema)
        }

        if (node.props.definition.schemaMemoKey) {
          node.props.definition.schemaMemoKey += '-validation-icon'
        }
      }
    })
  }
}

export default validationIconPlugin
