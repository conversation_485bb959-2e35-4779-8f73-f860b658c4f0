import { defineFormKitConfig } from '@formkit/vue'
import { createProPlugin, dropdown, toggle, datepicker, mask, autocomplete } from '@formkit/pro'
import { genesisIcons } from '@formkit/icons'
import NavChevronDown from '@design-system/assets/icons/NavChevronDown.svg?raw'
import NavCheckIcon from '@design-system/assets/icons/NavCheckIcon.svg?raw'
import NavDiamond from '@design-system/assets/icons/NavDiamond.svg?raw'
import NavSearch from '@design-system/assets/icons/NavSearch.svg?raw'
import NavCloseIcon from '@design-system/assets/icons/NavCloseIcon.svg?raw'
import { rootClasses } from './formkit.theme'
import validationIconPlugin from './fkPlugins/validationIcon'

const myCheckbox = `<svg xmlns="http://www.w3.org/2000/svg" width="14" height="10" viewBox="0 0 14 10" fill="none"><path d="M5.12117 9.84663L0.75 5.47546L1.84279 4.38267L5.12117 7.66104L12.1572 0.625L13.25 1.71779L5.12117 9.84663Z" fill="currentcolor"/></svg>`

export default defineFormKitConfig(() => {
  // env issue:
  // https://github.com/formkit/formkit/issues/1593
  // const config = useRuntimeConfig()

  const proPlugin = createProPlugin('fk-46a6e7941f5', {
    toggle,
    dropdown,
    datepicker,
    mask,
    autocomplete,
  })

  return {
    config: {
      rootClasses,
    },
    icons: {
      ...genesisIcons,
      myCheckbox,
      NavChevronDown,
      NavCheckIcon,
      NavDiamond,
      NavSearch,
      NavCloseIcon,
    },
    plugins: [validationIconPlugin, proPlugin],
  }
})
