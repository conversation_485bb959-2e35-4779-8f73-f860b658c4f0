<script lang="ts" setup>
/** this component is responsible for synchronizing form steps validity to the FormKitContext
* so any time the original Form validity state changes, it updates the validity state of the current form step.
* It helps primarily to display the state in the UI as disabled/active button.
*/

const props = defineProps<{
  formId: string
}>()
const emit = defineEmits<{ (e: 'validityChange', isValid: boolean): void }>()

const formContext = useFormKitContextById(props.formId)

watch(() => formContext.value?.state.valid, (isValid) => {
  if (typeof isValid === 'undefined') return
  emit('validityChange', isValid)
})
</script>

<template>
  <slot />
</template>
