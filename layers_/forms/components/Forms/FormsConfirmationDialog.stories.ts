import type { StoryObj } from '@storybook/vue3'
import { ref } from 'vue' // For controlling dialog state in render functions
import AtomsButton from '@design-system/components/Atoms/AtomsButton.vue'
import FormsConfirmationDialog from './FormsConfirmationDialog.vue'

// Define metadata for the component
const meta = {
  title: 'Organisms/FormConfirmationDialog',
  component: FormsConfirmationDialog,
  // tags: ['autodocs'], // Consider adding autodocs if appropriate
  parameters: {
    docs: {
      description: {
        component:
          'A specialized dialog for confirming form submissions or critical actions. It builds upon a foundational dialog component (`MoleculesDialog`) to provide specific UIs for success/warning states. Emphasizes clear user feedback and follows accessibility best practices for dialogs, ensuring focus management and appropriate ARIA attributes.',
      },
    },
  },
  argTypes: {
    modelValue: {
      control: 'boolean',
      description: 'Controls the visibility of the dialog (v-model). Key for programmatic opening/closing.',
      table: { category: 'Props' },
    },
    title: {
      control: 'text',
      description: 'The main title of the confirmation dialog, providing a succinct summary of the action or result.',
      table: { category: 'Props' },
    },
    message: {
      control: 'text',
      description: 'The detailed message content, offering more context or information about the confirmation.',
      table: { category: 'Props' },
    },
    status: {
      control: 'select',
      options: ['success', 'warning'],
      description: 'Determines the visual style and icon of the dialog (e.g., success for positive confirmations, warning for cautionary messages).',
      table: { category: 'Props' },
    },
    // Slots
    actions: {
      description: 'A dedicated slot for action buttons, allowing custom confirmation and cancellation logic. Typically contains `AtomsButton` components.',
      table: { category: 'Slots' },
    },
  },
  args: {
    modelValue: false, // Default to closed for most stories, triggered by a button
    title: 'Default Confirmation Title',
    message: 'This is a default message for the confirmation dialog.',
    status: 'success',
  },
}

export default meta
type Story = StoryObj<typeof meta>

// Base story showing a success state, triggered by a button
export const Success: Story = {
  args: {
    title: 'Submission Successful',
    message: 'Your form has been submitted successfully and is being processed.',
    status: 'success',
  },
  render: args => ({
    components: { FormsConfirmationDialog, AtomsButton },
    setup() {
      const isOpen = ref(args.modelValue)
      const openDialog = () => isOpen.value = true
      return { args, isOpen, openDialog }
    },
    template: `
      <div>
        <AtomsButton @click="openDialog">Show Success Dialog</AtomsButton>
        <FormsConfirmationDialog v-model="isOpen" :title="args.title" :message="args.message" :status="args.status">
          <template #actions>
            <div class="flex gap-sm w-full">
              <AtomsButton anatomy="primary" @click="() => isOpen = false" class="flex-1">OK</AtomsButton>
            </div>
          </template>
        </FormsConfirmationDialog>
      </div>
    `,
  }),
}

export const Warning: Story = {
  args: {
    modelValue: false, // Start closed
    title: 'Are you sure?',
    message: 'Proceeding with this action cannot be undone. Please confirm you wish to continue.',
    status: 'warning',
  },
  render: args => ({
    components: { FormsConfirmationDialog, AtomsButton },
    setup() {
      const isOpen = ref(args.modelValue)
      const openDialog = () => isOpen.value = true
      const handleConfirm = () => {
        console.log('Confirmed warning action!')
        isOpen.value = false
      }
      const handleCancel = () => {
        console.log('Cancelled warning action.')
        isOpen.value = false
      }
      return { args, isOpen, openDialog, handleConfirm, handleCancel }
    },
    template: `
      <div>
        <AtomsButton @click="openDialog" anatomy="primary">Show Warning Dialog</AtomsButton>
        <FormsConfirmationDialog v-model="isOpen" :title="args.title" :message="args.message" :status="args.status">
          <template #actions>
            <div class="flex gap-sm w-full">
              <AtomsButton anatomy="secondary" @click="handleCancel" class="flex-1">Cancel</AtomsButton>
              <AtomsButton anatomy="primary" @click="handleConfirm" class="flex-1">Confirm Action</AtomsButton>
            </div>
          </template>
        </FormsConfirmationDialog>
      </div>
    `,
  }),
}

export const InitiallyOpenSuccess: Story = {
  args: {
    modelValue: true, // Story starts with dialog open
    title: 'Action Completed',
    message: 'This dialog was opened programmatically on load.',
    status: 'success',
  },
  // No render function needed if we just want to show it open with default actions slot behavior
  // If specific actions are needed for an initially open story, add a render function similar to others.
}

// This story replaces the old 'WithActions' one with clearer context
export const CustomActionsWarning: Story = {
  args: {
    modelValue: false, // Start closed
    title: 'Delete Item?',
    message: 'This item will be permanently deleted. This action is irreversible.',
    status: 'warning',
  },
  render: args => ({
    components: { FormsConfirmationDialog, AtomsButton },
    setup() {
      const isOpen = ref(args.modelValue)
      const openDialog = () => isOpen.value = true
      const handleDelete = () => {
        console.log('Item Deletion Confirmed')
        isOpen.value = false
      }
      const keepItem = () => {
        console.log('Item Deletion Cancelled')
        isOpen.value = false
      }
      return { args, isOpen, openDialog, handleDelete, keepItem }
    },
    template: `
      <div>
        <AtomsButton @click="openDialog" anatomy="primary">Delete Item (Show Dialog)</AtomsButton>
        <FormsConfirmationDialog v-model="isOpen" :title="args.title" :message="args.message" :status="args.status">
          <template #actions>
            <div class="flex gap-sm w-full">
              <AtomsButton anatomy="secondary" @click="keepItem" class="flex-1">Keep Item</AtomsButton>
              <AtomsButton anatomy="primary" @click="handleDelete" class="flex-1">Delete Permanently</AtomsButton>
            </div>
          </template>
        </FormsConfirmationDialog>
      </div>
    `,
  }),
}
