<script lang="ts" setup>
import type { FormsConfirmationDialogProps } from './FormsConfirmationDialog.props'

const props = withDefaults(defineProps<FormsConfirmationDialogProps>(), {
  status: 'success',
})
const open = defineModel<boolean>()

const iconName = computed(() => {
  switch (props.status) {
    case 'warning':
      return 'GenAttentionIcon'
    case 'info':
      return 'GenHelpIcon'
    default:
      return 'NavCheckIcon'
  }
})

const iconClass = computed(() => {
  switch (props.status) {
    case 'warning':
      return 'text-warning'
    case 'success':
      return 'text-success'
    default:
      return ''
  }
})
</script>

<template>
  <MoleculesDialog
    v-model="open"
    :title="title"
    :message="message"
    :icon="iconName"
    :icon-class="iconClass"
    content-class="w-[calc(100%-var(--grid-system-spacing-x)*2)] max-w-88"
  >
    <template v-if="$slots.actions" #actions>
      <div class="flex *:flex-1 gap-xs">
        <slot name="actions" />
      </div>
    </template>
  </MoleculesDialog>
</template>
