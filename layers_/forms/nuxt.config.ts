// https://nuxt.com/docs/api/configuration/nuxt-config
import { fileURLToPath } from 'url'
import { env } from '../../env'

export default defineNuxtConfig({
  extends: ['../design-system'],
  modules: ['@formkit/nuxt'],
  runtimeConfig: {
    formkitProKey: env.FORMKIT_PRO_KEY,
  },
  alias: {
    '@forms': fileURLToPath(new URL('./', import.meta.url)),
    '@design-system': fileURLToPath(
      new URL('../design-system', import.meta.url),
    ),
  },
  formkit: {
    autoImport: true,
    configFile: fileURLToPath(new URL('./formkit.config.ts', import.meta.url)),
  },
})
