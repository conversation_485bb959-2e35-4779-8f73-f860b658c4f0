import type { FormKitNode } from '@formkit/core'

/**
 * This is the theme function itself, it should be imported and used as the
 * config.rootClasses function. For example:
*
* ```js
* import { theme } from './formkit.theme'
* import { defineFormKitConfig } from '@formkit/vue'
*
* export default defineFormKitConfig({
*   config: {
*     rootClasses: theme
*   }
* })
* ```
**/
export function rootClasses(sectionName: string, node: FormKitNode) {
  const key = `${node.props.type}__${sectionName}`
  const semanticKey = `formkit-${sectionName}`
  const familyKey = node.props.family ? `family:${node.props.family}__${sectionName}` : ''
  const memoKey = `${key}__${familyKey}`
  if (!(memoKey in classes)) {
    const sectionClasses = classes[key] ?? globals[sectionName] ?? {}
    sectionClasses[semanticKey] = true
    if (familyKey in classes) {
      classes[memoKey] = { ...classes[familyKey], ...sectionClasses }
    }
    else {
      classes[memoKey] = sectionClasses
    }
  }
  return classes[memoKey] ?? { [semanticKey]: true }
}

/**
 * These classes have already been merged with globals using tailwind-merge
 * and are ready to be used directly in the theme.
 **/
const classes: Record<string, Record<string, boolean>> = {
  'family:button__outer': {
    'group': true,
    'flex-grow': true,
    'max-w-[20em]': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'family:button__wrapper': {
    'mb-1.5': true,
  },
  'family:button__label': {
    'block': true,
    'text-neutral-900': true,
    'text-sm': true,
    'mb-1': true,
  },
  'family:button__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    '-ml-1': true,
    'mr-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    '[&>svg]:w-full': true,
    'text-neutral-900': true,
  },
  'family:button__suffixIcon': {
    'flex': true,
    'items-center': true,
    '-mr-1': true,
    'ml-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
    'text-neutral-900': true,
  },
  'family:button__input': {
    'appearance-none': true,
    '[color-scheme:light]': true,
    'placeholder:text-neutral-400': true,
    'flex': true,
    'items-center': true,
    'rounded-none': true,
    'px-7': true,
    'py-3': true,
    'outline-none': true,
    'text-neutral-900': true,
    'border': true,
    'border-neutral-900': true,
    'group-data-[disabled]:!cursor-not-allowed': true,
    'group-data-[prefix-icon]:pl-5': true,
    'group-data-[suffix-icon]:pr-5': true,
    'focus:ring-2': true,
  },
  'family:button__help': {
    'text-neutral-900': true,
    'text-xs': true,
  },
  'family:button__messages': {
    '': true,
  },
  'family:button__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'family:box__outer': {
    'group': true,
    'data-[disabled]:select-none': true,
    'p-1': true,
    'data-[multiple]:p-0': true,
  },
  'family:box__wrapper': {
    'inline-flex': true,
    'items-center': true,
  },
  'family:box__fieldset': {
    '': true,
  },
  'family:box__legend': {
    'block': true,
    'text-neutral': true,
    'text-sm': true,
    'mb-2': true,
  },
  'family:box__help': {
    'text-neutral': true,
    'text-body-sm': true,
    'ml-6': true,
    '-mt-1': true,
  },
  'family:box__inner': {
    '': true,
  },
  'family:box__input': {
    'appearance-none': true,
    'outline-none': true,
    'placeholder:text-neutral-400': false,
    'peer': true,
    'absolute': true,
    'overflow-hidden': true,
    'opacity-0': true,
  },
  'family:box__decorator': {
    'flex items-center': true,
    'select-none': true,
    'w-4': true,
    'h-4': true,
    'aspect-[1/1]': true,
    'border-2': true,
    'outline-[var(--color-background-accent)]': true,
    'border-accent': true,
    'hover:shadow-[0px_0px_4px_0px_var(--color-states-azure-1000,#2F6EB4)]': true,
    'rounded': true,
    'bg-neutral': true,
    'peer-disabled:cursor-not-allowed': true,
    'peer-focus-visible:ring-2': true,
    'peer-focus-visible:ring-[var(--color-stroke-focus)]': true,
    'peer-focus-visible:ring-offset-2': true,
    'flex': true,
    'items-center': true,
    'justify-center': true,
  },
  'family:box__decoratorIcon': {
    '': true,
  },
  'family:box__options': {
    'space-y-padding-md': true,
  },
  'family:box__option': {
    '': true,
  },
  'family:box__label': {
    'block': true,
    'text-neutral': true,
    'body-1': true,
    'font-semibold': true,
    'mb-0': true,
    'ml-1.5': true,
  },
  'family:box__optionHelp': {
    'text-neutral': true,
    'text-body-sm': true,
    'ml-6': true,
    '-mt-1': true,
  },
  'family:box__messages': {
    '': true,
  },
  'family:box__message': {
    '': true,
  },
  'family:text__outer': {
    'group': true,
    'flex-grow': true,
    'flex flex-col gap-gap-xs': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'family:text__wrapper': {
    'flex flex-col gap-gap-xxs': true,
  },
  'family:text__label': {
    'block': true,
    'text-neutral body-2 font-bold': true,
    'group-data-[disabled]:text-disabled': true,
    '[&:has(+_*>input[aria-required="true"]):not(.no-asterisk)]:after:content-["*"]': true,
  },
  'family:text__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    'h-5': true,
    'w-5': true,
    '[&>svg]:w-full': true,
  },
  'family:text__suffixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    'h-5': true,
    'w-5': true,
    '[&>svg]:w-full': true,
    'group-data-[disabled]:hidden': true,
  },
  'family:text__inner': {
    'flex': true,
    'border-neutral border-b pb-gap-xxs': true,
    'focus-within:border-focus focus-within:border-b-2': true,
    'group-data-[invalid]:border-alert group-data-[invalid]:border-b-2': true,
    'group-data-[disabled]:border-b-0!': true,
  },
  'family:text__input': {
    'peer': true,
    'appearance-none': true,
    'outline-none': true,
    '[color-scheme:light]': true,
    'grow': true,
    'text-neutral body-1': true,
    'placeholder:text-placeholder placeholder:body-2': true,
  },
  'family:text__help': {
    'text-info body-2': true,
    'group-data-[disabled]:hidden': true,
  },
  'family:text__messages': {
    'space-y-2 ': true,
  },
  'family:text__message': {
    'text-alert': true,
    'caption-1': true,
  },
  'family:dropdown__outer': {
    'group': true,
    'flex-grow': true,
    'space-y-2': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:cursor-not-allowed': true,
    'data-[disabled]:**:cursor-not-allowed': true,
  },
  'family:dropdown__wrapper': {
    '': true,
  },
  'family:dropdown__label': {
    'block': true,
    'text-neutral body-2 font-bold': true,
    'group-data-[disabled]:text-disabled': true,
    '[&:has(+_*>button[required]):not(.no-asterisk)]:after:content-["*"]': true,
  },
  'family:dropdown__inner': {
    'relative': true,
    'flex': true,
    'group-data-[expanded]:rounded-b-none': true,
    'group-data-[expanded]:shadow-lg': true,
    'group-data-[expanded]:shadow-states-azure-1000': true,
  },
  'family:dropdown__prefixIcon': {
    '': true,
  },
  'family:dropdown__suffixIcon': {
    '': true,
  },
  'family:dropdown__input': {
    'appearance-none': true,
  },
  'family:dropdown__selectionWrapper': {
    '': true,
  },
  'family:dropdown__selection': {
    '': true,
  },
  'family:dropdown__tagsWrapper': {
    '': true,
  },
  'family:dropdown__tags': {
    '': true,
  },
  'family:dropdown__tagWrapper': {
    '': true,
  },
  'family:dropdown__tag': {
    '': true,
  },
  'family:dropdown__tagLabel': {
    '': true,
  },
  'family:dropdown__controlLabel': {
    '': true,
  },
  'family:dropdown__listboxButton': {
    '': true,
  },
  'family:dropdown__removeSelection': {
    '': true,
  },
  'family:dropdown__selectIcon': {
    'text-accent': true,
    'group-data-[expanded]:rotate-180': true,
    'transition-transform': true,
    'duration-200': true,
  },
  'family:dropdown__closeIcon': {
    '': true,
  },
  'family:dropdown__loaderIcon': {
    '': true,
  },
  'family:dropdown__loadMore': {
    '': true,
  },
  'family:dropdown__dropdownWrapper': {
    'rounded-none': true,
    'bg-white': true,
    'empty:hidden': true,
    'border': true,
    'border-accent': true,
    'rounded-b-extra-small': true,
    'border-t-0': true,
    'group-data-[overscroll]:border-none': true,
    'group-data-[expanded]:shadow-sm': true,
    'group-data-[expanded]:shadow-states-azure-1000': true,
  },
  'family:dropdown__listbox': {
    '': true,
  },
  'family:dropdown__listitemGroup': {
    '': true,
  },
  'family:dropdown__groupLabel': {
    '': true,
  },
  'family:dropdown__listitem': {
    'relative': true,
    'flex': true,
    'items-center': true,
    'pr-padding-xxs': true,
    'pl-padding-xs': true,
    'py-padding-xxs': true,
    'text-neutral': true,
    'text-base': true,
    'data-[is-active=true]:bg-hover': true,
    'data-[is-active=true]:text-invert': true,
    'group-[]/optgroup:first:before:!rounded-none': true,
    'group-[]/optgroup:last:before:!rounded-none': true,
    '[&:has([data-checked=true])]:bg-light': true,
    '[&:has([data-checked=true])]:text-neutral': true,
    'cursor-pointer': true,
    'focus:bg-hover': true,
    'focus:text-invert': true,
  },
  'family:dropdown__selectedIcon': {
    '': true,
  },
  'family:dropdown__option': {
    'body-1': true,
    'data-[checked=true]:font-bold': true,
  },
  'family:dropdown__optionLoading': {
    '': true,
  },
  'family:dropdown__emptyMessage': {
    '': true,
  },
  'family:dropdown__emptyMessageInner': {
    '': true,
  },
  'family:dropdown__help': {
    'text-hover': true,
    'caption-1': true,
  },
  'family:dropdown__messages': {
    'space-y-2': true,
  },
  'family:dropdown__message': {
    'text-alert': true,
    'caption-1': true,
  },
  'button__outer': {
    'group': true,
    'flex-grow': true,
    'max-w-[20em]': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'button__wrapper': {
    'mb-1.5': true,
  },
  'button__label': {
    'block': true,
    'text-neutral-900': true,
    'text-sm': true,
    'mb-1': true,
  },
  'button__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    '-ml-1': true,
    'mr-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    '[&>svg]:w-full': true,
  },
  'button__suffixIcon': {
    'flex': true,
    'items-center': true,
    '-mr-1': true,
    'ml-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
  },
  'button__input': {
    'appearance-none': true,
    'outline-none': true,
    '[color-scheme:light]': true,
    'placeholder:text-neutral-400': true,
  },
  'button__help': {
    'caption-1': true,
  },
  'button__messages': {
    '': true,
  },
  'button__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'checkbox__outer': {
    '': true,
  },
  'checkbox__wrapper': {
    '': true,
  },
  'checkbox__fieldset': {
    '': true,
  },
  'checkbox__legend': {
    'body-2 font-bold text-neutral': true,
    'group-data-[disabled]:text-disabled': true,
  },
  'checkbox__help': {
    '': true,
  },
  'checkbox__inner': {
    '': true,
  },
  'checkbox__options': {
    '': true,
  },
  'checkbox__option': {
    '': true,
  },
  'checkbox__decorator': {
    'peer-disabled:border-none': true,
    'peer-disabled:outline-none': true,
    'peer-disabled:bg-disabled': true,
    'peer-disabled:text-[var(--color-background-disabled)]': true,
    'peer-checked:bg-accent': true,
    'peer-checked:text-white': true,
    'peer-disabled:peer-checked:text-white': true,
    'text-white': true,
  },
  'checkbox__decoratorIcon': {
    'peer-disabled:text-disabled': true,
    '[&>svg]:w-full': true,
  },
  'checkbox__label': {
    'group-data-[multiple]:!font-body-md': true,
    'group-data-[disabled]:text-disabled': true,

  },
  'checkbox__optionHelp': {
    '': true,
  },
  'checkbox__messages': {
    'text-alert caption-1 ': true,
  },
  'checkbox__message': {
    '': true,
  },
  'color__outer': {
    'group': true,
    'flex-grow': true,
    'max-w-[20em]': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'color__wrapper': {
    'mb-1.5': true,
  },
  'color__label': {
    'block': true,
    'text-neutral-900': true,
    'text-sm': true,
    'mb-1': true,
  },
  'color__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    '-ml-1': true,
    'mr-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    '[&>svg]:w-full': true,
  },
  'color__suffixIcon': {
    'flex': true,
    'items-center': true,
    '-mr-1': true,
    'ml-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
  },
  'color__inner': {
    '!w-auto': true,
  },
  'color__input': {
    'appearance-none': true,
    'outline-none': true,
    '[color-scheme:light]': true,
    'placeholder:text-neutral-400': true,
    'rounded-none': true,
    'overflow-clip': true,
    '[&::-webkit-color-swatch-wrapper]:p-0': true,
    '[&::-webkit-color-swatch]:border-none': true,
    '[&::-moz-color-swatch]:border-none': true,
  },
  'color__help': {
    'text-neutral-900': true,
    'text-xs': true,
  },
  'color__messages': {
    '': true,
  },
  'color__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'date__outer': {
    'group': true,
    'flex-grow': true,
    'max-w-[20em]': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'date__wrapper': {
    'mb-1.5': true,
  },
  'date__label': {
    'block': true,
    'text-neutral-900': true,
    'text-sm': true,
    'mb-1': true,
  },
  'date__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    '-ml-1': true,
    'mr-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    '[&>svg]:w-full': true,
  },
  'date__suffixIcon': {
    'flex': true,
    'items-center': true,
    '-mr-1': true,
    'ml-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
  },
  'date__inner': {
    '': true,
  },
  'date__input': {
    'appearance-none': true,
    'outline-none': true,
    '[color-scheme:light]': true,
    'placeholder:text-neutral-400': true,
  },
  'date__help': {
    'text-neutral-900': true,
    'text-xs': true,
  },
  'date__messages': {
    '': true,
  },
  'date__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'datetime-local__outer': {
    'group': true,
    'flex-grow': true,
    'max-w-[20em]': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'datetime-local__wrapper': {
    'mb-1.5': true,
  },
  'datetime-local__label': {
    'block': true,
    'text-neutral-900': true,
    'text-sm': true,
    'mb-1': true,
  },
  'datetime-local__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    '-ml-1': true,
    'mr-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    '[&>svg]:w-full': true,
  },
  'datetime-local__suffixIcon': {
    'flex': true,
    'items-center': true,
    '-mr-1': true,
    'ml-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
  },
  'datetime-local__inner': {
    '': true,
  },
  'datetime-local__input': {
    'appearance-none': true,
    'outline-none': true,
    '[color-scheme:light]': true,
    'placeholder:text-neutral-400': true,
  },
  'datetime-local__help': {
    'text-neutral-900': true,
    'text-xs': true,
  },
  'datetime-local__messages': {
    '': true,
  },
  'datetime-local__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'email__outer': {
    'group': true,
    'flex-grow': true,
    'max-w-[20em]': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:text-disabled': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'email__wrapper': {
    'mb-1.5': true,
  },
  'email__label': {
    'block': true,
    'text-neutral-900': true,
    'text-sm': true,
    'mb-1': true,
  },
  'email__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    '-ml-1': true,
    'mr-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    '[&>svg]:w-full': true,
  },
  'email__suffixIcon': {
    'flex': true,
    'items-center': true,
    '-mr-1': true,
    'ml-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
  },
  'email__inner': {
    '': true,
  },
  'email__input': {
    'appearance-none': true,
    'outline-none': true,
    '[color-scheme:light]': true,
    'placeholder:text-neutral-400': true,
  },
  'email__help': {
    'text-neutral-900': true,
    'text-xs': true,
  },
  'email__messages': {
    '': true,
  },
  'email__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'file__outer': {
    'group': true,
    'flex-grow': true,
    'max-w-[20em]': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'file__wrapper': {
    'mb-1.5': true,
  },
  'file__label': {
    'block': true,
    'text-neutral-900': true,
    'text-sm': true,
    'mb-1': true,
  },
  'file__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    '-ml-1': true,
    'mr-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    '[&>svg]:w-full': true,
  },
  'file__suffixIcon': {
    'flex': true,
    'items-center': true,
    '-mr-1': true,
    'ml-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
  },
  'file__inner': {
    'relative': true,
    'group-data-[multiple]:rounded-none': true,
  },
  'file__input': {
    'appearance-none': true,
    'outline-none': true,
    '[color-scheme:light]': true,
    'placeholder:text-neutral-400': true,
    'text-transparent': true,
    'absolute': true,
    'inset-0': true,
    'opacity-0': true,
    'z-10': true,
    'file:pointer-events-none': true,
    'file:w-0': true,
    'file:h-0': true,
    'file:overflow-hidden': true,
  },
  'file__fileList': {
    'group/list': true,
    'peer': true,
    'grow': true,
    'min-w-0': true,
  },
  'file__fileItemIcon': {
    'h-[1em]': true,
    'w-[1em]': true,
    'mr-2': true,
    'shrink-0': true,
  },
  'file__fileItem': {
    'flex': true,
    'min-w-0': true,
    'items-center': true,
    'text-neutral-900': true,
  },
  'file__fileName': {
    'truncate': true,
    'min-w-0': true,
    'w-full': true,
    'shrink': true,
  },
  'file__fileRemove': {
    'flex': true,
    'items-center': true,
    'text-neutral-900': true,
    'text-[0px]': true,
    'peer-data-[has-multiple]:text-xs': true,
    'peer-data-[has-multiple]:shrink-0': true,
    'appearance-none': true,
    'outline-none': true,
    'relative': true,
    'z-20': true,
  },
  'file__fileRemoveIcon': {
    'block': true,
    'text-base': true,
    'w-[0.75em]': true,
  },
  'file__noFiles': {
    'flex': true,
    'w-full': true,
    'items-center': true,
    'text-neutral-900': true,
  },
  'file__noFilesIcon': {
    'w-[1em]': true,
    'mr-2': true,
  },
  'file__help': {
    'text-neutral-900': true,
    'text-xs': true,
  },
  'file__messages': {
    '': true,
  },
  'file__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'form__form': {
    '': true,
  },
  'form__actions': {
    '': true,
  },
  'form__summaryInner': {
    'group/summary': true,
    'border': true,
    'border-neutral-900': true,
    'rounded-none': true,
    'py-2': true,
    'px-3': true,
  },
  'form__summaryHeader': {
    'text-lg': true,
    'text-neutral-900': true,
    'mb-2': true,
  },
  'form__messages': {
    '': true,
  },
  'form__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
    'group-[]/summary:text-sm': true,
  },
  'month__outer': {
    'group': true,
    'flex-grow': true,
    'max-w-[20em]': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'month__wrapper': {
    'mb-1.5': true,
  },
  'month__label': {
    'block': true,
    'text-neutral-900': true,
    'text-sm': true,
    'mb-1': true,
  },
  'month__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    '-ml-1': true,
    'mr-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    '[&>svg]:w-full': true,
  },
  'month__suffixIcon': {
    'flex': true,
    'items-center': true,
    '-mr-1': true,
    'ml-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
  },
  'month__inner': {
    '': true,
  },
  'month__input': {
    'appearance-none': true,
    'outline-none': true,
    '[color-scheme:light]': true,
    'placeholder:text-neutral-400': true,
  },
  'month__help': {
    'text-neutral-900': true,
    'text-xs': true,
  },
  'month__messages': {
    '': true,
  },
  'month__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'number__outer': {
    '': true,
  },
  'number__wrapper': {
    '': true,
  },
  'number__label': {
    '': true,
  },
  'number__prefixIcon': {
    '': true,
  },
  'number__suffixIcon': {
    '': true,
  },
  'number__inner': {
    '': true,
  },
  'number__input': {
    '': true,
  },
  'number__help': {
    '': true,
  },
  'number__messages': {
    '': true,
  },
  'number__message': {
    '': true,
  },
  'password__outer': {
    '': true,
  },
  'password__wrapper': {
    '': true,
  },
  'password__label': {
    '': true,
  },
  'password__prefixIcon': {
    '': true,
  },
  'password__suffixIcon': {
    '': true,
  },
  'password__inner': {
    '': true,
  },
  'password__input': {
    '': true,
  },
  'password__help': {
    '': true,
  },
  'password__messages': {
    '': true,
  },
  'password__message': {
    '': true,
  },
  'radio__outer': {
    'max-w-[20em]': false,
  },
  'radio__wrapper': {
    '': true,
  },
  'radio__fieldset': {
    '': true,
  },
  'radio__legend': {
    'text-sm': false,
    'body-2 font-bold mb-4': true,
  },
  'radio__help': {
    '': true,
  },
  'radio__inner': {
    '': true,
  },
  'radio__options': {
    '': true,
  },
  'radio__option': {
    'mb-1.5': false,
    'mb-2': true,
  },
  'radio__decorator': {
    'rounded-full': true,
    'peer-disabled:border-[var(--color-stroke-disabled)]': true,
    'peer-disabled:outline-[var(--color-stroke-disabled)]': true,
    'peer-disabled:bg-disabled': true,
    'peer-disabled:peer-checked:text-disabled': true,
    'peer-disabled:text-[var(--color-background-disabled)]': true,
    'text-white': true,
    'peer-checked:bg-white': true,
    'peer-checked:text-accent': true,
    'cursor-pointer': true,
  },
  'radio__decoratorIcon': {
    'w-2.5': true,
    'h-2.5': true,
    '[&>svg]:w-full': true,
  },
  'radio__label': {
    'group-data-[disabled]:text-disabled': true,
  },
  'radio__optionHelp': {
    '': true,
  },
  'radio__messages': {
    '': true,
  },
  'radio__message': {
    'text-alert': true,
    'caption-1': true,
    'mb-1.5': true,
  },
  'range__outer': {
    'group': true,
    'flex-grow': true,
    'max-w-[20em]': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'range__wrapper': {
    'mb-1.5': true,
  },
  'range__label': {
    'block': true,
    'text-neutral-900': true,
    'text-sm': true,
    'mb-1': true,
  },
  'range__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    '-ml-1': true,
    'mr-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    '[&>svg]:w-full': true,
  },
  'range__suffixIcon': {
    'flex': true,
    'items-center': true,
    '-mr-1': true,
    'ml-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
  },
  'range__inner': {
    'relative': true,
    '!border-none': true,
    '!ring-0': true,
    '!px-0': true,
    '!bg-transparent': true,
  },
  'range__input': {
    'appearance-none': true,
    'outline-none': true,
    '[color-scheme:light]': true,
    'placeholder:text-neutral-400': true,
    '[&::-webkit-slider-runnable-track]:bg-neutral-300': true,
    '[&::-webkit-slider-runnable-track]:h-1.5': true,
    '[&::-webkit-slider-runnable-track]:rounded-none': true,
    '[&::-moz-range-track]:bg-neutral-300': true,
    '[&::-moz-range-track]:h-1.5': true,
    '[&::-moz-range-track]:rounded-none': true,
    '[&::-webkit-slider-thumb]:appearance-none': true,
    '[&::-webkit-slider-thumb]:w-3.5': true,
    '[&::-webkit-slider-thumb]:h-3.5': true,
    '[&::-webkit-slider-thumb]:bg-neutral-600': true,
    '[&::-webkit-slider-thumb]:rounded-full': true,
    '[&::-webkit-slider-thumb]:relative': true,
    '[&::-webkit-slider-thumb]:top-1/2': true,
    '[&::-webkit-slider-thumb]:-translate-y-1/2': true,
    '[&::-webkit-slider-thumb]:focus-visible:ring-2': true,
    '[&::-moz-range-thumb]:appearance-none': true,
    '[&::-moz-range-thumb]:border-none': true,
    '[&::-moz-range-thumb]:w-3.5': true,
    '[&::-moz-range-thumb]:h-3.5': true,
    '[&::-moz-range-thumb]:bg-neutral-600': true,
    '[&::-moz-range-thumb]:rounded-full': true,
    '[&::-moz-range-thumb]:focus-visible:ring-2': true,
  },
  'range__help': {
    'text-neutral-900': true,
    'text-xs': true,
  },
  'range__messages': {
    '': true,
  },
  'range__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'search__outer': {
    '': true,
  },
  'search__wrapper': {
    '': true,
  },
  'search__label': {
    '': true,
  },
  'search__prefixIcon': {
    '': true,
  },
  'search__suffixIcon': {
    '': true,
  },
  'search__inner': {
    '': true,
  },
  'search__input': {
    '': true,
  },
  'search__help': {
    '': true,
  },
  'search__messages': {
    '': true,
  },
  'search__message': {
    '': true,
  },
  'select__outer': {
    'group': true,
    'flex-grow': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'select__wrapper': {
    'mb-1.5': true,
  },
  'select__label': {
  },
  'select__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    'mr-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    '[&>svg]:w-full': true,
    'ml-2': true,
    'text-neutral-900': true,
  },
  'select__suffixIcon': {
    'flex': true,
    'items-center': true,
    'ml-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
    'mr-2': true,
    'text-neutral': true,
  },
  'select__inner': {
    'relative': true,
    'flex': true,
    'items-center': true,
    'border': true,
    'border-neutral': true,
    'rounded-extra-small': true,
    'focus-within:ring-2': true,
    'focus-within:ring-(--color-stroke-focus)': true,
    'focus-within:ring-offset-2': true,
    'group-data-[multiple]:rounded-none': true,
  },
  'select__input': {
    'appearance-none': true,
    'placeholder:text-placeholder': true,
    'grow': true,
    'py-padding-xs': true,
    'px-padding-xs': true,
    'body-1': true,
    'text-ellipsis': true,
    'min-w-0': true,
    'outline-none': true,
    'bg-transparent': true,
    'group-data-[prefix-icon]:!pl-0': true,
    'group-data-[suffix-icon]:!pr-0': true,
    'data-[placeholder]:text-neutral': true,
    'border-none': true,
    'focus:ring-0': true,
    'bg-none': true,
  },
  'select__selectIcon': {
    'absolute': true,
    'w-[1em]': true,
    'text-neutral-900': true,
    'pointer-events-none': true,
    'right-2': true,
    'group-data-[suffix-icon]:mr-[1.5em]': true,
  },
  'select__optGroup': {
    'group/optgroup': true,
    'bg-white': true,
    'text-neutral-900': true,
    'font-bold': true,
    'text-sm': true,
  },
  'select__option': {
    'text-neutral': true,
    'group-data-[multiple]:text-sm': true,
    'group-data-[multiple]:outline-none': true,
    'group-data-[multiple]:border-none': true,
    'group-data-[multiple]:py-1.5': true,
    'group-data-[multiple]:px-2': true,
    'dark:text-neutral': true,
    'dark:bg-neutral-900': true,
  },
  'select__help': {
    'text-neutral-900': true,
    'text-xs': true,
  },
  'select__messages': {
    '': true,
  },
  'select__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'submit__outer': {
    'group': true,
    'flex-grow': true,
    'max-w-[20em]': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'submit__wrapper': {
    'mb-1.5': true,
  },
  'submit__label': {
    'block': true,
    'text-neutral-900': true,
    'text-sm': true,
    'mb-1': true,
  },
  'submit__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    '-ml-1': true,
    'mr-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    '[&>svg]:w-full': true,
  },
  'submit__suffixIcon': {
    'flex': true,
    'items-center': true,
    '-mr-1': true,
    'ml-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
  },
  'submit__input': {
    'appearance-none': true,
    'outline-none': true,
    '[color-scheme:light]': true,
    'placeholder:text-neutral-400': true,
  },
  'submit__help': {
    'text-neutral-900': true,
    'text-xs': true,
  },
  'submit__messages': {
    '': true,
  },
  'submit__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'tel__outer': {
    '': true,
  },
  'tel__wrapper': {
    '': true,
  },
  'tel__label': {
    '': true,
  },
  'tel__prefixIcon': {
    '': true,
  },
  'tel__suffixIcon': {
    '': true,
  },
  'tel__inner': {
    '': true,
  },
  'tel__input': {
    '': true,
  },
  'tel__help': {
    '': true,
  },
  'tel__messages': {
    '': true,
  },
  'tel__message': {
    '': true,
  },
  'text__outer': {
    '': true,
  },
  'text__wrapper': {
    '': true,
  },
  'text__label': {
    '': true,
  },
  'text__prefixIcon': {
    '': true,
  },
  'text__suffixIcon': {
    '': true,
  },
  'text__inner': {
    '': true,
  },
  'text__input': {
    '': true,
  },
  'text__help': {
    '': true,
  },
  'text__messages': {
    '': true,
  },
  'text__message': {
    '': true,
  },
  'textarea__outer': {
    'group': true,
    'flex-grow': true,
    'flex flex-col gap-gap-xs': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'textarea__wrapper': {
    'flex flex-col flex-grow gap-gap-xxs': true,
  },
  'textarea__label': {
    'block': true,
    'text-neutral body-2 font-bold': true,
    'group-data-[disabled]:text-disabled': true,
  },
  'textarea__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    'h-5': true,
    'w-5': true,
    '[&>svg]:w-full': true,
  },
  'textarea__suffixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    'h-5': true,
    'w-5': true,
    '[&>svg]:w-full': true,
    'group-data-[disabled]:hidden': true,
  },
  'textarea__inner': {
    'flex flex-grow': true,
    'border-neutral border-b pb-gap-xxs': true,
    'focus-within:border-focus focus-within:border-b-2': true,
    'group-data-[invalid]:border-alert group-data-[invalid]:border-b-2': true,
    'group-data-[disabled]:border-b-0!': true,
  },
  'textarea__input': {
    'peer': true,
    'appearance-none': true,
    'outline-none': true,
    '[color-scheme:light]': true,
    'grow': true,
    'text-neutral body-1': true,
    'placeholder:text-placeholder placeholder:body-2': true,
  },
  'textarea__help': {
    'text-info body-2': true,
    'group-data-[disabled]:hidden': true,
  },
  'textarea__messages': {
    'space-y-2 ': true,
    'group-data-[disabled]:hidden': true,
  },
  'textarea__message': {
    'text-alert': true,
    'caption-1': true,
  },
  'time__outer': {
    'group': true,
    'flex-grow': true,
    'max-w-[20em]': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'time__wrapper': {
    'mb-1.5': true,
  },
  'time__label': {
    'block': true,
    'text-neutral-900': true,
    'text-sm': true,
    'mb-1': true,
  },
  'time__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    '-ml-1': true,
    'mr-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    '[&>svg]:w-full': true,
  },
  'time__suffixIcon': {
    'flex': true,
    'items-center': true,
    '-mr-1': true,
    'ml-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
  },
  'time__inner': {
    '': true,
  },
  'time__input': {
    'appearance-none': true,
    'outline-none': true,
    '[color-scheme:light]': true,
    'placeholder:text-neutral-400': true,
  },
  'time__help': {
    'text-neutral-900': true,
    'text-xs': true,
  },
  'time__messages': {
    '': true,
  },
  'time__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'url__outer': {
    '': true,
  },
  'url__wrapper': {
    '': true,
  },
  'url__label': {
    '': true,
  },
  'url__prefixIcon': {
    '': true,
  },
  'url__suffixIcon': {
    '': true,
  },
  'url__inner': {
    '': true,
  },
  'url__input': {
    '': true,
  },
  'url__help': {
    '': true,
  },
  'url__messages': {
    '': true,
  },
  'url__message': {
    '': true,
  },
  'week__outer': {
    group: true,
  },
  'week__wrapper': {
    'mb-1.5': true,
  },
  'week__label': {
    'block': true,
    'text-neutral-900': true,
    'text-sm': true,
    'mb-1': true,
  },
  'week__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    '-ml-1': true,
    'mr-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    '[&>svg]:w-full': true,
  },
  'week__suffixIcon': {
    'flex': true,
    'items-center': true,
    '-mr-1': true,
    'ml-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
  },
  'week__inner': {
    '': true,
  },
  'week__input': {
    'appearance-none': true,
    'outline-none': true,
    '[color-scheme:light]': true,
    'placeholder:text-neutral-400': true,
  },
  'week__help': {
    'text-neutral-900': true,
    'text-xs': true,
  },
  'week__messages': {
    '': true,
  },
  'week__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'autocomplete__outer': {
    'group flex-grow flex flex-col gap-gap-xs': true,
    'data-[disabled]:select-none data-[disabled]:pointer-events-none': true,
  },
  'autocomplete__wrapper': {
    'flex flex-col gap-gap-xxs': true,
  },
  'autocomplete__label': {
    'block': true,
    'text-neutral body-2 font-bold': true,
    'group-data-[disabled]:text-disabled': true,
  },
  'autocomplete__inner': {
    'flex border-neutral border-b pb-gap-xxs': true,
    'focus-within:border-focus focus-within:border-b-2': true,
    'group-data-[invalid]:border-alert group-data-[invalid]:border-b-2': true,
    'group-data-[disabled]:border-b-0!': true,
    'group-data-[expanded]:shadow-[0_3px_4px_-1px]': true,
  },
  'autocomplete__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    'h-5': true,
    'w-5': true,
    '[&>svg]:w-full': true,
  },
  'autocomplete__suffixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    'h-5': true,
    'w-5': true,
    '[&>svg]:w-full': true,
    'group-data-[disabled]:hidden': true,
  },
  'autocomplete__input': {
    'appearance-none outline-none [color-scheme:light] grow': true,
    'text-neutral body-1 placeholder:text-placeholder placeholder:body-2 group-data-[disabled]:text-disabled': true,
  },
  'autocomplete__selections': {
    '': true,
  },
  'autocomplete__selectionWrapper': {
    '': true,
  },
  'autocomplete__selection': {
    '': true,
  },
  'autocomplete__loaderIcon': {
    '': true,
  },
  'autocomplete__removeSelection': {
    '*:flex *:items-center [&_svg]:w-4 [&_svg]:h-4': true,
  },
  'autocomplete__listboxButton': {
    '': true,
  },
  'autocomplete__selectIcon': {
    '': true,
  },
  'autocomplete__dropdownWrapper': {
    'border-focus': true,
  },
  'autocomplete__listbox': {
    '': true,
  },
  'autocomplete__listitem': {
    '': true,
  },
  'autocomplete__selectedIcon': {
    '': true,
  },
  'autocomplete__option': {
    '': true,
  },
  'autocomplete__emptyMessage': {
    '': true,
  },
  'autocomplete__emptyMessageInner': {
    '': true,
  },
  'autocomplete__help': {
    'text-info body-2 group-data-[disabled]:hidden': true,
  },
  'autocomplete__messages': {
    'space-y-2 group-data-[disabled]:hidden': true,
  },
  'autocomplete__message': {
    'text-alert caption-1': true,
  },
  'colorpicker__outer': {
    'group': true,
    'flex-grow': true,
    'max-w-[20em]': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'colorpicker__wrapper': {
    'mb-1.5': true,
  },
  'colorpicker__label': {
    'block': true,
    '!text-neutral': true,
    'text-sm': true,
    'mb-1': true,
  },
  'colorpicker__inner': {
    'relative': true,
    'inline-flex': true,
    '!w-auto': true,
    'pl-2': true,
    'group-data-[inline]:border-none': true,
    'group-data-[inline]:p-0': true,
    'group-data-[inline]:outline-none': true,
    'group-data-[inline]:!ring-0': true,
    'group-data-[inline]:!w-full': true,
    'group-data-[inline]:rounded-none': true,
  },
  'colorpicker__swatchPreview': {
    'flex': true,
    'items-center': true,
    'rounded-none': true,
  },
  'colorpicker__canvasSwatchPreviewWrapper': {
    'mr-2': true,
    'relative': true,
    'before:content-[\'\']': true,
    'before:absolute': true,
    'before:inset-0': true,
    'before:rounded-none': true,
    'before:shadow-[inset_0_0_0_1px_rgba(0,0,0,0.2)]': true,
    'before:z-[2]': true,
  },
  'colorpicker__canvas': {
    'block': true,
    'w-full': true,
  },
  'colorpicker__canvasSwatchPreview': {
    'text-base': true,
    'rounded-none': true,
    'aspect-[1/1]': true,
    'shrink-0': true,
    'grow': true,
    '!w-[1.5em]': true,
  },
  'colorpicker__valueString': {
    'inline-block': true,
    'text-sm': true,
    'text-neutral-900': true,
    'font-mono': true,
  },
  'colorpicker__panel': {
    'absolute': true,
    'top-full': true,
    'left-0': true,
    'z-[99]': true,
    'flex': true,
    'w-[100vw]': true,
    'max-w-[18.5em]': true,
    'touch-manipulation': true,
    'flex-col': true,
    'rounded-none': true,
    'border': true,
    'bg-white': true,
    'p-2': true,
    'group-data-[inline]:static': true,
    'group-data-[inline]:z-auto': true,
    'group-data-[inline]:w-full': true,
    'group-data-[inline]:max-w-none': true,
    'group-data-[inline]:bg-transparent': true,
    'group-data-[inline]:border': true,
    'border-neutral-900': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-[&:not([data-inline])]:!fixed': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-[&:not([data-inline])]:top-auto': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-[&:not([data-inline])]:max-w-none': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-[&:not([data-inline])]:bottom-0': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-[&:not([data-inline])]:left-0': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-[&:not([data-inline])]:rounded-none': true,
  },
  'colorpicker__panelClose': {
    'flex': true,
    'justify-end': true,
    'items-center': true,
    'text-neutral-900': true,
    'mb-1.5': true,
    'border-b': true,
    'border-neutral-900': true,
    'pb-2': true,
  },
  'colorpicker__closeIcon': {
    'w-[1.75rem]': true,
    'aspect-[1/1]': true,
    'p-1': true,
    'rounded-none': true,
    'border': true,
    'border-neutral-900': true,
    '[&>svg]:w-full': true,
    '[&>svg]:aspect-[1/1]': true,
    '[&>svg]:max-w-none': true,
    '[&>svg]:max-h-none': true,
  },
  'colorpicker__control': {
    'absolute': true,
    'bg-white': true,
    'shadow-[0_0_0_2px_rgba(255,255,255,1),0_0_0_3px_rgba(0,0,0,0.2)]': true,
    '-translate-y-1/2': true,
    '-translate-x-1/2': true,
    'pointer-events-none': true,
    'focus-visible:outline-none': true,
    'focus-visible:ring-2': true,
    'data-[prevent-focus-style]:outline-none': true,
    'data-[prevent-focus-style]:ring-0': true,
    'data-[prevent-focus-style]:shadow-[0_0_0_2px_rgba(255,255,255,1),0_0_0_3px_rgba(0,0,0,0.2)]': true,
  },
  'colorpicker__controlGroup': {
    'grid': true,
    '[grid-template-areas:\'a_a_a\'_\'b_c_e\'_\'b_d_e\']': true,
    'mb-2': true,
  },
  'colorpicker__LS': {
    '[grid-area:a]': true,
    'relative': true,
    'mb-2': true,
  },
  'colorpicker__canvasLS': {
    'aspect-[2/1]': true,
    'cursor-pointer': true,
    'rounded-none': true,
  },
  'colorpicker__controlLS': {
    'w-[10px]': true,
    'h-[10px]': true,
    'rounded-full': true,
  },
  'colorpicker__preview': {
    '[grid-area:b]': true,
    'relative': true,
    'inline-flex': true,
    'w-[2em]': true,
    'aspect-[1/1]': true,
    'overflow-hidden': true,
    'rounded-none': true,
    'after:content-[\'\']': true,
    'after:absolute': true,
    'after:inset-0': true,
    'after:rounded-none': true,
    'after:shadow-[inset_0_0_0_1px_rgba(0,0,0,0.2)]': true,
  },
  'colorpicker__hue': {
    '[grid-area:c]': true,
    'relative': true,
    'inline-flex': true,
    'h-3/4': true,
    'ml-2': true,
  },
  'colorpicker__canvasHue': {
    'rounded-none': true,
  },
  'colorpicker__controlHue': {
    'w-[4px]': true,
    'top-1/2': true,
    'h-[calc(100%-2px)]': true,
    'rounded-none': true,
  },
  'colorpicker__alpha': {
    '[grid-area:d]': true,
    'relative': true,
    'inline-flex': true,
    'h-3/4': true,
    'ml-2': true,
  },
  'colorpicker__canvasAlpha': {
    'rounded-none': true,
  },
  'colorpicker__controlAlpha': {
    'w-[4px]': true,
    'top-1/2': true,
    'h-[calc(100%-2px)]': true,
    'rounded-none': true,
  },
  'colorpicker__eyeDropper': {
    '[grid-area:e]': true,
    'w-[2em]': true,
    'ml-2': true,
    'self-center': true,
    'justify-center': true,
    'justify-self-center': true,
    'inline-flex': true,
    'content-center': true,
    'items-center': true,
    'aspect-[1/1]': true,
    'cursor-pointer': true,
    'rounded-none': true,
    'border': true,
    'border-neutral-900': true,
    'text-neutral-900': true,
  },
  'colorpicker__eyeDropperIcon': {
    'w-auto': true,
    '[&>svg]:w-[1em]': true,
  },
  'colorpicker__formatField': {
    'flex': true,
    'items-center': true,
    'justify-center': true,
    'grow': true,
  },
  'colorpicker__colorInputGroup': {
    'flex': true,
    'items-center': true,
    'justify-center': true,
    'grow': true,
  },
  'colorpicker__fieldGroup': {
    'flex': true,
    'flex-col': true,
    'items-center': true,
    'justify-center': true,
    'w-full': true,
    'mr-1': true,
    '[&>input]:p-1': true,
    '[&>input]:text-sm': true,
    '[&>input]:text-neutral': true,
    '[&>input]:m-0': true,
    '[&>input]:grow': true,
    '[&>input]:shrink': true,
    '[&>input]:w-full': true,
    '[&>input]:border': true,
    '[&>input]:border-neutral': true,
    '[&>input]:rounded-none': true,
    '[&>input]:text-center': true,
    '[&>input]:appearance-none': true,
    '[&>input::-webkit-outer-spin-button]:appearance-none': true,
    '[&>input::-webkit-inner-spin-button]:appearance-none': true,
    '[&>input::-webkit-inner-spin-button]:m-0': true,
    '[&>input:focus]:outline-none': true,
    '[&>input:focus]:ring-2': true,
    'max-[431px]:[&>input]:text-base': true,
  },
  'colorpicker__colorField': {
    'bg-transparent': true,
    'text-neutral': true,
    'border': true,
    'border-neutral': true,
  },
  'colorpicker__fieldLabel': {
    'caption-1': true,
    'text-neutral': true,
    'mt-2': true,
  },
  'colorpicker__formatSwitcher': {
    'flex': true,
    'justify-end': true,
    'self-start': true,
    'shrink-0': true,
    'p-1': true,
    'mt-0.5': true,
    'text-neutral': true,
    'select-none': true,
  },
  'colorpicker__switchIcon': {
    '[&>svg]:w-3': true,
  },
  'colorpicker__swatches': {
    'inline-flex': true,
    'flex-wrap': true,
    'w-full': true,
    'justify-self-center': true,
    'mx-auto': true,
    'pt-2': true,
    'pb-2': true,
    'mt-2': true,
    '-mb-2': true,
    'border-t': true,
    'border-neutral': true,
    'overflow-auto': true,
    'max-h-[200px]': true,
    'select-none': true,
    'first:-mt-2': true,
    'first:last:-mb-2': true,
    'first:border-t-0': true,
  },
  'colorpicker__swatchGroup': {
    'flex': true,
    'flex-wrap': true,
    'w-full': true,
    'mb-2': true,
    'last:mb-0': true,
  },
  'colorpicker__swatchGroupLabel': {
    'ml-1': true,
    'block': true,
    'w-full': true,
    'text-sm': true,
    'text-neutral': true,
  },
  'colorpicker__swatch': {
    'relative': true,
    'text-base': true,
    'w-[calc((100%/10)-0.5em)]': true,
    'max-w-[22px]': true,
    'm-[0.16em]': true,
    'cursor-pointer': true,
    'before:content-[\'\']': true,
    'before:absolute': true,
    'before:inset-0': true,
    'before:shadow-[inset_0_0_0_1px_rgba(0,0,0,0.2)]': true,
    'before:pointer-events-none': true,
    'before:z-[2]': true,
    'data-[active=true]:after:content-[\'\']': true,
    'data-[active=true]:after:block': true,
    'data-[active=true]:after:absolute': true,
    'data-[active=true]:after:w-1.5': true,
    'data-[active=true]:after:h-1.5': true,
    'data-[active=true]:after:top-1/2': true,
    'data-[active=true]:after:left-1/2': true,
    'data-[active=true]:after:pointer-events-none': true,
    'data-[active=true]:after:rounded-full': true,
    'data-[active=true]:after:-translate-x-1/2': true,
    'data-[active=true]:after:-translate-y-1/2': true,
    'data-[active=true]:after:bg-white': true,
    'data-[active=true]:after:z-[2]': true,
    'data-[active=true]:after:shadow-[0_0_0_1px_rgba(0,0,0,0.33)]': true,
    '[&>canvas]:block': true,
    '[&>canvas]:w-full': true,
    '[&>canvas]:aspect-[1/1]': true,
    '[&>canvas:focus-visible]:outline-none': true,
    '[&>canvas:focus-visible]:ring-2': true,
  },
  'colorpicker__help': {
    'text-neutral-900': true,
    'text-xs': true,
    'mt-1.5': true,
    'group-data-[inline]:-mt-1': true,
    'group-data-[inline]:mb-2': true,
  },
  'colorpicker__messages': {
    '': true,
  },
  'colorpicker__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'datepicker__outer': {
    'group': true,
    'flex-grow': true,
    'w-full': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'datepicker__wrapper': {
    '': true,
  },
  'datepicker__label': {
    'block': true,
    'w-full': true,
    'text-neutral': true,
    'text-sm': true,
    'mb-1': true,
    'body-2': true,
    'group-data-[disabled]:text-disabled': true,
    'group-[.underline-appearance]:mb-0': true,
    '[&:has(+_*>input[required]):not(.no-asterisk)]:after:content-["*"]': true,
  },
  'datepicker__inner': {
    'w-full': true,
    '!px-padding-xxs': true,
    '!py-padding-xxs': true,
    'relative': true,
    'border': true,
    '!border-neutral': true,
    '!rounded-extra-small': true,
    'focus-within:!ring-offset-4': true,
    'focus-within:!ring-transparent': true,
    'group-data-[expanded=true]:!rounded-b-none': true,
    'group-data-[expanded=true]:!border-focus': true,
    'group-data-[expanded=true]:!shadow-sm': true,
    'group-data-[expanded=true]:!shadow-states-azure-1000': true,
    'bg-white': true,
    'group-[.underline-appearance]:!bg-transparent': true,
    'group-[.underline-appearance]:!p-0': true,
    'group-[.underline-appearance]:border-0': true,
    'group-[.underline-appearance]:border-b': true,
    'group-[.underline-appearance]:!rounded-none': true,
    'group-data-[disabled]:!border-none': true,
  },
  'datepicker__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    '-ml-1': true,
    'mr-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    '[&>svg]:w-full': true,
  },
  'datepicker__suffixIcon': {
    'flex': true,
    'items-center': true,
    '-mr-1': true,
    'ml-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
  },
  'datepicker__removeSelection': {
    '': true,
  },
  'datepicker__clearIcon': {
    'flex': true,
    'items-center': true,
    'ml-1': true,
    'mr-2': true,
    'text-neutral': true,
    'text-base': true,
    'w-[0.75em]': true,
    'h-[0.75em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
  },
  'datepicker__overlay': {
    'text-neutral': true,
    'leading-[1.9]!': true,
  },
  'datepicker__overlayInner': {
    'body-1': true,
  },
  'datepicker__overlayPlaceholder': {
    'text-placeholder': true,
    'body-1': true,
  },
  'datepicker__overlayChar': {
    'text-neutral': true,
    'body-1': true,
  },
  'datepicker__overlayEnum': {
    'body-1': true,
    'text-neutral': true,
    'font-semibold': true,
  },
  'datepicker__overlayLiteral': {
    'text-placeholder': true,
    'body-1': true,
  },
  'datepicker__input': {
    'appearance-none': true,
    'outline-none': true,
    '[&::selection]:bg-disabled': true,
    'body-1': true,
    'group-data-[disabled]:!text-disabled': true,

  },
  'datepicker__openButton': {
    'appearance-none': true,
    'border-0': true,
    'bg-transparent': true,
    'flex': true,
    'items-center': true,
    'justify-center': true,
    'p-0': true,
    'self-stretch': true,
    'focus-visible:outline-none': true,
    'focus-visible:ring-2': true,
    'h-8': true,
    'aspect-square': true,
    'group-data-[disabled]:hidden': true,
  },
  'datepicker__calendarIcon': {
    'text-neutral': true,
    'focus-visible:text-neutral': true,
    'flex': true,
    'w-[1em]': true,
    'grow-0': true,
    'shrink-0': true,
    'self-stretch': true,
    'select-none': true,
    '[&>svg]:w-full': true,
    '[&>svg]:m-auto': true,
    '[&>svg]:max-h-[1em]': true,
    '[&>svg]:max-w-[1em]': true,
  },
  'datepicker__panelWrapper': {
    'group/panel': true,
    'absolute': true,
    'min-w-[20em]': true,
    'top-full': true,
    'left-0': true,
    'rounded-b-extra-small': true,
    'shadow-sm': true,
    'shadow-states-azure-1000': true,
    'p-4': true,
    'bg-white': true,
    'border': true,
    'border-focus': true,
    'border-t-0': true,
    'z-[99]': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-[&:not([data-inline])]:!fixed': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-[&:not([data-inline])]:top-auto': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-[&:not([data-inline])]:max-w-none': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-[&:not([data-inline])]:bottom-0': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-[&:not([data-inline])]:left-0': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-[&:not([data-inline])]:rounded-none': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-[&:not([data-inline])]:w-full': true,
  },
  'datepicker__panelHeader': {
    'grid': true,
    'grid-cols-[2.5em_1fr_2.5em]': true,
    'justify-center': true,
    'items-center': true,
    'mb-2': true,
    'pb-2.5': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-[&:not([data-inline])]:grid-cols-[2.5em_1fr_2.5em_2.5em]': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-[&:not([data-inline])]:group-data-[panel=time]/panel:grid-cols-[2.5em_1fr_2.5em]': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-[&:not([data-inline])]:group-data-[panel=month]/panel:grid-cols-[2.5em_1fr_2.5em]': true,
  },
  'datepicker__panelClose': {
    'aspect-[1/1]': true,
    'border': true,
    'border-neutral': true,
    'rounded-none': true,
    'flex': true,
    'items-center': true,
    'justify-center': true,
    'text-neutral': true,
    '[&_svg]:w-[1.25em]': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-data-[panel=time]/panel:col-start-3': true,
    '[@media(max-width:431px)_and_(hover:none)]:group-data-[panel=month]/panel:col-start-3': true,
  },
  'datepicker__monthsHeader': {
    'flex': true,
    'items-center': true,
    'justify-center': true,
    'col-start-2': true,
    'col-end-2': true,
    'cursor-pointer': true,
  },
  'datepicker__yearsHeader': {
    'flex': true,
    'items-center': true,
    'justify-center': true,
    'col-start-2': true,
    'col-end-2': true,
    'cursor-pointer': true,
  },
  'datepicker__timeHeader': {
    'flex': true,
    'items-center': true,
    'justify-center': true,
    'col-start-2': true,
    'col-end-2': true,
    'cursor-pointer': true,
  },
  'datepicker__daysHeader': {
    'flex': true,
    'items-center': true,
    'justify-center': true,
    'cursor-pointer': true,
  },
  'datepicker__prev': {
    'mr-auto': true,
    'px-2.5': true,
    'py-0.5': true,
    'col-start-1': true,
    'col-end-1': true,
    'focus-visible:outline-none': true,
    'focus-visible:ring-2': true,
    'cursor-pointer': true,
  },
  'datepicker__next': {
    'ml-auto': true,
    'px-2.5': true,
    'py-0.5': true,
    'col-start-3': true,
    'col-end-3': true,
    'focus-visible:outline-none': true,
    'focus-visible:ring-2': true,
    'cursor-pointer': true,
  },
  'datepicker__prevLabel': {
    hidden: true,
  },
  'datepicker__nextLabel': {
    hidden: true,
  },
  'datepicker__prevIcon': {
    'flex': true,
    'w-[0.75em]': true,
    'select-none': true,
    'text-neutral': true,
    '[&>svg]:w-full': true,
  },
  'datepicker__nextIcon': {
    'flex': true,
    'w-[0.75em]': true,
    'select-none': true,
    'text-neutral': true,
    '[&>svg]:w-full': true,
  },
  'datepicker__panel': {
    'flex': true,
    'justify-center': true,
  },
  'datepicker__months': {
    'grid': true,
    'grid-cols-3': true,
    'w-full': true,
    'cursor-pointer': true,
  },
  'datepicker__month': {
    'm-2': true,
    'p-2': true,
    'text-center': true,
    'text-neutral': true,
    'bg-transparent': true,
    'aria-selected:!bg-accent': true,
    'aria-selected:!text-white': true,
    'focus:outline-none': true,
    'focus:ring-2': true,
    'focus:ring-(--color-stroke-accent)': true,
    'focus:bg-light': true,
    'group-data-[disabled=true]:opacity-50': true,
    'group-data-[disabled=true]:pointer-events-none': true,
    'cursor-pointer': true,
  },
  'datepicker__years': {
    'grid': true,
    'grid-cols-5': true,
    'w-full': true,
    'cursor-pointer': true,
  },
  'datepicker__year': {
    'text-sm': true,
    'text-center': true,
    'text-neutral': true,
    'items-center': true,
    'm-1': true,
    'p-1': true,
    'aria-selected:!bg-accent': true,
    'aria-selected:!text-white': true,
    'focus:outline-none': true,
    'focus:ring-2': true,
    'focus:ring-(--color-stroke-accent)': true,
    'focus:bg-light': true,
    'group-data-[disabled=true]:opacity-50': true,
    'group-data-[disabled=true]:pointer-events-none': true,
    'cursor-pointer': true,
  },
  'datepicker__weekDays': {
    'grid': true,
    'grid-cols-7': true,
  },
  'datepicker__weekDay': {
    'text-disabled': true,
    'm-1.5': true,
    'body-1': true,
    'cursor-default': true,
    'pointer-events-none': true,
  },
  'datepicker__week': {
    'grid': true,
    'grid-cols-7': true,
    'group-data-[disabled=true]:opacity-50': true,
    'group-data-[disabled=true]:pointer-events-none': true,
  },
  'datepicker__dayCell': {
    'flex': true,
    'body-1': true,
    'items-center': true,
    'justify-center': true,
    'text-center': true,
    'text-neutral': true,
    'w-[2.25em]': true,
    'h-[2.25em]': true,
    'm-1': true,
    'p-2': true,
    'bg-neutral': true,
    'aria-selected:bg-accent': true,
    'aria-selected:text-white': true,
    'focus:outline-none': true,
    'focus:ring-2': true,
    'focus:ring-(--color-stroke-accent)': true,
    'cursor-pointer': true,
    'focus:bg-light': true,
    'data-[disabled=true]:opacity-50': true,
    'data-[disabled=true]:pointer-events-none': true,
  },
  'datepicker__timeInput': {
    'w-full': true,
    'border-2': true,
    'text-neutral': true,
    'border-neutral': true,
    'rounded-none': true,
    'py-2': true,
    'px-3': true,
    'my-2.5': true,
    'focus-visible:outline-none': true,
    'focus-visible:ring-2': true,
  },
  'datepicker__dayButton': {
    'appearance-none': true,
    'text-neutral': true,
    'px-2.5': true,
    'py-0.5': true,
    'mx-1': true,
    'focus-visible:outline-none': true,
    'focus-visible:ring-2': true,
  },
  'datepicker__monthButton': {
    'appearance-none': true,
    'text-neutral': true,
    'px-2.5': true,
    'py-0.5': true,
    'mx-1': true,
    'focus-visible:outline-none': true,
    'focus-visible:ring-2': true,
  },
  'datepicker__yearButton': {
    'appearance-none': true,
    'text-neutral': true,
    'px-2.5': true,
    'py-0.5': true,
    'mx-1': true,
    'focus-visible:outline-none': true,
    'focus-visible:ring-2': true,
  },
  'datepicker__help': {
    'text-hover': true,
    'caption-1': true,
  },
  'datepicker__messages': {
    '': true,
  },
  'datepicker__message': {
    'text-alert': true,
    'mb-1.5': true,
    'caption-1': true,
  },
  'dropdown__outer': {
    '': true,
  },
  'dropdown__wrapper': {
    '': true,
  },
  'dropdown__label': {
    'body-2 font-bold text-neutral mb-1.5': true,
  },
  'dropdown__inner': {
    '': true,
  },
  'dropdown__prefixIcon': {
    '': true,
  },
  'dropdown__suffixIcon': {
    '': true,
  },
  'dropdown__selector': {
    'flex': true,
    'grow': true,
    'justify-between': true,
    'items-center': true,
    'w-full': true,
    'pl-padding-xs': true,
    'pr-padding-xxs': true,
    'py-padding-xxs': true,
    'body-1': true,
    'bg-neutral': true,
    'font-semibold': true,
    'text-neutral': true,
    'text-left': true,
    'group-data-[prefix-icon]:!pl-0': true,
    'group-data-[suffix-icon]:!pr-0': true,
    'data-[placeholder]:text-placeholder': true,
    'data-[placeholder]:!font-regular': true,
    'outline-none': true,
    'focus-visible:ring-2': true,
    'focus-visible:ring-(--color-stroke-accent)': true,
    'focus-visible:ring-offset-2': true,
    'rounded-extra-small': true,
    'border-neutral': true,
    'border': true,
    'cursor-pointer': true,
    'group-data-[expanded]:rounded-b-none': true,
    'group-data-[expanded]:border-accent': true,
    'group-data-[errors=true]:border-alert': true,
    'group-data-[disabled=true]:opacity-50': true,
    'group-data-[disabled=true]:bg-disabled': true,
  },
  'dropdown__placeholder': {
    'text-placeholder': true,
    'body-2': true,
    '!font-normal': true,
    'grow': true,
  },
  'dropdown__optionLoading': {
    '': true,
  },
  'dropdown__selectionWrapper': {
    '': true,
  },
  'dropdown__selection': {
    '': true,
  },
  'dropdown__selectionsWrapper': {
    '': true,
  },
  'dropdown__selections': {
    '': true,
  },
  'dropdown__selectionsItem': {
    '': true,
  },
  'dropdown__truncationCount': {
    '': true,
  },
  'dropdown__tagsWrapper': {
    '': true,
  },
  'dropdown__tags': {
    '': true,
  },
  'dropdown__tagWrapper': {
    '': true,
  },
  'dropdown__tag': {
    '': true,
  },
  'dropdown__tagLabel': {
    '': true,
  },
  'dropdown__removeSelection': {
    '': true,
  },
  'dropdown__selectIcon': {
    '': true,
  },
  'dropdown__dropdownWrapper': {
    '': true,
  },
  'dropdown__listbox': {
    '': true,
  },
  'dropdown__emptyMessage': {
    '': true,
  },
  'dropdown__emptyMessageInner': {
    '': true,
  },
  'dropdown__listItem': {
    '': true,
  },
  'dropdown__selectedIcon': {
    'order-1': true,
    'ml-auto': true,
    'mr-1': true,
    '*:w-4': true,
    '*:h-4': true,
    'flex': true,
    'justify-center': true,
    'w-6': true,
    'text-success': true,
  },
  'dropdown__option': {
    '': true,
  },
  'dropdown__help': {
    '': true,
  },
  'dropdown__messages': {
  },
  'dropdown__message': {
    '': true,
  },
  'mask__outer': {
    '': true,
  },
  'mask__wrapper': {
    '': true,
  },
  'mask__label': {
    '': true,
  },
  'mask__inner': {
    '': true,
  },
  'mask__prefixIcon': {
    '': true,
  },
  'mask__suffixIcon': {
    '': true,
  },
  'mask__input': {
    '': true,
  },
  'mask__overlay': {
    '': true,
  },
  'mask__overlayInner': {
    '': true,
  },
  'mask__overlayPlaceholder': {
    '': true,
  },
  'mask__overlayChar': {
    '': true,
  },
  'mask__overlayEnum': {
    '': true,
  },
  'mask__overlayLiteral': {
    '': true,
  },
  'mask__help': {
    '': true,
  },
  'mask__messages': {
    '': true,
  },
  'mask__message': {
    '': true,
  },
  'rating__outer': {
    'group': true,
    'flex-grow': true,
    'max-w-[20em]': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'rating__wrapper': {
    'mb-1.5': true,
  },
  'rating__label': {
    'block': true,
    'text-neutral-900': true,
    'text-sm': true,
    'mb-1': true,
  },
  'rating__inner': {
    'flex': true,
    'w-[130px]': true,
    'items-center': true,
    'relative': true,
  },
  'rating__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    '-ml-1': true,
    'mr-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    '[&>svg]:w-full': true,
  },
  'rating__suffixIcon': {
    'flex': true,
    'items-center': true,
    '-mr-1': true,
    'ml-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
  },
  'rating__itemsWrapper': {
    'relative': true,
    'inline-flex': true,
    'focus:outline-2': true,
    'focus:outline-neutral-900': true,
  },
  'rating__onItemRow': {
    'h-full': true,
    'w-full': true,
  },
  'rating__offItemRow': {
    'h-full': true,
    'w-full': true,
  },
  'rating__onItemWrapper': {
    '[&>*]:w-full': true,
    '[&>*]:h-full': true,
    'w-full': true,
    'h-full': true,
    'text-yellow-400': true,
  },
  'rating__offItemWrapper': {
    '[&>*]:w-full': true,
    '[&>*]:h-full': true,
    'w-full': true,
    'h-full': true,
    'text-neutral-400': true,
  },
  'rating__ratingItem': {
    'relative': true,
    'focus-within:outline': true,
    'focus-within:outline-2': true,
    'focus-within:outline-neutral-900': true,
    'w-[1.5em]': true,
    'h-[1.5em]': true,
  },
  'rating__itemLabelInner': {
    'h-px': true,
    'w-px': true,
    'overflow-hidden': true,
    'absolute': true,
    'white-space-nowrap': true,
  },
  'rating__itemLabel': {
    'absolute': true,
    'h-full': true,
  },
  'rating__ratingIcon': {
    'w-[1.5em]': true,
    'h-[1.5em]': true,
    'flex': true,
  },
  'rating__input': {
    'appearance-none': true,
    '[color-scheme:light]': true,
    'placeholder:text-neutral-400': true,
    'outline-none': true,
  },
  'rating__messages': {
    'mt-1.5': true,
  },
  'repeater__outer': {
    'flex-grow': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'group/repeater': true,
    'max-w-full': true,
  },
  'repeater__fieldset': {
    'min-w-0': true,
  },
  'repeater__legend': {
    'block': true,
    'text-neutral-900': true,
    'text-sm': true,
    'mb-2': true,
  },
  'repeater__help': {
    'text-neutral-900': true,
    'text-xs': true,
    'mb-2': true,
    '-mt-1': true,
  },
  'repeater__inner': {
    '': true,
  },
  'repeater__items': {
    '': true,
  },
  'repeater__item': {
    'flex': true,
    'w-full': true,
    'mb-2': true,
    'border': true,
    'border-neutral-900': true,
    'rounded-none': true,
    '[&.formkit-dropZone]:opacity-30': true,
  },
  'repeater__dragHandleWrapper': {
    'relative': true,
    'w-8': true,
    'rounded-none': true,
    'rounded-tr-none': true,
    'rounded-br-none': true,
  },
  'repeater__dragHandle': {
    'w-full': true,
    'h-full': true,
    'flex': true,
    'absolute': true,
    'top-0': true,
    'left-0': true,
    'cursor-grab': true,
    'active:cursor-grabbing': true,
  },
  'repeater__dragHandleIcon': {
    'w-2': true,
    'm-auto': true,
    'text-neutral-500': true,
    '[&>svg>path]:fill-current': true,
  },
  'repeater__content': {
    'min-w-0': true,
    'grow': true,
    'p-5': true,
    'flex': true,
    'flex-col': true,
    'align-center': true,
    '[&>div[data-type]]:max-w-none': true,
    '[&>div[data-type]:last-child]:mb-0': true,
  },
  'repeater__controls': {
    'flex': true,
    'flex-col': true,
    'items-center': true,
    'justify-center': true,
    'p-2': true,
    '[&>li]:w-[1.5em]': true,
    '[&>li]:h-[1.5em]': true,
    '[&>li]:my-1': true,
    '[&>li]:mx-auto': true,
    '[&>li]:flex': true,
    '[&>li]:items-center': true,
    '[&>li]:appearance-none': true,
    '[&>li]:justify-center': true,
    '[&>li]:aspect-[1/1]': true,
    '[&>li]:text-neutral-500': true,
    '[&>li:hover]:text-neutral-600': true,
    'group-data-[disabled]/repeater:[&>li_button]:opacity-50': true,
    'group-data-[disabled]/repeater:[&>li_button]:!text-neutral-500': true,
    'group-data-[disabled]/repeater:[&>li_button]:pointer-events-none': true,
  },
  'repeater__upControl': {
    '': true,
  },
  'repeater__removeControl': {
    '': true,
  },
  'repeater__insertControl': {
    '': true,
  },
  'repeater__downControl': {
    '': true,
  },
  'repeater__controlLabel': {
    'absolute': true,
    'opacity-0': true,
    'pointer-events-none': true,
    'text-[0px]': true,
  },
  'repeater__moveDownIcon': {
    'block': true,
    'w-[0.75em]': true,
    'aspect-[1/1]': true,
  },
  'repeater__moveUpIcon': {
    'block': true,
    'w-[0.75em]': true,
    'aspect-[1/1]': true,
  },
  'repeater__removeIcon': {
    'block': true,
    'w-[1.25em]': true,
    'aspect-[1/1]': true,
  },
  'repeater__addIcon': {
    'block': true,
    'w-[1.25em]': true,
    'aspect-[1/1]': true,
  },
  'repeater__addButton': {
    '!mb-0': true,
    'group-data-[disabled]/repeater:pointer-events-none': true,
    'group-data-[disabled]/repeater:opacity-50': true,
    'group-data-[disabled]/repeater:grayscale': true,
  },
  'repeater__messages': {
    '': true,
  },
  'repeater__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'slider__outer': {
    'group': true,
    'flex-grow': true,
    'max-w-[20em]': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'slider__wrapper': {
    'mb-1.5': true,
  },
  'slider__label': {
    'block': true,
    'text-neutral-900': true,
    'text-sm': true,
    'mb-1': true,
  },
  'slider__help': {
    'text-neutral-900': true,
    'text-xs': true,
    'mb-1': true,
  },
  'slider__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    '-ml-1': true,
    'mr-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    '[&>svg]:w-full': true,
    'text-neutral-600': true,
  },
  'slider__suffixIcon': {
    'flex': true,
    'items-center': true,
    '-mr-1': true,
    'ml-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
    'text-neutral-600': true,
  },
  'slider__sliderInner': {
    'flex': true,
    'items-center': true,
    '[&>[data-type=number]]:mb-0': true,
    '[&>[data-type=number]]:ml-3': true,
    '[&>[data-type=number]]:shrink': true,
    '[&>[data-type=number]]:grow-0': true,
    '[&[data-has-mark-labels=true]_[id^=track]]:mb-3': true,
  },
  'slider__track': {
    'grow': true,
    'relative': true,
    'z-20': true,
    'py-2.5': true,
    'select-none': true,
  },
  'slider__chart': {
    'relative': true,
    'w-full': true,
    'flex': true,
    'items-center': true,
    'justify-between': true,
    'aspect-[3/1]': true,
    'mb-2': true,
    'z-20': true,
  },
  'slider__chartBar': {
    'absolute': true,
    'bottom-0': true,
    'h-full': true,
    'bg-neutral-400': true,
    'data-[active=true]:bg-neutral-900': true,
  },
  'slider__trackWrapper': {
    'rounded-none': true,
    'bg-neutral-200': true,
    'px-1': true,
  },
  'slider__trackInner': {
    'relative': true,
    'h-1.5': true,
  },
  'slider__fill': {
    'h-full': true,
    'rounded-none': true,
    'absolute': true,
    'top-0': true,
    '-mx-1': true,
    'bg-neutral-900': true,
  },
  'slider__marks': {
    'absolute': true,
    'pointer-events-none': true,
    'inset-0': true,
  },
  'slider__mark': {
    'absolute': true,
    'top-1/2': true,
    'w-[3px]': true,
    'h-[3px]': true,
    'rounded-full': true,
    '-translate-x-1/2': true,
    '-translate-y-1/2': true,
    'bg-neutral-900': true,
    'data-[active=true]:bg-white': true,
  },
  'slider__markLabel': {
    'absolute': true,
    'top-[calc(100%+0.5em)]': true,
    'left-1/2': true,
    'text-neutral-900': true,
    'text-xs': true,
    '-translate-x-1/2': true,
  },
  'slider__handles': {
    '': true,
  },
  'slider__handle': {
    'group/handle': true,
    'select-none': true,
    'w-4': true,
    'h-4': true,
    'rounded-full': true,
    'bg-white': true,
    'absolute': true,
    'top-1/2': true,
    'left-0': true,
    'z-30': true,
    '-translate-x-1/2': true,
    '-translate-y-1/2': true,
    'border-2': true,
    'border-neutral-900': true,
    'focus-visible:outline-0': true,
    'focus-visible:ring-2': true,
    'data-[is-target=true]:z-20': true,
  },
  'slider__handleInner': {
    '': true,
  },
  'slider__tooltip': {
    'absolute': true,
    'bottom-full': true,
    'left-1/2': true,
    '-translate-x-1/2': true,
    '-translate-y-[4px]': true,
    'bg-neutral-900': true,
    'text-white': true,
    'py-1': true,
    'px-1.5': true,
    'text-xs': true,
    'leading-none': true,
    'whitespace-nowrap': true,
    'rounded-none': true,
    'opacity-0': true,
    'transition-opacity': true,
    'group-hover:opacity-100': true,
    'group-focus-visible/handle:opacity-100': true,
    'group-data-[show-tooltip=true]/handle:opacity-100': true,
    'after:content-[""]': true,
    'after:absolute': true,
    'after:top-full': true,
    'after:left-1/2': true,
    'after:-translate-x-1/2': true,
    'after:-translate-y-[1px]': true,
    'after:border-4': true,
    'after:border-transparent': true,
    'after:border-t-neutral-900': true,
  },
  'slider__linkedValues': {
    'flex': true,
    'items-start': true,
    'justify-between': true,
  },
  'slider__minValue': {
    'relative': true,
    'grow': true,
    '!max-w-[45%]': true,
    'mb-0': true,
    '[&>div::after]:content-[""]': true,
    '[&>div::after]:absolute': true,
    '[&>div::after]:top-1/2': true,
    '[&>div::after]:left-[105%]': true,
    '[&>div::after]:-translate-y-1/2': true,
    '[&>div::after]:w-[12%]': true,
    '[&>div::after]:h-[1px]': true,
    '[&>div::after]:bg-neutral-900': true,
  },
  'slider__maxValue': {
    'relative': true,
    'grow': true,
    '!max-w-[45%]': true,
    'mb-0': true,
  },
  'slider__messages': {
    '': true,
  },
  'slider__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'taglist__outer': {
    'group': true,
    'flex-grow': true,
    'max-w-[20em]': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
  },
  'taglist__wrapper': {
    'mb-1.5': true,
  },
  'taglist__label': {
    'block': true,
    'text-neutral-900': true,
    'text-sm': true,
    'mb-1': true,
  },
  'taglist__inner': {
    'py-2': true,
    'pr-0': true,
    'pl-0': true,
  },
  'taglist__prefixIcon': {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    '-ml-1': true,
    'mr-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    '[&>svg]:w-full': true,
  },
  'taglist__suffixIcon': {
    'flex': true,
    'items-center': true,
    '-mr-1': true,
    'ml-2': true,
    'text-base': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
  },
  'taglist__tags': {
    'pl-3': true,
  },
  'taglist__tagWrapper': {
    '[&.formkit-dropZone_.formkit-tag]:opacity-50': true,
    '[&.formkit-touchDropZone_.formkit-tag]:opacity-50': true,
  },
  'taglist__tag': {
    '': true,
  },
  'taglist__tagLabel': {
    '': true,
  },
  'taglist__removeSelection': {
    '': true,
  },
  'taglist__input': {
    'appearance-none': true,
    'outline-none': true,
    '[color-scheme:light]': true,
    'placeholder:text-neutral-400': true,
    '!p-0': true,
    '!w-[0%]': true,
    'min-w-[1em]': true,
    'inline-block': true,
    'mb-1': true,
  },
  'taglist__loaderIcon': {
    'animate-spin': true,
    'flex': true,
    'items-center': true,
    'my-auto': true,
    'ml-2': true,
    'text-base': true,
    'text-neutral-900': true,
    'h-[1em]': true,
    'w-[1em]': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
  },
  'taglist__listboxButton': {
    'ml-auto': true,
    'shrink-0': true,
  },
  'taglist__selectIcon': {
    '': true,
  },
  'taglist__dropdownWrapper': {
    '': true,
  },
  'taglist__listbox': {
    '': true,
  },
  'taglist__emptyMessage': {
    '': true,
  },
  'taglist__emptyMessageInner': {
    '': true,
  },
  'taglist__listItem': {
    '': true,
  },
  'taglist__selectedIcon': {
    '': true,
  },
  'taglist__option': {
    '': true,
  },
  'taglist__help': {
    'text-neutral-900': true,
    'text-xs': true,
  },
  'taglist__messages': {
    '': true,
  },
  'taglist__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'toggle__outer': {
    'group': true,
    'flex-grow': true,
    'min-w-0': true,
    'text-base': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
    'max-w-none': true,
  },
  'toggle__wrapper': {
    'flex': true,
    'flex-wrap': true,
    'items-center': true,

    '**:cursor-pointer': true,
  },
  'toggle__altLabel': {
    'block': true,
    'w-full': true,
    'mb-1.5': true,
    'text-xs': true,
    'text-neutral': true,
  },
  'toggle__inner': {
    'inline-block': true,
    'mr-2': true,
  },
  'toggle__input': {
    'appearance-none': true,
    'outline-none': true,
    'peer': true,
    'absolute': true,
    'opacity-0': true,
    'w-0': true,
    'h-0': true,
  },
  'toggle__track': {
    'relative': true,
    'min-w-[3.5em]': true,
    'p-0.5': true,
    'select-none': true,
    'rounded-full': true,
    'transition-all': true,
    'bg-neutral-400': true,
    'border-2': true,
    'border-disabled': true,
    'peer-focus-visible:ring-2': true,
    'peer-focus-visible:ring-(--color-text-info)': true,
    'peer-focus-visible:ring-offset-2': true,
    'peer-checked:bg-accent': true,
    'peer-checked:shadow-sm': true,
    'peer-checked:shadow-states-azure-1000': true,
    'peer-checked:border-accent': true,
    'peer-checked:[&>div:last-child]:!bg-white': true,
    'peer-checked:[&>div:last-child]:left-full': true,
    'peer-checked:[&>div:last-child]:-translate-x-full': true,
    'peer-checked:[&>div:first-child:not(:last-child)]:left-0': true,
    'peer-checked:[&>div:first-child:not(:last-child)]:translate-x-0': true,
  },
  'toggle__innerLabel': {
    'absolute': true,
    'text-white': true,
    'text-[10px]': true,
    'left-full': true,
    'top-1/2': true,
    '-translate-x-full': true,
    '-translate-y-1/2': true,
    'px-1': true,
  },
  'toggle__thumb': {
    'relative': true,
    'w-[42%]': true,
    'aspect-[1/1]': true,
    'p-0.5': true,
    'left-0': true,
    'rounded-full': true,
    'transition-all': true,
    'bg-(--color-icon-disabled)': true,
    'text-neutral': true,
  },
  'toggle__thumbIcon': {
    '': true,
  },
  'toggle__valueLabel': {
    'font-bold': true,
    'text-xs': true,
    'text-neutral': true,
  },
  'toggle__label': {
    'block': true,
    'text-neutral': true,
    'body-1': true,

  },
  'toggle__help': {
    'text-neutral-900': true,
    'text-xs': true,
  },
  'toggle__messages': {
    '': true,
  },
  'toggle__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'togglebuttons__options': {
    'group/options': true,
    'inline-flex': true,
    'data-[vertical=true]:flex-col': true,
  },
  'togglebuttons__option': {
    'group/option': true,
  },
  'togglebuttons__input': {
    'appearance-none': true,
    'outline-none': true,
    '[color-scheme:light]': true,
    'placeholder:text-neutral-400': true,
    '!px-4': true,
    'group-data-[vertical=true]/options:w-full': true,
    'justify-center': true,
    'disabled:opacity-50': true,
    'disabled:cursor-not-allowed': true,
    'group-data-[disabled]:disabled:opacity-100': true,
    'aria-[pressed=true]:bg-neutral-900': true,
    'aria-[pressed=true]:text-white': true,
    'group-[]/option:!rounded-none': true,
    'group-data-[vertical=false]/options:group-first/option:!rounded-none': true,
    'group-data-[vertical=true]/options:group-first/option:!rounded-none': true,
    'group-data-[vertical=false]/options:group-first/option:!rounded-tr-none': true,
    'group-data-[vertical=false]/options:group-first/option:!rounded-br-none': true,
    'group-data-[vertical=true]/options:group-first/option:!rounded-bl-none': true,
    'group-data-[vertical=true]/options:group-first/option:!rounded-br-none': true,
    'group-data-[vertical=false]/options:group-last/option:!rounded-none': true,
    'group-data-[vertical=true]/options:group-last/option:!rounded-none': true,
    'group-data-[vertical=false]/options:group-last/option:!rounded-tl-none': true,
    'group-data-[vertical=false]/options:group-last/option:!rounded-bl-none': true,
    'group-data-[vertical=true]/options:group-last/option:!rounded-tl-none': true,
    'group-data-[vertical=true]/options:group-last/option:!rounded-tr-none': true,
    'group-data-[vertical=false]/options:group-[]/option:!border-r-0': true,
    'group-data-[vertical=false]/options:group-last/option:!border-r': true,
    'group-data-[vertical=false]/options:group-[]/option:aria-[pressed=true]:border-x-neutral-100': true,
    'group-data-[vertical=false]/options:group-first/option:aria-[pressed=true]:border-l-neutral-900': true,
    'group-data-[vertical=false]/options:group-last/option:aria-[pressed=true]:border-r-neutral-900': true,
    'group-data-[vertical=true]/options:group-[]/option:!border-b-0': true,
    'group-data-[vertical=true]/options:group-last/option:!border-b': true,
    'group-data-[vertical=true]/options:group-[]/option:aria-[pressed=true]:border-y-neutral-100': true,
    'group-data-[vertical=true]/options:group-first/option:aria-[pressed=true]:border-t-neutral-900': true,
    'group-data-[vertical=true]/options:group-last/option:aria-[pressed=true]:border-b-neutral-900': true,
  },
  'transferlist__outer': {
    'group': true,
    'flex-grow': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
    'max-w-none': true,
    '[&_.dnd-placeholder]:bg-neutral-900': true,
    '&_.dnd-placeholder]:text-white': true,
  },
  'transferlist__fieldset': {
    '': true,
  },
  'transferlist__legend': {
    'block': true,
    'text-neutral-900': true,
    'text-sm': true,
  },
  'transferlist__help': {
    'text-neutral-900': true,
    'text-xs': true,
    'pb-2': true,
  },
  'transferlist__wrapper': {
    'mb-1.5': true,
    'flex': true,
    'flex-col': true,
    'sm:flex-row': true,
    'justify-between': true,
    'w-full': true,
    'max-w-none': true,
  },
  'transferlist__transferlist': {
    'grow': true,
    'shrink': true,
    'min-w-0': true,
    'aspect-[4/5]': true,
    'h-[350px]': true,
    'overflow-hidden': true,
    'flex': true,
    'flex-col': true,
    'border': true,
    'border-neutral-900': true,
    'rounded-none': true,
    'select-none': true,
  },
  'transferlist__transferlistHeader': {
    'flex': true,
    'text-neutral-900': true,
    'text-sm': true,
    'justify-between': true,
    'items-center': true,
    'border-b': true,
    'border-neutral-900': true,
    'py-2': true,
    'px-2.5': true,
  },
  'transferlist__transferlistHeaderLabel': {
    '': true,
  },
  'transferlist__transferlistHeaderItemCount': {
    'ml-auto': true,
    'text-xs': true,
    'text-center': true,
    'text-neutral-900': true,
  },
  'transferlist__transferlistControls': {
    'p-2': true,
    'border-b': true,
    'border-neutral-900': true,
  },
  'transferlist__transferlistSearch': {
    'border': true,
    'rounded-none': true,
    'border-neutral-900': true,
    'text-neutral-900': true,
    'px-2.5': true,
    'py-2': true,
  },
  'transferlist__transferlistSearchInput': {
    'w-full': true,
    'bg-transparent': true,
    'outline-none': true,
    'text-sm': true,
    'cursor-pointer': true,
  },
  'transferlist__transferlistSearchClear': {
    'm-auto': true,
  },
  'transferlist__closeIcon': {
    'ml-2': true,
    'text-neutral-900': true,
    'select-none': true,
    'flex': true,
    'text-base': true,
    '[&>svg]:w-full': true,
    '[&>svg]:max-w-[1em]': true,
  },
  'transferlist__transferlistListItems': {
    'h-full': true,
    'overflow-y-auto': true,
  },
  'transferlist__transferlistListItem': {
    'bg-white': true,
    'py-2': true,
    'px-2': true,
    'text-neutral-900': true,
    'ring-1': true,
    'ring-neutral-900': true,
    'aria-selected:bg-neutral-100': true,
    'data-[is-active=true]:bg-neutral-100': true,
    'relative': true,
    'flex': true,
    'items-center': true,
    'pl-[1.5em]': true,
    '[&.formkit-dropZone]:bg-neutral-100': true,
    '[&.formkit-selectionDropZone]:bg-neutral-100': true,
    '[&.formkit-touchDropZone]:bg-neutral-100': true,
    '[&.formkit-touchSelectionDropZone]:bg-neutral-100': true,
    '[&.formkit-longTouch]:bg-neutral-100': true,
  },
  'transferlist__selectedIcon': {
    'absolute': true,
    'w-[0.75em]': true,
    'left-[0.5em]': true,
    'text-neutral-900': true,
  },
  'transferlist__transferlistOption': {
    'text-sm': true,
  },
  'transferlist__transferListLoadMore': {
    '': true,
  },
  'transferlist__source': {
    '': true,
  },
  'transferlist__sourceHeader': {
    '': true,
  },
  'transferlist__sourceHeaderLabel': {
    '': true,
  },
  'transferlist__sourceHeaderItemCount': {
    '': true,
  },
  'transferlist__sourceControls': {
    '': true,
  },
  'transferlist__sourceSearch': {
    'flex': true,
    'items-center': true,
  },
  'transferlist__sourceSearchInput': {
    '': true,
  },
  'transferlist__sourceSearchClear': {
    '': true,
  },
  'transferlist__sourceListItems': {
    'group-data-[is-max=true]:opacity-50': true,
  },
  'transferlist__sourceListItem': {
    '': true,
  },
  'transferlist__sourceOption': {
    '': true,
  },
  'transferlist__sourceEmptyMessage': {
    'my-2': true,
    'text-center': true,
    'text-neutral-900': true,
  },
  'transferlist__sourceLoadMore': {
    '': true,
  },
  'transferlist__sourceLoadMoreInner': {
    '': true,
  },
  'transferlist__target': {
    '': true,
  },
  'transferlist__targetHeader': {
    '': true,
  },
  'transferlist__targetHeaderLabel': {
    '': true,
  },
  'transferlist__targetHeaderItemCount': {
    '': true,
  },
  'transferlist__targetListItems': {
    '': true,
  },
  'transferlist__targetListItem': {
    '': true,
  },
  'transferlist__targetOption': {
    '': true,
  },
  'transferlist__targetEmptyMessage': {
    'my-2': true,
    'text-center': true,
    'text-neutral-900': true,
  },
  'transferlist__targetLoadMore': {
    '': true,
  },
  'transferlist__emptyMessageInner': {
    'flex': true,
    'items-center': true,
    'justify-center': true,
    'p-2': true,
    'text-sm': true,
  },
  'transferlist__loadMoreInner': {
    'flex': true,
    'text-sm': true,
    'text-neutral-900': true,
    'p-2': true,
    '[&>span]:mr-2': true,
  },
  'transferlist__transferControls': {
    'inline-flex': true,
    'flex-row': true,
    'justify-center': true,
    'grow-0': true,
    'shrink': true,
    'border': true,
    'border-neutral-900': true,
    'rounded-none': true,
    'overflow-clip': true,
    'my-2': true,
    'mx-auto': true,
    'sm:flex-col': true,
    'sm:my-auto': true,
    'sm:mx-2': true,
  },
  'transferlist__controlLabel': {
    'absolute': true,
    'opacity-0': true,
    'pointer-events-none': true,
    'text-[0px]': true,
  },
  'transferlist__transferlistButton': {
    'appearance-none': true,
    'border-none': true,
    'flex': true,
    'justify-center': true,
    'text-sm': true,
    'shrink-0': true,
    'box-content': true,
    'text-neutral-900': true,
    'disabled:!text-neutral-400': true,
    'p-3': true,
    'disabled:opacity-50': true,
  },
  'transferlist__transferButtonForwardAll': {
    '': true,
  },
  'transferlist__transferButtonForward': {
    '': true,
  },
  'transferlist__transferButtonBackward': {
    '': true,
  },
  'transferlist__transferButtonBackwardAll': {
    '': true,
  },
  'transferlist__fastForwardIcon': {
    'w-4': true,
    'flex': true,
    'select-none': true,
    '[&>svg]:m-auto': true,
    '[&>svg]:w-full': true,
    '[&>svg]:max-w-[1rem]': true,
    '[&>svg]:max-h-[1rem]': true,
    'rotate-90': true,
    'sm:rotate-0': true,
  },
  'transferlist__moveRightIcon': {
    'w-4': true,
    'flex': true,
    'select-none': true,
    '[&>svg]:m-auto': true,
    '[&>svg]:w-full': true,
    '[&>svg]:max-w-[1rem]': true,
    '[&>svg]:max-h-[1rem]': true,
    'rotate-90': true,
    'sm:rotate-0': true,
  },
  'transferlist__moveLeftIcon': {
    'w-4': true,
    'flex': true,
    'select-none': true,
    '[&>svg]:m-auto': true,
    '[&>svg]:w-full': true,
    '[&>svg]:max-w-[1rem]': true,
    '[&>svg]:max-h-[1rem]': true,
    'rotate-90': true,
    'sm:rotate-0': true,
  },
  'transferlist__rewindIcon': {
    'w-4': true,
    'flex': true,
    'select-none': true,
    '[&>svg]:m-auto': true,
    '[&>svg]:w-full': true,
    '[&>svg]:max-w-[1rem]': true,
    '[&>svg]:max-h-[1rem]': true,
    'rotate-90': true,
    'sm:rotate-0': true,
  },
  'transferlist__messages': {
    'mt-2': true,
  },
  'transferlist__message': {
    'text-red-600': true,
    'mb-1.5': true,
    'text-xs': true,
  },
  'barcode__barcodeIcon': {
    'w-[1.5em]': true,
    'text-neutral-700': true,
    'cursor-pointer': true,
  },
  'barcode__dialog': {
    'border-none': true,
    'outline-none': true,
    'overflow-clip': true,
    'p-0': true,
    'bg-black': true,
    'rounded-none': true,
    'w-[100%-2rem]': true,
    'max-w-[30rem]': true,
    '[&::backdrop]:bg-neutral-800/50': true,
  },
  'barcode__video': {
    'w-full': true,
    'aspect-[1/1]': true,
    'object-cover': true,
    'block': true,
    'pointer-events-none': true,
  },
  'barcode__closeIcon': {
    'cursor-pointer': true,
    'absolute': true,
    'bg-white': true,
    'color-neutral-700': true,
    'w-[1.5em]': true,
    'h-[1.5em]': true,
    'rounded-none': true,
    'flex': true,
    'top-2': true,
    'right-2': true,
    'z-20': true,
    '[&>svg]:w-[1.25em]': true,
    '[&>svg]:h-[1.25em]': true,
    '[&>svg]:m-auto': true,
  },
  'barcode__overlay': {
    'text-neutral-900': true,
    'absolute': true,
    'top-1/2': true,
    'left-1/2': true,
    'w-[min(20em,75%)]': true,
    'aspect-[1/1]': true,
    '-translate-x-1/2': true,
    '-translate-y-1/2': true,
    'rounded-none': true,
    'pointer-events-none': true,
    'shadow-[0_0_0_999em_rgba(0,0,0,0.5)]': true,
  },
  'barcode__overlayDecorators': {
    'absolute': true,
    'inset-0': true,
    'z-10': true,
  },
  'barcode__overlayDecoratorTopLeft': {
    'absolute': true,
    'w-[1.5rem]': true,
    'h-[1.5rem]': true,
    'rounded-none': true,
    'top-0': true,
    'left-0': true,
    'border-l-4': true,
    'border-t-4': true,
    'rounded-tr-none': true,
    'rounded-bl-none': true,
  },
  'barcode__overlayDecoratorTopRight': {
    'absolute': true,
    'w-[1.5rem]': true,
    'h-[1.5rem]': true,
    'rounded-none': true,
    'top-0': true,
    'right-0': true,
    'border-r-4': true,
    'border-t-4': true,
    'rounded-tl-none': true,
    'rounded-br-none': true,
  },
  'barcode__overlayDecoratorBottomRight': {
    'absolute': true,
    'w-[1.5rem]': true,
    'h-[1.5rem]': true,
    'rounded-none': true,
    'bottom-0': true,
    'right-0': true,
    'border-r-4': true,
    'border-b-4': true,
    'rounded-tr-none': true,
    'rounded-bl-none': true,
  },
  'barcode__overlayDecoratorBottomLeft': {
    'absolute': true,
    'w-[1.5rem]': true,
    'h-[1.5rem]': true,
    'rounded-none': true,
    'bottom-0': true,
    'left-0': true,
    'border-l-4': true,
    'border-b-4': true,
    'rounded-tl-none': true,
    'rounded-br-none': true,
  },
  'multi-step__outer': {
    'group': true,
    'flex-grow': true,
    'min-w-0': true,
    'text-base': true,
    'mb-4': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:opacity-50': true,
    'data-[disabled]:pointer-events-none': true,
    'group/multistep': true,
    'max-w-[32rem]': true,
  },
  'multi-step__wrapper': {
    'mb-1.5': true,
    'group/wrapper': true,
    'data-[tab-style=tab]:rounded-none': true,
  },
  'multi-step__tabs': {
    'flex': true,
    'items-center': true,
    'group-data-[tab-style=tab]/wrapper:overflow-auto': true,
    'group-data-[tab-style=tab]/wrapper:border': true,
    'group-data-[tab-style=tab]/wrapper:border-b-0': true,
    'group-data-[tab-style=tab]/wrapper:border-neutral-900': true,
    'group-data-[tab-style=tab]/wrapper:rounded-none': true,
    'group-data-[tab-style=tab]/wrapper:rounded-bl-none': true,
    'group-data-[tab-style=tab]/wrapper:rounded-br-none': true,
    'group-data-[tab-style=progress]/wrapper:my-6': true,
    'group-data-[tab-style=progress]/wrapper:justify-around': true,
    'group-data-[tab-style=progress]/wrapper:overflow-visible': true,
    'group-data-[tab-style=progress]/wrapper:group-data-[hide-labels=true]/wrapper:mb-3.5': true,
  },
  'multi-step__tab': {
    'group/tab': true,
    'group-data-[tab-style=tab]/wrapper:relative': true,
    'group-data-[tab-style=tab]/wrapper:flex': true,
    'group-data-[tab-style=tab]/wrapper:grow': true,
    'group-data-[tab-style=tab]/wrapper:text-sm': true,
    'group-data-[tab-style=tab]/wrapper:items-center': true,
    'group-data-[tab-style=tab]/wrapper:justify-center': true,
    'group-data-[tab-style=tab]/wrapper:cursor-pointer': true,
    'group-data-[tab-style=tab]/wrapper:text-neutral-700': true,
    'group-data-[tab-style=tab]/wrapper:bg-neutral-50': true,
    'group-data-[tab-style=tab]/wrapper:py-3.5': true,
    'group-data-[tab-style=tab]/wrapper:px-4': true,
    'group-data-[tab-style=tab]/wrapper:border-r': true,
    'group-data-[tab-style=tab]/wrapper:border-b': true,
    'group-data-[tab-style=tab]/wrapper:border-neutral-900': true,
    'group-data-[tab-style=tab]/wrapper:last:border-r-0': true,
    'group-data-[tab-style=tab]/wrapper:data-[active=true]:bg-white': true,
    'group-data-[tab-style=tab]/wrapper:data-[active=true]:border-b-transparent': true,
    'group-data-[tab-style=tab]/wrapper:data-[active=true]:z-10': true,
    'group-data-[tab-style=progress]/wrapper:flex': true,
    'group-data-[tab-style=progress]/wrapper:flex-col': true,
    'group-data-[tab-style=progress]/wrapper:items-center': true,
    'group-data-[tab-style=progress]/wrapper:grow': true,
    'group-data-[tab-style=progress]/wrapper:shrink-0': true,
    'group-data-[tab-style=progress]/wrapper:relative': true,
    'group-data-[tab-style=progress]/wrapper:before:block': true,
    'group-data-[tab-style=progress]/wrapper:before:text-sm': true,
    'group-data-[tab-style=progress]/wrapper:before:w-[1.25rem]': true,
    'group-data-[tab-style=progress]/wrapper:before:h-[1.25rem]': true,
    'group-data-[tab-style=progress]/wrapper:before:border-4': true,
    'group-data-[tab-style=progress]/wrapper:before:border-neutral-300': true,
    'group-data-[tab-style=progress]/wrapper:before:rounded-full': true,
    'group-data-[tab-style=progress]/wrapper:before:bg-white': true,
    'group-data-[tab-style=progress]/wrapper:before:z-10': true,
    'group-data-[tab-style=progress]/wrapper:after:block': true,
    'group-data-[tab-style=progress]/wrapper:after:h-1': true,
    'group-data-[tab-style=progress]/wrapper:after:w-full': true,
    'group-data-[tab-style=progress]/wrapper:after:absolute': true,
    'group-data-[tab-style=progress]/wrapper:after:top-[0.5em]': true,
    'group-data-[tab-style=progress]/wrapper:after:left-[calc(50%+0.5em)]': true,
    'group-data-[tab-style=progress]/wrapper:after:bg-neutral-300': true,
    'group-data-[tab-style=progress]/wrapper:data-[valid=true]:data-[visited=true]:after:bg-neutral-900': true,
    'group-data-[tab-style=progress]/wrapper:last:after:hidden': true,
  },
  'multi-step__tabLabel': {
    'group-data-[tab-style=progress]/wrapper:absolute': true,
    'group-data-[tab-style=progress]/wrapper:text-neutral-800': true,
    'group-data-[tab-style=progress]/wrapper:top-full': true,
    'group-data-[tab-style=progress]/wrapper:w-full': true,
    'group-data-[tab-style=progress]/wrapper:whitespace-nowrap': true,
    'group-data-[tab-style=progress]/wrapper:text-xs': true,
  },
  'multi-step__badge': {
    'bg-neutral-900': true,
    'absolute': true,
    'font-mono': true,
    'font-bold': true,
    'flex': true,
    'items-center': true,
    'justify-center': true,
    'aspect-[1/1]': true,
    '[line-height:1.25rem]': true,
    'text-white': true,
    'rounded-full': true,
    'group-data-[tab-style=tab]/wrapper:text-[0.66rem]': true,
    'group-data-[tab-style=tab]/wrapper:p-1.5': true,
    'group-data-[tab-style=tab]/wrapper:w-5': true,
    'group-data-[tab-style=tab]/wrapper:h-5': true,
    'group-data-[tab-style=tab]/wrapper:top-1.5': true,
    'group-data-[tab-style=tab]/wrapper:right-1.5': true,
    'group-data-[tab-style=progress]/wrapper:w-[1.25rem]': true,
    'group-data-[tab-style=progress]/wrapper:h-[1.25rem]': true,
    'group-data-[tab-style=progress]/wrapper:p-1': true,
    'group-data-[tab-style=progress]/wrapper:text-[10px]': true,
    'group-data-[tab-style=progress]/wrapper:[line-height:0]': true,
    'group-data-[tab-style=progress]/wrapper:z-10': true,
  },
  'multi-step__validStepIcon': {
    'w-full': true,
    'h-full': true,
    'mt-0.5': true,
  },
  'multi-step__steps': {
    'px-10': true,
    'pt-8': true,
    'pb-4': true,
    'bg-white': true,
    'border': true,
    'border-neutral-900': true,
    'rounded-none': true,
    'group-data-[tab-style=tab]/wrapper:border-t-0': true,
    'group-data-[tab-style=tab]/wrapper:rounded-tl-none': true,
    'group-data-[tab-style=tab]/wrapper:rounded-tr-none': true,
    '[&_[data-type]]:max-w-none': true,
  },
  'step__stepActions': {
    'flex': true,
    'justify-between': true,
    '[&>*]:grow-0': true,
  },
  'step__stepPrevious': {
    'mr-1.5': true,
  },
  'step__stepNext': {
    'ml-auto': true,
  },
}

/**
 * Globals are merged prior to generating this file — these are included for
 * any other non-matching inputs.
 **/
const globals: Record<string, Record<string, boolean>> = {
  outer: {
    'group': true,
    'flex-grow': true,
    'data-[disabled]:select-none': true,
    'data-[disabled]:pointer-events-none': true,
  },
  wrapper: {
    '': true,
  },
  label: {
    'block': true,
    'body-2': true,
    'text-neutral': true,
  },
  legend: {
    '': true,
  },
  input: {
    'appearance-none': true,
    'outline-none': true,
    '[color-scheme:light]': true,
    'text-neutral body-1': true,
  },
  placeholder: {
    'body-2': true,
    'text-placeholder': true,
  },
  prefixIcon: {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    'h-5': true,
    'w-5': true,
    '[&>svg]:w-full': true,
  },
  suffixIcon: {
    'flex': true,
    'shrink-0': true,
    'items-center': true,
    'h-5': true,
    'w-5': true,
    '[&>svg]:w-full': true,
  },
  loaderIcon: {
    'animate-spin': true,
    'flex': true,
    'items-center': true,
    'my-auto': true,
    'ml-2': true,
    'body-1': true,
    'text-neutral': true,
    'h-5': true,
    'w-5': true,
    'shrink-0': true,
    '[&>svg]:w-full': true,
  },
  loadMoreInner: {
    'flex': true,
    'body-2': true,
    'text-neutral': true,
    'p-2': true,
    '[&>span]:mr-2': true,
  },
  help: {
    'caption-1': true,
    'text-neutral': true,
  },
  messages: {
    '': true,
  },
  message: {
    'caption-1': true,
    'text-alert': true,
  },
  overlay: {
    'text-neutral': true,
  },
  overlayPlaceholder: {
    'text-neutral': true,
  },
  overlayLiteral: {
    'text-neutral': true,
  },
  overlayChar: {
    'text-neutral': true,
  },
  overlayEnum: {
    'text-neutral': true,
  },
}
