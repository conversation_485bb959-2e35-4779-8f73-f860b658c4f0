// https://nuxt.com/docs/api/configuration/nuxt-config
import { fileURLToPath } from 'url'
import tailwindcss from '@tailwindcss/vite'
import { env } from '../../env'

export default defineNuxtConfig({
  modules: [
    'nuxt-i18n-micro',
    '@nuxtjs/storybook',
    '@digital-retex/twind-design-tokens',
    '@nuxt/icon',
    '@nuxt/test-utils/module',
    'reka-ui/nuxt',
    '@vueuse/nuxt',
  ],
  imports: {
    presets: [
      {
        from: 'tailwind-variants',
        imports: ['tv'],
      },
    ],
  },
  css: [fileURLToPath(new URL('./assets/css/main.css', import.meta.url))],
  alias: {
    '@design-system': fileURLToPath(new URL('./', import.meta.url)),
  },
  vite: {
    plugins: [tailwindcss()],
  },
  i18n: {
    locales: env.SITE === 'modernfold'
      ? [
          { code: 'en', iso: 'en-US' },
          { code: 'es', iso: 'es-ES' },
        ]
      : [
          { code: 'en', iso: 'en-US' },
          { code: 'fr', iso: 'fr-FR' },
        ],
    defaultLocale: 'en',
    fallbackLocale: 'en',
    translationDir: 'locales',
    meta: true,
    strategy: 'prefix',
    disableWatcher: true,
    disablePageLocales: true,
  },
  icon: {
    provider: 'server',
    customCollections: [
      {
        prefix: 'my-icons',
        dir: fileURLToPath(new URL('./assets/icons', import.meta.url)),
        normalizeIconName: false,
      },
    ],
    ...(process.env.STORYBOOK_BUILD === 'true'
      ? {
          clientBundle: {
            includeCustomCollections: true,
            sizeLimitKb: 10_000,
          },
        }
      : {}),
  },
  tailwindTokens: {
    cwd: fileURLToPath(new URL('./', import.meta.url)),
    output: {
      tailwindImport: false,
      destination: 'assets/css/tokens.css',
      transforms: [
        { from: 'spacing-gap', to: 'gap' },
        { from: 'spacing-margin', to: 'grid-system-spacing-x' },
        { from: 'spacing-gutter', to: 'grid-system-gutter-x' },
        { from: 'grids-columns', to: 'grid-system-columns' },
        { from: 'color-stroke', to: 'outline-color' },
      ],
      filters: [
        'primitive/typography/*',
        'primitive/spacing',
        'primitive/radius',
        'primitive/border-width',
        'primitive/bianco',
      ],
    },
    tokens: {
      theme: {
        isMultiTheme: true,
        default: 'modernfold',
      },
      breakpoint: {
        screens: {
          sm: 320,
          md: 768,
          lg: 1024,
          xl: 1440,
        },
      },
    },
  },
})
