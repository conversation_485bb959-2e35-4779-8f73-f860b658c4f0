export type Category = {
  id: string
  name: string
  current: number
  total: number
  suggestions: Suggestion[]
}

export type FileExtension = 'DOCX' | 'DWG' | 'PDF' | 'PNG' | 'JPEG'

export type Suggestion = {
  id: string
  name: string
  description: string
  tag?: string
  to: string
  prefixIcon?: `Ext${FileExtension}Icon` | 'GenFolderIcon'
}

export type SuggestionsType = {
  results_number: {
    current: number
    total: number
  }
  categories: Category[]
}

export const searchSuggestions: SuggestionsType = {
  results_number: {
    current: 6,
    total: 2345000,
  },
  categories: [
    {
      id: 'cat-1',
      name: 'Category',
      current: 6 / 2,
      total: 2345000 / 2,
      suggestions: [
        { id: 'item-1', name: 'Item 1', description: 'Description for Item 1', tag: 'education', to: '#' },
        { id: 'item-2', name: 'Item 2', description: 'Description for Item 2', tag: 'finance', to: '#' },
        { id: 'item-3', name: 'Item 3', description: 'Description for Item 3', tag: 'health', to: '#' },
      ],
    },
    {
      id: 'cat-2',
      name: 'Different Category',
      current: 6 / 2,
      total: 2345000 / 2,
      suggestions: [
        { id: 'item-2345', name: 'Item 1', description: 'Description for Item 1', to: '#', prefixIcon: 'ExtDOCXIcon' },
        { id: 'item-2346', name: 'Item 2', description: 'Description for Item 2', to: '#', prefixIcon: 'ExtPNGIcon' },
        { id: 'item-2347', name: 'Item 3', description: 'Description for Item 3', to: '#', prefixIcon: 'ExtPDFIcon' },
      ],
    },
  ],
}
