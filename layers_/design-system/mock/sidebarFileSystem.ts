import type { MoleculesSidebarFilesNavigationProps } from '@design-system/components/Molecules/MoleculesSidebarFilesNavigation.props'

export const sidebarFileSystem: MoleculesSidebarFilesNavigationProps = {
  parentIds: [],
  folders: [
    {
      id: 'company',
      title: 'Company',
      subtitle: 'Internal resources',
      folders: [
        {
          id: 'hr',
          title: 'Human Resources',
          subtitle: 'Policies and onboarding',
          folders: [
            {
              id: 'onboarding',
              title: 'Onboarding',
              folders: [
                {
                  id: 'welcome-pack',
                  title: 'Welcome Pack',
                  subtitle: 'First day essentials',
                },
                {
                  id: 'training-materials',
                  title: 'Training Materials',
                  folders: [
                    {
                      id: 'compliance',
                      title: 'Compliance',
                    },
                    {
                      id: 'security',
                      title: 'Security Training',
                    },
                    {
                      id: 'culture',
                      title: 'Company Culture',
                      folders: [
                        {
                          id: 'events',
                          title: 'Events',
                          folders: [
                            {
                              id: '2023',
                              title: '2023',
                              folders: [
                                {
                                  id: 'kickoff',
                                  title: 'Kickoff Meeting',
                                },
                                {
                                  id: 'retreat',
                                  title: 'Team Retreat',
                                },
                              ],
                            },
                            {
                              id: '2024',
                              title: '2024',
                              folders: [
                                {
                                  id: 'hackathon',
                                  title: 'Hackathon',
                                },
                              ],
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              ],
            },
            {
              id: 'policies',
              title: 'Policies',
              folders: [
                {
                  id: 'vacation',
                  title: 'Vacation Policy',
                },
                {
                  id: 'remote-work',
                  title: 'Remote Work Policy',
                },
              ],
            },
          ],
        },
        {
          id: 'finance',
          title: 'Finance',
          subtitle: 'Budgets and reports',
          folders: [
            {
              id: 'budgets',
              title: 'Budgets',
              folders: [
                {
                  id: '2023-budget',
                  title: '2023',
                },
                {
                  id: '2024-budget',
                  title: '2024',
                },
              ],
            },
            {
              id: 'reports',
              title: 'Reports',
              folders: [
                {
                  id: 'quarterly',
                  title: 'Quarterly Reports',
                  folders: [
                    {
                      id: 'q1',
                      title: 'Q1',
                    },
                    {
                      id: 'q2',
                      title: 'Q2',
                    },
                  ],
                },
                {
                  id: 'annual',
                  title: 'Annual Reports',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: 'projects',
      title: 'Projects',
      subtitle: 'Active and archived projects',
      folders: [
        {
          id: 'alpha',
          title: 'Alpha',
          folders: [
            {
              id: 'design',
              title: 'Design',
              folders: [
                {
                  id: 'wireframes',
                  title: 'Wireframes',
                },
                {
                  id: 'mockups',
                  title: 'Mockups',
                },
              ],
            },
            {
              id: 'development',
              title: 'Development',
              folders: [
                {
                  id: 'frontend',
                  title: 'Frontend',
                  folders: [
                    {
                      id: 'components',
                      title: 'Components',
                    },
                    {
                      id: 'pages',
                      title: 'Pages',
                    },
                  ],
                },
                {
                  id: 'backend',
                  title: 'Backend',
                },
              ],
            },
            {
              id: 'testing',
              title: 'Testing',
              folders: [
                {
                  id: 'unit-tests',
                  title: 'Unit Tests',
                },
                {
                  id: 'integration-tests',
                  title: 'Integration Tests',
                },
              ],
            },
          ],
        },
        {
          id: 'beta',
          title: 'Beta',
          folders: [
            {
              id: 'planning',
              title: 'Planning',
            },
            {
              id: 'milestones',
              title: 'Milestones',
              folders: [
                {
                  id: 'm1',
                  title: 'Milestone 1',
                },
                {
                  id: 'm2',
                  title: 'Milestone 2',
                  folders: [
                    {
                      id: 'review',
                      title: 'Review',
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          id: 'gamma',
          title: 'Gamma',
          folders: [],
        },
      ],
    },
    {
      id: 'archive',
      title: 'Archive',
      subtitle: 'Old documents',
      folders: [
        {
          id: 'legal',
          title: 'Legal',
          folders: [
            {
              id: 'contracts',
              title: 'Contracts',
            },
            {
              id: 'nda',
              title: 'NDAs',
            },
          ],
        },
        {
          id: 'historical-reports',
          title: 'Historical Reports',
          folders: [
            {
              id: '2019',
              title: '2019',
            },
            {
              id: '2020',
              title: '2020',
              folders: [
                {
                  id: 'q1-2020',
                  title: 'Q1',
                },
                {
                  id: 'q2-2020',
                  title: 'Q2',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: 'empty',
      title: 'Empty Folder Example',
      subtitle: 'No children here',
      folders: [],
    },
  ],
}
