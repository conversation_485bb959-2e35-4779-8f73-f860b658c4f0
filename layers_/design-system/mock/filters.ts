export const filtersPanelMock = [
  {
    type: 'search',
    name: 'searchItems',
    placeholder: 'Search for something',
  },
  {
    type: 'dropdown',
    name: 'selectSomething1',
    label: 'Label',
    placeholder: 'Select something 1',
    options: ['option1', 'option2'],
  },
  {
    type: 'dropdown',
    name: 'selectSomething2',
    label: 'Label',
    placeholder: 'Select something 2',
    options: ['option1', 'option2'],
  },
  {
    type: 'dropdown',
    name: 'selectSomething3',
    label: 'Label',
    placeholder: 'Select something 3',
    options: ['option1', 'option2'],
  },
  {
    type: 'checkbox',
    name: 'checkbox1',
    label: 'Checkbox items list',
    options: [
      { value: false, label: 'Lorem' },
      { value: false, label: 'Lorem' },
    ],
  },
  {
    type: 'group',
    name: 'advanced',
    children: [
      {
        type: 'text',
        name: 'additionalField1',
        label: 'Label',
        placeholder: 'ex: Placeholder',
      },
      {
        type: 'text',
        name: 'additionalField2',
        label: 'Label',
        placeholder: 'ex: Placeholder',
      },
      {
        type: 'text',
        name: 'additionalField3',
        label: 'Label',
        placeholder: 'ex: Placeholder',
      },
      {
        type: 'text',
        name: 'additionalField4',
        label: 'Label',
        placeholder: 'ex: Placeholder',
      },
    ],
  },
]
