import type {
  LanguageItem,
  LanguageConfig,
  LanguageMenuItem,
  LanguageMenuOptions,
  LanguageUtils,
} from '../types/language'

/**
 * Creates a language icon using the provided pattern
 */
export const createLanguageIcon = (
  locale: string,
  pattern: (locale: string) => string = (locale: string) => `circle-flags:${locale}`,
): string => pattern(locale)

/**
 * Creates a translated language label using the provided pattern
 */
export const createLanguageLabel = (
  locale: string,
  ts: (key: string) => string,
  pattern: (locale: string) => string = (locale: string) => `header.language.${locale}`,
): string => ts(pattern(locale))

/**
 * Creates a complete language item with label, value, and icon
 */
export const createLanguageItem = (
  locale: string,
  ts: (key: string) => string,
  config: LanguageConfig = {},
): LanguageItem => {
  const { iconPattern, labelPattern } = config

  return {
    label: createLanguageLabel(locale, ts, labelPattern),
    value: locale,
    icon: createLanguageIcon(locale, iconPattern),
  }
}

/**
 * Creates a menu item from a language item
 */
export const createLanguageMenuItem = (
  language: LanguageItem,
  getUrl: (locale: string) => string,
): LanguageMenuItem => ({
  label: language.label,
  icon: language.icon,
  value: language.value,
  to: getUrl(language.value),
  external: true,
  target: '_self',
})

/**
 * Filters languages based on provided options
 */
export const filterLanguages = (
  languages: LanguageItem[],
  currentLocale: string,
  options: LanguageMenuOptions = {},
): LanguageItem[] => {
  const { includeCurrentLanguage = false, customFilter } = options

  if (customFilter) {
    return languages.filter(lang => customFilter(lang, currentLocale))
  }

  return includeCurrentLanguage
    ? languages
    : languages.filter(lang => lang.value !== currentLocale)
}

/**
 * Creates a locale pattern regex for URL manipulation
 */
export const createLocalePattern = (locales: string[]): RegExp => {
  return new RegExp(`^/(${locales.join('|')})(/|$)`)
}

/**
 * Removes locale from path
 */
export const removeLocaleFromPath = (path: string, locales: string[]): string => {
  const localePattern = createLocalePattern(locales)
  return path.replace(localePattern, '/')
}

/**
 * Complete language utilities object
 */
export const languageUtils: LanguageUtils = {
  createLanguageItem,
  createLanguageMenuItem,
  filterLanguages,
}
