/**
 * Gets the appropriate file icon based on filename extension
 */

const FILE_ICONS: Record<string, string> = {
  // Images
  'jpg': 'ExtJPGIcon',
  'jpeg': 'ExtJPGIcon',
  'png': 'ExtPNGIcon',

  // Documents
  'pdf': 'ExtPDFIcon',
  'ppt': 'ExtPPTIcon',
  'pptx': 'ExtPPTIcon',

  // Spreadsheets
  'xls': 'ExtXSLIcon',
  'xlsx': 'ExtXSLIcon',
  'csv': 'ExtXSLIcon',

  // Videos
  'mp4': 'ExtMP4Icon',
  'mov': 'ExtMP4Icon',
  'avi': 'ExtMP4Icon',

  // Archives
  'zip': 'ExtZIPIcon',
  'rar': 'ExtRARIcon',
  '7z': 'ExtZIPIcon',

  // Other
  'docx': 'ExtDOCXIcon',
  'dwg': 'ExtDWGIcon',
}

const DEFAULT_ICON = 'ExtPDFIcon'

export function getFileIcon(filename: string | null | undefined): string {
  if (!filename) return DEFAULT_ICON

  const ext = filename.split('.').pop()?.toLowerCase() || ''
  return FILE_ICONS[ext] || DEFAULT_ICON
}
