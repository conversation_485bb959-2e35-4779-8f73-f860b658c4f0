export const getSkyfoldLogo = (): string => {
  return `
<svg width="940" height="213" viewBox="0 0 940 213" style="width: 100%; height: auto;" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M409.24 171.92C401.24 171.92 395 178.16 395 186.64C395 195.12 401.24 201.33 409.24 201.33C414.42 201.33 418.24 198.97 420.54 195.39V200.87H423.96V157.24H420.54V177.84C418.24 174.3 414.42 171.9 409.24 171.9V171.92ZM409.63 175.16C416.08 175.16 420.72 180.1 420.72 186.64C420.72 193.18 416.08 198.09 409.63 198.09C403.18 198.09 398.51 193.06 398.51 186.64C398.51 180.22 403.08 175.16 409.63 175.16ZM447.04 171.92C438.59 171.92 432.25 178.25 432.25 186.64C432.25 195.03 438.58 201.33 447.04 201.33C455.5 201.33 461.89 195.03 461.89 186.64C461.89 178.25 455.5 171.92 447.04 171.92ZM447.04 175.16C453.46 175.16 458.34 180.01 458.34 186.64C458.34 193.27 453.46 198.09 447.04 198.09C440.62 198.09 435.77 193.27 435.77 186.64C435.77 180.01 440.65 175.16 447.04 175.16ZM482.45 171.88C478.69 171.88 475.51 173.88 473.66 177.21V172.36H470.27V200.87H473.69V184.78C473.69 179.02 477.08 175.18 481.84 175.18C483.57 175.18 485.51 175.57 486.9 176.33L487.78 172.97C486.23 172.21 484.48 171.88 482.45 171.88ZM530.11 171.92C525.69 171.92 521.17 173.89 519.05 178.56C517.35 174.38 513.29 171.92 507.96 171.92C504.02 171.92 500.02 173.56 497.78 177.37V172.37H494.36V200.88H497.81V184.64C497.81 179.03 501.99 175.19 507.35 175.19C513.17 175.19 516.5 178.7 516.5 184.46V200.88H519.95V184.64C519.95 179.03 524.13 175.19 529.49 175.19C535.31 175.19 538.67 178.7 538.67 184.46V200.88H542.12V183.4C542.12 176.34 537.24 171.92 530.09 171.92H530.11ZM579.21 172.37H575.79V177.85C573.49 174.31 569.67 171.91 564.49 171.91C556.49 171.91 550.25 178.15 550.25 186.63C550.25 195.11 556.49 201.32 564.49 201.32C569.67 201.32 573.49 198.96 575.79 195.38V200.86H579.21V172.35V172.37ZM564.88 175.16C571.33 175.16 575.97 180.1 575.97 186.64C575.97 193.18 571.33 198.09 564.88 198.09C558.43 198.09 553.76 193.06 553.76 186.64C553.76 180.22 558.33 175.16 564.88 175.16ZM592.95 157.25H589.5V200.88H592.95V187.52H597.59L609.8 200.88H614.19L600.37 185.82L613.94 172.37H609.58L597.58 184.4H592.94V157.25H592.95ZM645.87 172.37H642.45V177.85C640.15 174.31 636.33 171.91 631.15 171.91C623.15 171.91 616.91 178.15 616.91 186.63C616.91 195.11 623.15 201.32 631.15 201.32C636.33 201.32 640.15 198.96 642.45 195.38V200.86H645.87V172.35V172.37ZM631.54 175.16C637.99 175.16 642.63 180.1 642.63 186.64C642.63 193.18 637.99 198.09 631.54 198.09C625.09 198.09 620.42 193.06 620.42 186.64C620.42 180.22 624.99 175.16 631.54 175.16ZM670.85 171.92C665.7 171.92 661.88 174.28 659.61 177.8V157.26H656.16V200.89H659.61V195.47C661.88 199.01 665.7 201.35 670.85 201.35C678.88 201.35 685.12 195.14 685.12 186.66C685.12 178.18 678.88 171.94 670.85 171.94V171.92ZM670.46 175.16C676.97 175.16 681.58 180.19 681.58 186.64C681.58 193.09 677.04 198.09 670.46 198.09C663.88 198.09 659.37 193.18 659.37 186.64C659.37 180.1 664.04 175.16 670.46 175.16ZM720.55 172.37H717.13V177.85C714.83 174.31 711.01 171.91 705.83 171.91C697.83 171.91 691.59 178.15 691.59 186.63C691.59 195.11 697.83 201.32 705.83 201.32C711.01 201.32 714.83 198.96 717.13 195.38V200.86H720.55V172.35V172.37ZM706.22 175.16C712.67 175.16 717.31 180.1 717.31 186.64C717.31 193.18 712.67 198.09 706.22 198.09C699.77 198.09 695.1 193.06 695.1 186.64C695.1 180.22 699.67 175.16 706.22 175.16ZM767.64 157.98C755.31 157.98 745.92 166.92 745.92 179.67C745.92 193.15 756.16 201.33 767.46 201.33C780.03 201.33 787.91 191.97 787.91 179.52C787.91 179.19 787.91 178.91 787.85 178.52H769.49V181.97H783.85C783.3 191.67 777.15 197.79 767.46 197.79C757.77 197.79 749.77 190.49 749.77 179.67C749.77 168.85 757.59 161.55 767.65 161.55C773.22 161.55 778.1 163.94 781.34 167.85L784.07 165.21C780.19 160.7 774.31 157.97 767.65 157.97L767.64 157.98ZM809.14 171.88C805.38 171.88 802.2 173.88 800.35 177.21V172.36H796.96V200.87H800.38V184.78C800.38 179.02 803.77 175.18 808.53 175.18C810.26 175.18 812.2 175.57 813.59 176.33L814.47 172.97C812.92 172.21 811.17 171.88 809.14 171.88ZM832.7 171.92C824.25 171.92 817.91 178.25 817.91 186.64C817.91 195.03 824.24 201.33 832.7 201.33C841.16 201.33 847.55 195.03 847.55 186.64C847.55 178.25 841.16 171.92 832.7 171.92ZM832.7 175.16C839.12 175.16 844 180.01 844 186.64C844 193.27 839.12 198.09 832.7 198.09C826.28 198.09 821.43 193.27 821.43 186.64C821.43 180.01 826.31 175.16 832.7 175.16ZM881.01 172.37H877.56V188.79C877.56 194.91 873.68 198.09 868.23 198.09C862.78 198.09 858.9 194.91 858.9 188.79V172.37H855.45V189.09C855.45 197.18 861.21 201.33 868.24 201.33C875.27 201.33 881.03 197.18 881.03 189.09V172.37H881.01ZM905.73 171.92C900.58 171.92 896.76 174.28 894.49 177.8V172.38H891.04V213.01H894.49V195.47C896.76 199.01 900.58 201.35 905.73 201.35C913.76 201.35 920 195.14 920 186.66C920 178.18 913.76 171.94 905.73 171.94V171.92ZM905.34 175.16C911.85 175.16 916.46 180.19 916.46 186.64C916.46 193.09 911.92 198.09 905.34 198.09C898.76 198.09 894.25 193.18 894.25 186.64C894.25 180.1 898.92 175.16 905.34 175.16Z" fill="black"/>
<path d="M870 25.75H830V125.75H870C897.28 125.75 920 102.93 920 75.75C920 48.57 897.28 25.75 870 25.75ZM870 115.75H840V35.75H870C891.83 35.75 910 54.01 910 75.75C910 97.49 891.83 115.75 870 115.75Z" fill="#939598"/>
<path d="M810 125.75H735V25.75H745V115.75H810V125.75Z" fill="#939598"/>
<path d="M717 75.75C717 104.47 693.72 127.75 665 127.75C636.28 127.75 613 104.47 613 75.75C613 47.03 636.28 23.75 665 23.75C693.72 23.75 717 47.03 717 75.75ZM665 33.75C641.8 33.75 623 52.55 623 75.75C623 98.95 641.8 117.75 665 117.75C688.2 117.75 707 98.95 707 75.75C707 52.55 688.2 33.75 665 33.75Z" fill="#939598"/>
<path d="M600 35.75V25.75H520V125.75H530V80.75H595V70.75H530V35.75H600Z" fill="#939598"/>
<path d="M486.34 25.75L455 68.1L423.66 25.75H405L447.5 83.18V125.75H462.5V83.18L505 25.75H486.34Z" fill="#0080C5"/>
<path d="M381.34 125.75H400L356.99 67.63L394.99 25.75H374.73L325 80.57V25.75H310V125.75H325V102.89L346.71 78.96L381.34 125.75Z" fill="#0080C5"/>
<path d="M285 40.75V25.75H223.49C214.36 25.75 205.68 31.12 201.88 40.05C196.81 51.98 202.37 65.77 214.3 70.85L269.83 94.46C274.14 96.29 276.15 101.28 274.32 105.59C272.95 108.82 269.81 110.76 266.51 110.76H205V125.76H266.51C275.64 125.76 284.32 120.39 288.12 111.46C293.19 99.53 287.63 85.74 275.7 80.66L220.17 57.05C215.86 55.22 213.85 50.23 215.68 45.92C217.06 42.69 220.2 40.75 223.51 40.75H285Z" fill="#0080C5"/>
<path d="M940 30.75C940 36.27 935.52 40.75 930 40.75C924.48 40.75 920 36.27 920 30.75C920 25.23 924.48 20.75 930 20.75C935.52 20.75 940 25.23 940 30.75ZM930 22.25C925.31 22.25 921.5 26.06 921.5 30.75C921.5 35.44 925.31 39.25 930 39.25C934.69 39.25 938.5 35.44 938.5 30.75C938.5 26.06 934.69 22.25 930 22.25ZM926 25.75H931.75C933.48 25.75 935 27.27 935 29C935 30.26 934.2 31.4 933.09 31.94L935 35.75H933.32L931.57 32.25H927.5V35.75H926V25.75ZM931.75 30.75C932.65 30.75 933.5 29.9 933.5 29C933.5 28.1 932.65 27.25 931.75 27.25H927.5V30.75H931.75Z" fill="#939598"/>
<path d="M19.51 25.31C27.68 16.32 38.01 9.33 49.67 5.14C50.45 4.86 51.23 4.6 52.02 4.34C51.25 4.61 50.49 4.92 49.75 5.26C46.83 6.62 44.27 8.6 42.54 11.59C39.02 17.67 40.35 25.86 45.4 34.53C51.8 45.52 64.17 57.31 80.27 66.63C91.79 73.3 103.45 77.69 113.81 79.68C126.58 82.13 137.37 80.92 143.48 75.85C144.81 74.74 145.92 73.46 146.78 71.98C148.24 69.46 148.79 66.78 148.76 64.07C148.75 63.07 148.67 62.07 148.52 61.07C148.47 60.75 148.42 60.43 148.36 60.11C148.44 60.55 148.53 60.99 148.6 61.43C149.11 64.3 149.5 67.22 149.74 70.19C149.62 72.06 149.28 73.91 148.69 75.7C141.92 96.2 106.72 103.01 70.08 90.91C33.44 78.81 9.23 52.38 16 31.87C16.79 29.49 17.99 27.31 19.5 25.31H19.51ZM77.88 122.56C112.81 120.13 141.33 104.33 149.43 84.85C149.79 81.87 150 78.84 150 75.76C150 75.29 149.97 74.82 149.96 74.35C149.81 76.14 149.45 77.91 148.88 79.63C141.89 100.79 105.86 107.91 68.41 95.54C30.96 83.16 6.26 55.97 13.25 34.81C13.74 33.31 14.39 31.89 15.17 30.54C5.65 43.12 0 58.77 0 75.75C0 83.21 1.1 90.41 3.13 97.21C14.88 114.18 44.31 124.9 77.88 122.56ZM95.42 140.1C119.92 130.57 138.5 114.47 146.7 97.8C147.52 95.12 148.18 92.37 148.71 89.58C140.79 109.17 112.39 124.91 77.67 127.07C45.22 129.09 16.78 118.64 4.81 102.2C13.2 124.47 31.88 141.67 55.05 148.06C67.64 148.07 81.55 145.51 95.42 140.11V140.1ZM93.78 143.86C83.13 147.48 72.52 149.38 62.56 149.7C66.61 150.38 70.76 150.75 75 150.75C104.6 150.75 130.19 133.59 142.39 108.68C133.12 123.2 115.75 136.38 93.78 143.85V143.86Z" fill="url(#paint0_radial_4052_4006)"/>
<path d="M16 31.87C9.23 52.37 33.44 78.81 70.08 90.91C106.72 103.01 141.92 96.21 148.69 75.7C149.28 73.91 149.62 72.06 149.74 70.19C149.85 71.56 149.92 72.94 149.95 74.33C149.8 76.12 149.44 77.89 148.87 79.61C141.88 100.77 105.85 107.89 68.4 95.52C30.96 83.16 6.26 55.97 13.25 34.81C13.74 33.31 14.39 31.89 15.17 30.54C16.54 28.73 17.98 26.98 19.51 25.31C17.99 27.31 16.79 29.49 16.01 31.87H16ZM95.42 140.1C81.54 145.5 67.64 148.06 55.05 148.05C57.5 148.73 60.01 149.28 62.56 149.71C72.51 149.38 83.12 147.49 93.78 143.87C115.76 136.4 133.12 123.21 142.39 108.7C144.1 105.21 145.55 101.58 146.7 97.81C138.5 114.48 119.92 130.58 95.42 140.11V140.1ZM77.88 122.56C44.31 124.9 14.88 114.19 3.13 97.21C3.63 98.9 4.19 100.56 4.81 102.19C16.79 118.63 45.22 129.07 77.67 127.06C112.39 124.9 140.78 109.16 148.71 89.57C149 88.01 149.24 86.43 149.43 84.84C141.33 104.33 112.81 120.12 77.88 122.55V122.56Z" fill="url(#paint1_radial_4052_4006)"/>
<path d="M146.9 71.78C146.86 71.85 146.83 71.92 146.79 71.99C138.8 85.77 109.07 83.34 80.26 66.66C76.85 64.69 73.62 62.6 70.56 60.43C104.52 49.36 137.12 54.53 146.89 71.78H146.9ZM77.88 28.93C65.7 28.08 54.08 28.96 43.64 31.22C47.64 39.6 55.38 48.65 65.8 56.86C66.66 56.55 67.53 56.25 68.41 55.96C104.42 44.06 139.09 50.2 147.93 69.49C148.43 68.06 148.68 66.6 148.75 65.13C139.83 46.35 111.89 31.31 77.88 28.94V28.93ZM77.67 24.43C112.25 26.58 140.55 42.21 148.6 61.68C148.45 60.5 148.21 59.31 147.91 58.15C147.55 56.74 147.13 55.21 146.63 53.56C138.4 36.94 119.85 20.9 95.42 11.39C81.54 5.99 67.64 3.43 55.04 3.44C54.67 3.54 54.31 3.64 53.96 3.73C49.36 4.99 45.06 7.22 42.53 11.59C39.99 15.98 39.98 21.46 42.06 27.43C52.81 24.77 64.93 23.64 77.66 24.43H77.67ZM93.78 7.63C115.72 15.09 133.06 28.24 142.35 42.73C141.67 41.35 140.91 39.91 140.05 38.42C133.23 26.54 123.65 17.25 112.59 10.86C101.53 4.44 88.71 0.75 75.01 0.75C70.13 0.75 66.02 1.18 62.49 1.79C72.46 2.11 83.1 4.01 93.77 7.64L93.78 7.63Z" fill="url(#paint2_radial_4052_4006)"/>
<path d="M95.42 11.39C81.54 5.99 67.64 3.43 55.04 3.44C57.2 2.86 59.66 2.27 62.49 1.78C72.46 2.1 83.1 4 93.77 7.63C115.71 15.09 133.05 28.24 142.34 42.73C144.35 46.81 145.67 50.41 146.63 53.56C138.4 36.94 119.85 20.9 95.42 11.39ZM148.61 61.69C140.56 42.21 112.26 26.58 77.68 24.44C64.94 23.65 52.83 24.78 42.08 27.44C42.51 28.68 43.04 29.95 43.65 31.23C54.09 28.97 65.72 28.09 77.89 28.94C111.9 31.31 139.84 46.35 148.76 65.13C148.81 63.99 148.76 62.84 148.61 61.69ZM68.41 55.96C67.53 56.25 66.67 56.56 65.8 56.86C67.33 58.07 68.92 59.25 70.56 60.42C104.52 49.35 137.12 54.52 146.89 71.77C147.31 71.02 147.65 70.26 147.92 69.48C139.08 50.19 104.41 44.06 68.4 55.95L68.41 55.96Z" fill="url(#paint3_radial_4052_4006)"/>
<path d="M49.31 5.71999C50.1 5.20999 50.9 4.73999 51.62 4.48999C50.85 4.75999 50.46 4.91999 49.73 5.25999C46.81 6.61999 44.14 8.58999 42.41 11.58C38.89 17.66 40.27 25.93 45.32 34.6C51.72 45.59 64.1 57.4 80.2 66.72C91.72 73.39 103.43 77.8 113.79 79.79C126.56 82.24 137.43 81.01 143.54 75.94C144.87 74.83 145.99 73.54 146.85 72.06C148.31 69.54 148.83 66.78 148.8 64.07C148.79 63.07 148.7 62.24 148.55 61.24C148.5 60.92 148.52 61.05 148.47 60.73C148.57 61.41 148.59 61.74 148.67 62.89C148.82 64.97 148.17 67.25 147.61 68.59C146.75 70.64 145.45 72.94 142.84 75.1C139.52 77.83 133.13 79.93 125.77 79.93C122.13 79.93 118.17 79.52 114 78.72C103.33 76.67 91.85 72.2 80.78 65.79C65.41 56.89 52 44.28 46.27 34.05C44.83 31.48 40.85 24.26 41.81 17.07C42.64 10.89 46.32 7.64999 49.31 5.71999Z" fill="url(#paint4_radial_4052_4006)"/>
<defs>
<radialGradient id="paint0_radial_4052_4006" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(69.58 74.41) scale(94.76 94.76)">
<stop stop-color="white"/>
<stop offset="0.3" stop-color="white"/>
<stop offset="0.6" stop-color="#E9EAEA"/>
<stop offset="1" stop-color="#939598"/>
</radialGradient>
<radialGradient id="paint1_radial_4052_4006" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(65.25 56.25) scale(70.7 70.7)">
<stop offset="0.2" stop-color="#40A0D3"/>
<stop offset="0.6" stop-color="#2693CE"/>
<stop offset="1" stop-color="#0080C5"/>
</radialGradient>
<radialGradient id="paint2_radial_4052_4006" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(0.499984 56.25) scale(70.7 70.7)">
<stop offset="0.2" stop-color="#40A0D3"/>
<stop offset="0.6" stop-color="#2693CE"/>
<stop offset="1" stop-color="#0080C5"/>
</radialGradient>
<radialGradient id="paint3_radial_4052_4006" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(66.25 57) scale(91.19 91.19)">
<stop offset="0.2" stop-color="white"/>
<stop offset="0.6" stop-color="#E9EAEA"/>
<stop offset="1" stop-color="#A9AAAD"/>
</radialGradient>
<radialGradient id="paint4_radial_4052_4006" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(64.34 30.08) scale(73.4 73.4)">
<stop stop-color="#A9AAAD"/>
<stop offset="0.4" stop-color="#E9EAEA"/>
<stop offset="0.8" stop-color="white"/>
</radialGradient>
</defs>
</svg>
`
}
