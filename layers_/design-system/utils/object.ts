export const groupBy = <K extends <PERSON>Key, T>(
  items: Array<T>,
  keySelector: (item: T, index: number) => K,
): Partial<Record<K, T[]>> =>
  items.reduce<Partial<Record<K, T[]>>>(
    (groupedBy, item, index) => {
      const key = keySelector(item, index)
      const previousValues = groupedBy[key] ?? []
      return {
        ...groupedBy,
        [key]: [...previousValues, item],
      }
    },
    {},
  )
