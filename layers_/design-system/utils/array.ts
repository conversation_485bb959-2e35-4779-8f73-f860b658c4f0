export const difference = <T>(arr1: T[], arr2: T[]) => {
  // This function calculates the symmetric difference between two arrays.
  // The symmetric difference is the set of elements that are in either of the arrays but not in both.
  // It uses JavaScript's Set to efficiently check for the presence of elements.

  const set1 = new Set(arr1) // Convert the first array into a Set for quick lookup.
  const set2 = new Set(arr2) // Convert the second array into a Set for quick lookup.

  return [
    // Filter elements in arr1 that are not in arr2 (set2).
    ...arr1.filter(item => !set2.has(item)),
    // Filter elements in arr2 that are not in arr1 (set1).
    ...arr2.filter(item => !set1.has(item)),
  ]
}

export const scan = <T, U>(array: T[], fn: (acc: U, curr: T) => U, initial: U): U[] => {
  const results: U[] = []
  let lastResult = initial
  for (const item of array) {
    lastResult = fn(lastResult, item)
    results.push(lastResult)
  }
  return results
}
