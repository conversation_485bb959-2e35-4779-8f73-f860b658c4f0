import type { InstantSearch, UiState } from 'instantsearch.js'
import type { PortfolioStoryblok } from '@integration/storyblok/data/components'
import type { WithRelationships } from '@integration/types/storyblok'
import type { ISbStoryData } from 'storyblok-js-client'
// @ts-expect-error types are not available for this beta version
import pdfMake from 'pdfmake'

type SearchInstance = InstantSearch<UiState, UiState> | undefined
type CmsData = ISbStoryData<WithRelationships<PortfolioStoryblok>>[]
type AppliedFilters = {
  name: string
  value: string | number | boolean | string[] | number[] | boolean[]
}[]

export default function usePortfolioPdf(searchInstance: Ref<SearchInstance>) {
  const isDownloadingPdf = ref(false)

  const downloadPdfMake = async () => {
    if (!searchInstance.value) return
    isDownloadingPdf.value = true

    const uiState = searchInstance.value?.getUiState()[`skyfold__portfolios__${useI18n().$getLocale()}`]

    const { appliedFilters, cmsData } = await $fetch<{ appliedFilters: AppliedFilters, cmsData: CmsData }>(
      `/api/${useI18n().$getLocale()}/portfolio/pdfData`,
      {
        params: {
          algoliaUIStateStringified: JSON.stringify(uiState),
        },
      },
    )

    const totalWalls = cmsData.reduce((acc, item) => acc + parseInt(item.content.number_of_walls ?? '0'), 0)
    const printFilters = appliedFilters.map(filter => ({ text: `${filter.name}: ${filter.value}` }))

    pdfMake.setFonts({
      Roboto: {
        normal: 'https://cdn.jsdelivr.net/npm/typeface-roboto@1.1.13/files/roboto-latin-400.woff',
        bold: 'https://cdn.jsdelivr.net/npm/typeface-roboto@1.1.13/files/roboto-latin-700.woff',
      },
    })

    const docDefinition = {
      pageMargins: [20, 70, 20, 20],
      header: {
        columns: [
          {
            image: 'logo',
            width: 200,
            margin: [20, 15, 0, 0],
          },
          {
            text: new Date().toLocaleDateString(),
            style: { fontSize: 12, alignment: 'right' },
            margin: [0, 25, 20, 0],
          },
        ],
      },
      footer: (currentPage: number, pageCount: number) => ({
        text: `${currentPage}/${pageCount}`,
        alignment: 'center',
        fontSize: 10,
      }),
      images: {
        logo: getBase64SkyfoldLogo(),
      },
      content: [
        printFilters,
        { text: `Found ${cmsData.length} Projects with ${totalWalls} Walls`, marginBottom: 10 },
        {
          layout: 'noBorders',
          color: '#77858e',
          style: 'table',
          table: {
            widths: ['*', '*', 50, 70],
            body: cmsData.map(item => [
              item.content.project_name ?? '',
              item.content.architect_design ?? '',
              `${item.content.number_of_walls ?? '0'} walls`,
              item.content.installation_date ?? '',
            ]),
          },
        },
      ],
      styles: {
        table: {
          margin: [0, 0, -20, 0],
          fontSize: 10,
        },
      },
      defaultStyle: {
        color: '#414f5b',
      },
    }

    pdfMake.createPdf(docDefinition).open()

    isDownloadingPdf.value = false
  }
  return {
    isDownloadingPdf,
    downloadPdfMake,
  }
}
