import type { SuggestionsType, Category, Suggestion, FileExtension } from '../mock/searchSuggestions'

export const useSearch = (query: MaybeRef<string>, page?: MaybeRef<number>) => {
  const { $ts } = useI18n()

  return useFetch(`/api/${useI18n().$getLocale()}/algolia/search`, {
    query: {
      query,
      page,
    },
    transform: (data): SuggestionsType => {
      const categories = data.results.map((result, i): Category => {
        const suggestions = result.hits.map((hit): Suggestion => {
          switch (hit.content_type) {
            case 'portfolio':
              return ({
                id: hit.objectID,
                name: hit.project_name,
                description: hit.product?.name ?? '',
                tag: hit.project_type?.name,
                to: '#',
              })
            case 'resource':
              return ({
                id: hit.objectID,
                name: hit.name,
                description: '',
                to: '#',
                tag: hit.category_name,
                prefixIcon: `Ext${hit.asset?.filename?.split?.('.')?.pop?.()?.toUpperCase?.() as FileExtension ?? 'PDF'}Icon`,
              })
            case 'resource_category':
              return ({
                id: hit.objectID,
                name: hit.name,
                description: hit.description ?? '',
                tag: hit.name,
                prefixIcon: 'GenFolderIcon',
                to: '#',
              })
            case 'resource_folder':
              return ({
                id: hit.objectID,
                name: hit.name,
                description: hit.description ?? '',
                tag: hit.category_name,
                prefixIcon: 'GenFolderIcon',
                to: '#',
              })
          }
        })

        const getName = () => {
          if (result.index?.includes('portfolios')) {
            return $ts('search.portfolios')
          }
          else if (result.index?.includes('resources')) {
            const uniquetTags = [...new Set(suggestions.map(s => s.tag))]
            if (uniquetTags.length === 1) {
              return uniquetTags[0]!
            }
            return $ts('search.resources')
          }
          return ''
        }

        return {
          id: result.index ?? i.toString(),
          name: getName(),
          current: suggestions.length,
          total: result.nbHits ?? 0,
          suggestions,
        }
      })

      return ({
        results_number: {
          current: categories.map(category => category.current).reduce((a, b) => a + b),
          total: categories.map(category => category.total).reduce((a, b) => a + b),
        },
        categories,
      })
    },
  })
}
