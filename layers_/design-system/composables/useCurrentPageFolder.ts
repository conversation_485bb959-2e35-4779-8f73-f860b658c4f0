export const useCurrentPageFolder = () => {
  const currentFolder = useState<string[]>('currentFolder', () => [])

  const isFolderOpen = (folderId: string, parentIds: string[]) => currentFolder.value.join().startsWith([...parentIds, folderId].join())

  const toggleFolder = (folderId: string, parentIds: string[]) => {
    // If folder is already open, collapse it by setting current folder to parent ids
    // If folder is closed, open it by adding it to the navigation path
    currentFolder.value = isFolderOpen(folderId, parentIds) ? parentIds : [...parentIds, folderId]
  }

  return {
    currentFolder,
    isFolderOpen,
    toggleFolder,
  }
}
