import emblaCarouselVue from 'embla-carousel-vue'
import type { EmblaOptionsType, EmblaPluginType } from 'embla-carousel'

export default function useEmblaCarousel(options: EmblaOptionsType = {}, plugins: EmblaPluginType[] = []) {
  const [emblaRef, emblaApi] = emblaCarouselVue(options, plugins)

  const scrollNext = () => {
    if (emblaApi.value) {
      emblaApi.value.scrollNext()
    }
  }

  const scrollPrev = () => {
    if (emblaApi.value) {
      emblaApi.value.scrollPrev()
    }
  }

  return {
    emblaRef,
    emblaApi,
    scrollNext,
    scrollPrev,
  }
}
