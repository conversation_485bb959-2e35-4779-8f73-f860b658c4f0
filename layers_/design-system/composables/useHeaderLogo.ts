import type { UseHeaderLogoProps } from './useHeaderLogo.props'

export default function useBrandLogo(
  props: UseHeaderLogoProps,
) {
  const logoName = computed(() => {
    const prefix = 'Brand'
    const brandName = props.brand === 'modernfold' ? 'ModernFold' : 'SkyFold'
    const variantSuffix = props.variant === 'colored' ? 'Colored' : 'White'

    return `${prefix}${brandName}${variantSuffix}`
  })

  return {
    logoName,
  }
}
