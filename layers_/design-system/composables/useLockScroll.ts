/**
 * This composable is used to lock the scroll
 * @returns {Object} isLocked - The state of the lock
 * @returns {Function} setIsLocked - The function to set the lock
 */
export default function useLockScroll() {
  const isLocked = useState('isLocked', () => false)

  function setIsLocked(value: boolean) {
    isLocked.value = value
  }

  watchEffect(() => {
    if (typeof document !== 'undefined') {
      if (isLocked.value) document.body.style.overflow = 'hidden'
      else document.body.style.overflow = 'auto'
    }
  })

  return { isLocked, setIsLocked }
}
