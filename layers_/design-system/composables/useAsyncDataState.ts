import type { NuxtApp, NuxtError } from '#app'

export const useAsyncDataState = <ResT, DataT = ResT>(
  key: string,
  handler: (ctx?: NuxtApp) => Promise<ResT>,
  options?: { transform: (data: ResT) => DataT },
) => {
  const nuxtApp = useNuxtApp()
  const asyncDataKey = `AsyncDataState_asyncData_${key}`
  // asyncData is used to manage concurrent requests, useState to manage different instances
  const asyncData = useAsyncData(asyncDataKey, handler, {
    immediate: false,
    dedupe: 'defer',
    getCachedData: (key, nuxtApp) => nuxtApp.payload.data[key],
    transform: options?.transform,
  })
  const data = useState<DataT | null>(`AsyncDataState_state_${key}`, () => null)
  const error = useState<NuxtError<unknown> | null>(`AsyncDataState_error_${key}`, () => null)

  // trigger only once, event if called multiple times from different instances.
  // useState is needed because useAsyncData do not share the same ref instance under the hood
  const init = async () => {
    if (data.value) return
    await asyncData.refresh()
    data.value = nuxtApp.payload.data[asyncDataKey]
  }

  // retrigger every time is called
  const refresh = async () => {
    await asyncData.refresh({ _initial: false, dedupe: 'cancel' })
    data.value = nuxtApp.payload.data[asyncDataKey]
  }

  watch(asyncData.data, (newData) => {
    // @ts-expect-error PickFrom<Data, KeysOf<Data>> is Data
    data.value = newData ?? null
  })

  watch(asyncData.error, (newError) => {
    error.value = newError ?? null
  })

  return {
    data,
    error,
    init,
    refresh,
  }
}
