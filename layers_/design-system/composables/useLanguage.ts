import type { MenuItem } from '@design-system/components/Organisms/Menu/OrganismsMenuDropdown.props'
import type { LanguageItem, LanguageConfig, LanguageMenuOptions, LanguageMenuItem } from '../types/language'
import {
  createLanguageItem,
  createLanguageMenuItem as createMenuItem,
  filterLanguages,
  removeLocaleFromPath,
} from '../utils/language'

export default function useLanguage(config: LanguageConfig = {}) {
  const { ts, getLocale, getLocales } = useI18n()
  const route = useRoute()

  const { fallbackLocale = 'en' } = config

  const availableLanguages = computed<LanguageItem[]>(() => {
    const locales = getLocales().map(locale => locale.code)
    return locales.map(locale => createLanguageItem(locale, ts, config))
  })

  const currentLanguage = computed<LanguageItem>(() => {
    const currentLocale = getLocale()
    return availableLanguages.value.find(lang => lang.value === currentLocale)
      || createLanguageItem(fallbackLocale, ts, config)
  })

  const currentPathWithoutLocale = computed(() => {
    const locales = getLocales().map(locale => locale.code)
    return removeLocaleFromPath(route.path, locales)
  })

  const getLanguageUrl = (languageValue: string) => {
    return `/${languageValue}${currentPathWithoutLocale.value}`
  }

  // Menu item creation - abstracted and reusable
  const createLanguageMenuItem = (language: LanguageItem): LanguageMenuItem =>
    createMenuItem(language, getLanguageUrl)

  const getFilteredLanguages = (options: LanguageMenuOptions = {}) =>
    filterLanguages(availableLanguages.value, getLocale(), options)

  const languageMenu = computed<MenuItem[]>(() => {
    return [
      {
        label: 'Language',
        to: '#',
        items: getFilteredLanguages().map(createLanguageMenuItem),
      },
    ]
  })

  const languageMenuMobile = computed<LanguageMenuItem[]>(() => {
    return getFilteredLanguages({ includeCurrentLanguage: true }).map(createLanguageMenuItem)
  })

  return {
    availableLanguages,
    currentLanguage,
    currentPathWithoutLocale,
    languageMenu,
    languageMenuMobile,
  }
}
