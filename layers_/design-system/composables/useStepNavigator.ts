import type { OrderedStepItem, StepItem } from '@design-system/types/types'

const normalizeSteps = <T extends string>(rawSteps: Array<StepItem<T>>) => {
  const _steps = {} as Record<T, OrderedStepItem<T>>

  rawSteps.forEach((step, index) => {
    _steps[step.id] = {
      id: step.id,
      title: step.title,
      isCompleted: step?.isCompleted ?? false,
      index,
    }
  })

  return _steps ?? {}
}

export default function useStepNavigator<T extends string>(_steps: Array<StepItem<T>>) {
  const steps = computed(() => normalizeSteps(_steps))
  const stepper = useStepper(steps)

  // computed
  const keys = computed(() => Object.keys(stepper.steps.value) as Array<T>)
  const values = computed<Array<OrderedStepItem<T>>>(() => Object.values(stepper.steps.value))
  const next = computed(() => stepper.next.value ? stepper.get(stepper.next.value) : '')
  const active = computed(() => stepper.current.value)
  const lastCompleted = computed<OrderedStepItem<T> | null>(() => {
    let _lastCompleted: OrderedStepItem<T> | null = null

    if (stepper.steps.value) {
      for (const step of keys.value) {
        const currentStep = stepper.steps.value[step as T]
        if (currentStep === undefined) continue
        if (!currentStep?.isCompleted) break
        _lastCompleted = currentStep
      }
    }

    return _lastCompleted ?? stepper.at(0) ?? null
  })
  const isLast = computed(() => stepper.isLast.value)
  const isFirst = computed(() => stepper.isFirst.value)
  const direction = ref<'forward' | 'backward'>('forward')

  // methods
  const goToStepByName = (name: T) => {
    const currentStep = stepper.get(name)

    if (!lastCompleted.value) return
    if (currentStep) {
      if (currentStep.index <= lastCompleted.value?.index + 1) {
        stepper.goTo(currentStep.id)
      }
    }
  }
  const goToNextStep = () => {
    if (next.value && stepper.current.value.isCompleted) {
      if (next.value.index <= (lastCompleted.value?.index ?? 0) + 1) {
        stepper.goToNext()
      }
    }
  }
  const goToPreviousStep = () => {
    stepper.goToPrevious()
  }

  const goToLastValidStep = () => {
    const firstStep = stepper.at(0)
    if (firstStep && !firstStep?.isCompleted) {
      stepper.goTo(firstStep.id)
    }
    else {
      const nextStep = stepper.at((lastCompleted?.value?.index ?? 0) + 1)
      if (nextStep) stepper.goTo(nextStep.id)
    }
  }

  watch(
    () => stepper.current.value,
    (newValue, oldValue) => {
      direction.value = newValue.index > oldValue.index ? 'forward' : 'backward'
    },
  )

  watch(
    () => lastCompleted.value,
    () => {
      goToLastValidStep()
    }, { immediate: true },
  )

  return {
    active,
    isLast,
    isFirst,
    next,
    lastCompleted,
    keys,
    direction,
    values,
    goToStepByName,
    goToNextStep,
    goToPreviousStep,
  }
}
