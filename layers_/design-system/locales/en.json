{"test": "design-system", "menu": {"back": "Back to {label}", "main": "Main Menu"}, "search": {"placeholder": "Search for something", "resultsNumber": "results of", "noResults": "No results", "seeAll": "See all results", "results": "results", "youSearchedFor": "You searched for", "goTo": "Go to {label}", "openSearch": "Open searchbar", "closeSearch": "Close searchbar", "portfolios": "Portfolios", "resources": "Resources"}, "atoms": {"folderButton": {"lastUpdate": "Last update:"}, "seeMoreLink": {"seeMoreItems": "See 10 more ({totalItems})", "seeLess": "See less"}}, "molecules": {"switchCode": {"label": "Switch code"}, "phoneNumber": {"required": "Phone number is required", "invalid": "Phone number is invalid"}}, "organisms": {"carouselCards": {"whatsNew": "What's new", "seeAll": "See all"}, "projectOfTheMonth": {"title": "Project of the month", "project": "Project", "scope": "<PERSON><PERSON>", "architect": "Architect", "specifyingDealer": "Specifying dealer", "dealer": "Dealer"}, "stepper": {"select": "Select step"}}, "portfolio": {"filters": {"title": "Filters", "reset": "Reset all", "advanced": "Advanced filters", "toggleAdvanced": "Toggle advanced filters accordion", "sort": "Sort by", "searchSomething": "Search something", "selectSomething": "Select something", "writeSomething": "Write something", "labels": {"brand": "Brand", "state": "State", "country__name": "Country", "client_name": "Client Name", "location": "Location", "product__name": "Product", "wall_types__name": "Wall Types", "project_type_sub_categories__name": "Project Type Subcategories", "finish_type": "Finish Type", "newRetrofit": "New/Retrofit", "award_winning": "Award Winning", "installation_date": "Installation Date", "architect_design": "Architect Design", "general_contractor": "General Contractor", "project_type__name": "Project Type", "dealer": "Dealer", "panel_finish": "Panel Finish", "industry_list": "Industry List", "specialFinish": "Special Finish"}}}, "assets": {"noResults": "No results"}, "fileUpload": {"uploadFile": "Upload file", "replaceFile": "Replace file", "removeFile": "Remove file"}, "table": {"noData": "0 results found"}}