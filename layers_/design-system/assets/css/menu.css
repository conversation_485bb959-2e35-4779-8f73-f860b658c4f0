@layer utilities {
  /* Perspective Utility */
  .perspective-2000 {
    perspective: 2000px;
  }

  /* Animation Utilities */
  .animate-scaleIn {
    animation: scaleIn 200ms ease;
  }
  .animate-scaleOut {
    animation: scaleOut 200ms ease;
  }
  .animate-fadeIn {
    animation: fadeIn 200ms ease;
  }
  .animate-fadeOut {
    animation: fadeOut 200ms ease;
  }
  .animate-enterFromLeft {
    animation: enterFromLeft 250ms ease;
  }
  .animate-enterFromRight {
    animation: enterFromRight 250ms ease;
  }
  .animate-exitToLeft {
    animation: exitToLeft 250ms ease;
  }
  .animate-exitToRight {
    animation: exitToRight 250ms ease;
  }
}

/* Keyframes Definitions */
@keyframes enterFromRight {
  from { opacity: 0; transform: translateX(200px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes enterFromLeft {
  from { opacity: 0; transform: translateX(-200px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes exitToRight {
  from { opacity: 1; transform: translateX(0); }
  to { opacity: 0; transform: translateX(200px); }
}

@keyframes exitToLeft {
  from { opacity: 1; transform: translateX(0); }
  to { opacity: 0; transform: translateX(-200px); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: rotateX(-10deg) scale(0.9); }
  to { opacity: 1; transform: rotateX(0deg) scale(1); }
}

@keyframes scaleOut {
  from { opacity: 1; transform: rotateX(0deg) scale(1); }
  to { opacity: 0; transform: rotateX(-10deg) scale(0.95); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}
