@import 'tailwindcss' source('../../../../');
@plugin "@tailwindcss/typography";
@import './tokens.css';
@import './typography.css';
@import './grid.css';
@import './menu.css';
@import './reka.css';

:root {
  --header-height: 3.5rem;
  --search-bar-height: 3.5rem;
}

/* host-grotesk-regular - latin */
@font-face {
  font-display: swap;
  /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
  font-family: 'Host Grotesk';
  font-style: normal;
  font-weight: 400;
  src: url('/fonts/host-grotesk-v4-latin-regular.woff2') format('woff2');
  /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}

/* host-grotesk-600 - latin */
@font-face {
  font-display: swap;
  /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
  font-family: 'Host Grotesk';
  font-style: normal;
  font-weight: 600;
  src: url('/fonts/host-grotesk-v4-latin-600.woff2') format('woff2');
  /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}

/* host-grotesk-700 - latin */
@font-face {
  font-display: swap;
  /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
  font-family: 'Host Grotesk';
  font-style: normal;
  font-weight: 700;
  src: url('/fonts/host-grotesk-v4-latin-700.woff2') format('woff2');
  /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}


@media (min-width: theme(--breakpoint-sm)) {
  :root {
    --spacing-page-container: 77rem;
  }
}

@media (min-width: theme(--breakpoint-md)) {
  :root {
    --spacing-page-container: 78rem;
  }
}

@media (min-width: theme(--breakpoint-lg)) {
  :root {
    --spacing-page-container: 79rem;
  }
}

@media (min-width: theme(--breakpoint-xl)) {
  :root {
    --spacing-page-container: 90rem;
  }
}

@theme inline {
  --animate-run: run 1.5s ease-in-out infinite alternate;
  --spacing-page-container: var(--spacing-page-container);
  --shadow-100: 0px 0px 4px 0px var(--color-states-azure-1000, #2F6EB4);
  --shadow-200: 0px 15px 20px rgba(0, 0, 0, 0.05);
  --shadow-300: 0px 20px 50px 0px rgba(0, 0, 0, 0.10);

  --spacing-page: calc(90svh - var(--header-height));

  @keyframes run {
    0% {
      left: 0;
      transform: translateX(-50%);
      width: 30%;
    }

    50% {
      width: 60%;
    }

    100% {
      left: 100%;
      transform: translateX(-50%);
      width: 30%;
    }
  }
}

@utility container {
  @apply max-w-page-container mx-auto px-(--grid-system-spacing-x);
}

*>* {
  @apply antialiased;
}

@custom-variant skyfold (&:where([data-theme="skyfold"] *));
@custom-variant modernfold (&:where([data-theme="modernfold"] *));


@utility disable-scrollbars {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE 10+ */

  &::-webkit-scrollbar {
    background: transparent;
    /* Chrome/Safari/Webkit */
    width: 0px;
    display: none;
  }
}
