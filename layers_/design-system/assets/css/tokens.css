/**
 * Do not edit directly, this file was auto-generated.
 */

:root {
  /* -- Theme modernfold  -- */
  --color-text-neutral: #000000;
  --color-text-invert: #ffffff;
  --color-text-accent: #00337f;
  --color-text-hover: #1c5ab6;
  --color-text-disabled: #83868c;
  --color-text-placeholder: #83868c;
  --color-text-success: #276f09;
  --color-text-warning: #e87000;
  --color-text-alert: #d32424;
  --color-text-info: #2f6eb4;
  --color-background-neutral: #ffffff;
  --color-background-invert: #000000;
  --color-background-light: #edf0f4;
  --color-background-hidden: rgba(255, 255, 255, 0.0000);
  --color-background-brand: #c4daed;
  --color-background-accent: #00337f;
  --color-background-hover: #1c5ab6;
  --color-background-disabled: #c4cad5;
  --color-background-success: #e2ffd6;
  --color-background-warning: #f5e1c1;
  --color-background-alert: #fdeedd;
  --color-background-info: #d3e8ff;
  --color-stroke-neutral: #c4cad5;
  --color-stroke-invert: #ffffff;
  --color-stroke-accent: #00337f;
  --color-stroke-hover: #1c5ab6;
  --color-stroke-success: #276f09;
  --color-stroke-warning: #e87000;
  --color-stroke-alert: #d32424;
  --color-stroke-disabled: #83868c;
  --color-stroke-focus: #2f6eb4;
  --color-icon-neutral: #000000;
  --color-icon-invert: #ffffff;
  --color-icon-accent: #00337f;
  --color-icon-hover: #1c5ab6;
  --color-icon-success: #276f09;
  --color-icon-warning: #e87000;
  --color-icon-alert: #d32424;
  --color-icon-disabled: #83868c;
  --font-host-grotesk: Host Grotesk;
  --angle-extra-small: 4px;
  --angle-small: 8px;
  --angle-medium: 16px;
  --angle-large: 32px;
  --angle-extra-large: 40px;
  --angle-max: 80px;
  --stroke-thin: 0.5px;
  --stroke-regular: 1px;
  --stroke-bold: 2px;
  /* -- Breakpoint default -- */
  --grids-column-number: 4px;
  --grids-width: 375px;
  --grids-columns: 4;
  --spacing-gutter: 16px;
  --spacing-margin: 16px;
  --spacing-padding-xxs: 4px;
  --spacing-padding-xs: 8px;
  --spacing-padding-sm: 12px;
  --spacing-padding-md: 16px;
  --spacing-padding-lg: 20px;
  --spacing-padding-xl: 24px;
  --spacing-padding-xxl: 32px;
  --spacing-gap-xxs: 4px;
  --spacing-gap-xs: 8px;
  --spacing-gap-sm: 12px;
  --spacing-gap-md: 16px;
  --spacing-gap-lg: 20px;
  --spacing-gap-xl: 24px;
  --spacing-gap-xxl: 32px;
  --font-size-link-md: 16px;
  --font-size-link-sm: 14px;
  --font-size-section-sm: 12px;
  --font-size-caption-sm: 14px;
  --font-size-body-md: 16px;
  --font-size-body-sm: 14px;
  --font-size-heading-xl: 36px;
  --font-size-heading-lg: 28px;
  --font-size-heading-md: 24px;
  --font-size-heading-sm: 20px;
  --font-size-subtitle-lg: 18px;
  --font-size-subtitle-md: 16px;
  --font-size-subtitle-sm: 16px;
  --font-size-cta-md: 16px;
  --font-size-cta-sm: 14px;
  --font-weight-heading-xl: bold;
  --font-weight-heading-lg: bold;
  --font-weight-heading-md: bold;
  --font-weight-heading-sm: bold;
  --font-weight-subtitle-lg: semibold;
  --font-weight-subtitle-md: semibold;
  --font-weight-subtitle-sm: semibold;
  --font-weight-cta-md: bold;
  --font-weight-cta-sm: bold;
  --font-weight-link-md: regular;
  --font-weight-link-sm: regular;
  --font-weight-section-sm: bold;
  --font-weight-caption-sm: String value;
  --font-weight-body-md: regular;
  --font-weight-body-sm: regular;
}

/* -- Theme skyfold -- */
.theme-skyfold {
  --color-text-neutral: #000000;
  --color-text-invert: #ffffff;
  --color-text-accent: #013c5b;
  --color-text-hover: #0080c5;
  --color-text-disabled: #83868c;
  --color-text-placeholder: #83868c;
  --color-text-success: #276f09;
  --color-text-warning: #e87000;
  --color-text-alert: #d32424;
  --color-text-info: #2f6eb4;
  --color-background-neutral: #ffffff;
  --color-background-invert: #000000;
  --color-background-light: #edf0f4;
  --color-background-hidden: rgba(255, 255, 255, 0.0000);
  --color-background-brand: #cee3f0;
  --color-background-accent: #013c5b;
  --color-background-hover: #0080c5;
  --color-background-disabled: #c4cad5;
  --color-background-success: #e2ffd6;
  --color-background-warning: #f5e1c1;
  --color-background-alert: #fdeedd;
  --color-background-info: #d3e8ff;
  --color-stroke-neutral: #c4cad5;
  --color-stroke-invert: #ffffff;
  --color-stroke-accent: #013c5b;
  --color-stroke-hover: #0080c5;
  --color-stroke-success: #276f09;
  --color-stroke-warning: #e87000;
  --color-stroke-alert: #d32424;
  --color-stroke-disabled: #83868c;
  --color-stroke-focus: #2f6eb4;
  --color-icon-neutral: #000000;
  --color-icon-invert: #ffffff;
  --color-icon-accent: #013c5b;
  --color-icon-hover: #0080c5;
  --color-icon-success: #276f09;
  --color-icon-warning: #e87000;
  --color-icon-alert: #d32424;
  --color-icon-disabled: #83868c;
  --font-host-grotesk: Host Grotesk;
  --angle-extra-small: 4px;
  --angle-small: 8px;
  --angle-medium: 16px;
  --angle-large: 32px;
  --angle-extra-large: 40px;
  --angle-max: 80px;
  --stroke-thin: 0.5px;
  --stroke-regular: 1px;
  --stroke-bold: 2px;
}

/* -- @screen(md) -- */
@media (min-width: theme(--breakpoint-md)) {
  :root {
    --grids-column-number: 8px;
    --grids-width: 768px;
    --grids-columns: 8;
    --spacing-gutter: 24px;
    --spacing-margin: 24px;
    --spacing-padding-xxs: 4px;
    --spacing-padding-xs: 8px;
    --spacing-padding-sm: 12px;
    --spacing-padding-md: 16px;
    --spacing-padding-lg: 20px;
    --spacing-padding-xl: 24px;
    --spacing-padding-xxl: 32px;
    --spacing-gap-xxs: 4px;
    --spacing-gap-xs: 8px;
    --spacing-gap-sm: 12px;
    --spacing-gap-md: 16px;
    --spacing-gap-lg: 20px;
    --spacing-gap-xl: 24px;
    --spacing-gap-xxl: 32px;
    --font-size-link-md: 16px;
    --font-size-link-sm: 14px;
    --font-size-section-sm: 12px;
    --font-size-caption-sm: 14px;
    --font-size-body-md: 16px;
    --font-size-body-sm: 14px;
    --font-size-heading-xl: 36px;
    --font-size-heading-lg: 28px;
    --font-size-heading-md: 24px;
    --font-size-heading-sm: 20px;
    --font-size-subtitle-lg: 20px;
    --font-size-subtitle-md: 18px;
    --font-size-subtitle-sm: 16px;
    --font-size-cta-md: 16px;
    --font-size-cta-sm: 14px;
    --font-weight-heading-xl: bold;
    --font-weight-heading-lg: bold;
    --font-weight-heading-md: bold;
    --font-weight-heading-sm: bold;
    --font-weight-subtitle-lg: semibold;
    --font-weight-subtitle-md: semibold;
    --font-weight-subtitle-sm: semibold;
    --font-weight-cta-md: bold;
    --font-weight-cta-sm: bold;
    --font-weight-link-md: regular;
    --font-weight-link-sm: regular;
    --font-weight-section-sm: bold;
    --font-weight-caption-sm: regular;
    --font-weight-body-md: regular;
    --font-weight-body-sm: regular;
  }
}

/* -- @screen(lg) -- */
@media (min-width: theme(--breakpoint-lg)) {
  :root {
    --grids-column-number: 12px;
    --grids-width: 1024px;
    --grids-columns: 12;
    --spacing-gutter: 24px;
    --spacing-margin: 32px;
    --spacing-padding-xxs: 4px;
    --spacing-padding-xs: 8px;
    --spacing-padding-sm: 12px;
    --spacing-padding-md: 16px;
    --spacing-padding-lg: 20px;
    --spacing-padding-xl: 24px;
    --spacing-padding-xxl: 32px;
    --spacing-gap-xxs: 4px;
    --spacing-gap-xs: 8px;
    --spacing-gap-sm: 12px;
    --spacing-gap-md: 16px;
    --spacing-gap-lg: 20px;
    --spacing-gap-xl: 24px;
    --spacing-gap-xxl: 32px;
    --font-size-link-md: 16px;
    --font-size-link-sm: 14px;
    --font-size-section-sm: 12px;
    --font-size-caption-sm: 14px;
    --font-size-body-md: 16px;
    --font-size-body-sm: 14px;
    --font-size-heading-xl: 56px;
    --font-size-heading-lg: 36px;
    --font-size-heading-md: 28px;
    --font-size-heading-sm: 24px;
    --font-size-subtitle-lg: 28px;
    --font-size-subtitle-md: 20px;
    --font-size-subtitle-sm: 18px;
    --font-size-cta-md: 16px;
    --font-size-cta-sm: 14px;
    --font-weight-heading-xl: bold;
    --font-weight-heading-lg: bold;
    --font-weight-heading-md: bold;
    --font-weight-heading-sm: bold;
    --font-weight-subtitle-lg: semibold;
    --font-weight-subtitle-md: semibold;
    --font-weight-subtitle-sm: semibold;
    --font-weight-cta-md: bold;
    --font-weight-cta-sm: bold;
    --font-weight-link-md: regular;
    --font-weight-link-sm: regular;
    --font-weight-section-sm: bold;
    --font-weight-caption-sm: regular;
    --font-weight-body-md: regular;
    --font-weight-body-sm: regular;
  }
}

/* -- @screen(xl) -- */
@media (min-width: theme(--breakpoint-xl)) {
  :root {
    --grids-column-number: 12px;
    --grids-width: 1440px;
    --grids-columns: 12;
    --spacing-gutter: 32px;
    --spacing-margin: 120px;
    --spacing-padding-xxs: 4px;
    --spacing-padding-xs: 8px;
    --spacing-padding-sm: 12px;
    --spacing-padding-md: 16px;
    --spacing-padding-lg: 20px;
    --spacing-padding-xl: 24px;
    --spacing-padding-xxl: 32px;
    --spacing-gap-xxs: 4px;
    --spacing-gap-xs: 8px;
    --spacing-gap-sm: 12px;
    --spacing-gap-md: 16px;
    --spacing-gap-lg: 20px;
    --spacing-gap-xl: 24px;
    --spacing-gap-xxl: 32px;
    --font-size-link-md: 16px;
    --font-size-link-sm: 14px;
    --font-size-section-sm: 12px;
    --font-size-caption-sm: 14px;
    --font-size-body-md: 16px;
    --font-size-body-sm: 14px;
    --font-size-heading-xl: 56px;
    --font-size-heading-lg: 36px;
    --font-size-heading-md: 28px;
    --font-size-heading-sm: 24px;
    --font-size-subtitle-lg: 28px;
    --font-size-subtitle-md: 20px;
    --font-size-subtitle-sm: 18px;
    --font-size-cta-md: 16px;
    --font-size-cta-sm: 14px;
    --font-weight-heading-xl: bold;
    --font-weight-heading-lg: bold;
    --font-weight-heading-md: bold;
    --font-weight-heading-sm: bold;
    --font-weight-subtitle-lg: semibold;
    --font-weight-subtitle-md: semibold;
    --font-weight-subtitle-sm: semibold;
    --font-weight-cta-md: bold;
    --font-weight-cta-sm: bold;
    --font-weight-link-md: regular;
    --font-weight-link-sm: regular;
    --font-weight-section-sm: bold;
    --font-weight-caption-sm: regular;
    --font-weight-body-md: regular;
    --font-weight-body-sm: regular;
  }
}

@theme inline {
  /* -- Primitive -- */
  --color-skyfold-navy-800: #013c5b;
  --color-skyfold-navy-600: #0080c5;
  --color-skyfold-navy-100: #cee3f0;
  --color-modernfold-blue-800: #00337f;
  --color-modernfold-blue-600: #1c5ab6;
  --color-modernfold-blue-100: #c4daed;
  --color-grey-scale-grey-0: #ffffff;
  --color-grey-scale-grey-5: #edf0f4;
  --color-grey-scale-grey-10: #dde4ee;
  --color-grey-scale-grey-20: #c4cad5;
  --color-grey-scale-grey-30: #aaafb6;
  --color-grey-scale-grey-40: #83868c;
  --color-grey-scale-grey-50: #4b4b4b;
  --color-grey-scale-grey-60: #343434;
  --color-grey-scale-grey-80: #262729;
  --color-grey-scale-grey-100: #000000;
  --color-black-opacity-black-20%: rgba(0, 0, 0, 0.2000);
  --color-black-opacity-black-30%: rgba(0, 0, 0, 0.3000);
  --color-black-opacity-black-40%: rgba(0, 0, 0, 0.4000);
  --color-black-opacity-black-60%: rgba(0, 0, 0, 0.6000);
  --color-black-opacity-black-80%: rgba(0, 0, 0, 0.8000);
  --color-white-opacity-white-0%: rgba(255, 255, 255, 0.0000);
  --color-white-opacity-white-20%: rgba(255, 255, 255, 0.2000);
  --color-white-opacity-white-30%: rgba(255, 255, 255, 0.3000);
  --color-white-opacity-white-40%: rgba(255, 255, 255, 0.4000);
  --color-white-opacity-white-60%: rgba(255, 255, 255, 0.6000);
  --color-white-opacity-white-80%: rgba(255, 255, 255, 0.8000);
  --color-white-opacity-white-100%: #ffffff;
  --color-states-green-1000: #276f09;
  --color-states-green-100: #e2ffd6;
  --color-states-red-1000: #d32424;
  --color-states-red-100: #fdeedd;
  --color-states-orange-1000: #e87000;
  --color-states-orange-100: #f5e1c1;
  --color-states-azure-1000: #2f6eb4;
  --color-states-azure-100: #d3e8ff;
  --blur-super-light: 4px;
  --blur-light: 8px;
  --blur-regular: 12px;
  --blur-medium: 16px;
  --blur-dark: 32px;
  --breakpoint-sm: 320px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1440px;
  /* -- Theme -- */
  --text-color-neutral: var(--color-text-neutral);
  --text-color-invert: var(--color-text-invert);
  --text-color-accent: var(--color-text-accent);
  --text-color-hover: var(--color-text-hover);
  --text-color-disabled: var(--color-text-disabled);
  --text-color-placeholder: var(--color-text-placeholder);
  --text-color-success: var(--color-text-success);
  --text-color-warning: var(--color-text-warning);
  --text-color-alert: var(--color-text-alert);
  --text-color-info: var(--color-text-info);
  --background-color-neutral: var(--color-background-neutral);
  --background-color-invert: var(--color-background-invert);
  --background-color-light: var(--color-background-light);
  --background-color-hidden: var(--color-background-hidden);
  --background-color-brand: var(--color-background-brand);
  --background-color-accent: var(--color-background-accent);
  --background-color-hover: var(--color-background-hover);
  --background-color-disabled: var(--color-background-disabled);
  --background-color-success: var(--color-background-success);
  --background-color-warning: var(--color-background-warning);
  --background-color-alert: var(--color-background-alert);
  --background-color-info: var(--color-background-info);
  --outline-color-neutral: var(--color-stroke-neutral);
  --border-color-neutral: var(--color-stroke-neutral);
  --outline-color-invert: var(--color-stroke-invert);
  --border-color-invert: var(--color-stroke-invert);
  --outline-color-accent: var(--color-stroke-accent);
  --border-color-accent: var(--color-stroke-accent);
  --outline-color-hover: var(--color-stroke-hover);
  --border-color-hover: var(--color-stroke-hover);
  --outline-color-success: var(--color-stroke-success);
  --border-color-success: var(--color-stroke-success);
  --outline-color-warning: var(--color-stroke-warning);
  --border-color-warning: var(--color-stroke-warning);
  --outline-color-alert: var(--color-stroke-alert);
  --border-color-alert: var(--color-stroke-alert);
  --outline-color-disabled: var(--color-stroke-disabled);
  --border-color-disabled: var(--color-stroke-disabled);
  --outline-color-focus: var(--color-stroke-focus);
  --border-color-focus: var(--color-stroke-focus);
  --color-icon-neutral: var(--color-icon-neutral);
  --color-icon-invert: var(--color-icon-invert);
  --color-icon-accent: var(--color-icon-accent);
  --color-icon-hover: var(--color-icon-hover);
  --color-icon-success: var(--color-icon-success);
  --color-icon-warning: var(--color-icon-warning);
  --color-icon-alert: var(--color-icon-alert);
  --color-icon-disabled: var(--color-icon-disabled);
  --font-host-grotesk: var(--font-host-grotesk);
  --radius-extra-small: var(--angle-extra-small);
  --radius-small: var(--angle-small);
  --radius-medium: var(--angle-medium);
  --radius-large: var(--angle-large);
  --radius-extra-large: var(--angle-extra-large);
  --radius-max: var(--angle-max);
  --border-width-thin: var(--stroke-thin);
  --border-width-regular: var(--stroke-regular);
  --border-width-bold: var(--stroke-bold);
  /* -- Breakpoint -- */
  --grids-column-number: var(--grids-column-number);
  --grids-width: var(--grids-width);
  --grid-system-columns: var(--grids-columns);
  --grid-system-gutter-x: var(--spacing-gutter);
  --grid-system-spacing-x: var(--spacing-margin);
  --spacing-padding-xxs: var(--spacing-padding-xxs);
  --spacing-padding-xs: var(--spacing-padding-xs);
  --spacing-padding-sm: var(--spacing-padding-sm);
  --spacing-padding-md: var(--spacing-padding-md);
  --spacing-padding-lg: var(--spacing-padding-lg);
  --spacing-padding-xl: var(--spacing-padding-xl);
  --spacing-padding-xxl: var(--spacing-padding-xxl);
  --gap-xxs: var(--spacing-gap-xxs);
  --gap-xs: var(--spacing-gap-xs);
  --gap-sm: var(--spacing-gap-sm);
  --gap-md: var(--spacing-gap-md);
  --gap-lg: var(--spacing-gap-lg);
  --gap-xl: var(--spacing-gap-xl);
  --gap-xxl: var(--spacing-gap-xxl);
  --text-link-md: var(--font-size-link-md);
  --text-link-sm: var(--font-size-link-sm);
  --text-section-sm: var(--font-size-section-sm);
  --text-caption-sm: var(--font-size-caption-sm);
  --text-body-md: var(--font-size-body-md);
  --text-body-sm: var(--font-size-body-sm);
  --text-heading-xl: var(--font-size-heading-xl);
  --text-heading-lg: var(--font-size-heading-lg);
  --text-heading-md: var(--font-size-heading-md);
  --text-heading-sm: var(--font-size-heading-sm);
  --text-subtitle-lg: var(--font-size-subtitle-lg);
  --text-subtitle-md: var(--font-size-subtitle-md);
  --text-subtitle-sm: var(--font-size-subtitle-sm);
  --text-cta-md: var(--font-size-cta-md);
  --text-cta-sm: var(--font-size-cta-sm);
  --font-weight-heading-xl: var(--font-weight-heading-xl);
  --font-weight-heading-lg: var(--font-weight-heading-lg);
  --font-weight-heading-md: var(--font-weight-heading-md);
  --font-weight-heading-sm: var(--font-weight-heading-sm);
  --font-weight-subtitle-lg: var(--font-weight-subtitle-lg);
  --font-weight-subtitle-md: var(--font-weight-subtitle-md);
  --font-weight-subtitle-sm: var(--font-weight-subtitle-sm);
  --font-weight-cta-md: var(--font-weight-cta-md);
  --font-weight-cta-sm: var(--font-weight-cta-sm);
  --font-weight-link-md: var(--font-weight-link-md);
  --font-weight-link-sm: var(--font-weight-link-sm);
  --font-weight-section-sm: var(--font-weight-section-sm);
  --font-weight-caption-sm: var(--font-weight-caption-sm);
  --font-weight-body-md: var(--font-weight-body-md);
  --font-weight-body-sm: var(--font-weight-body-sm);
}

