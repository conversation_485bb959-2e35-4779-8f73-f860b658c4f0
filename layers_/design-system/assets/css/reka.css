/* Accordion style */
@keyframes slideDown {
  from {
    height: 0;
    overflow: hidden;
  }
  to {
    height: var(--reka-accordion-content-height);
    overflow: hidden;
  }
}

@keyframes slideUp {
  from {
    height: var(--reka-accordion-content-height);
    overflow: hidden;
  }
  to {
    height: 0;
    overflow: hidden;
  }
}

.slide[data-state="open"] {
  animation: slideDown 300ms ease-in;
}

.slide[data-state="closed"] {
    animation: slideUp 300ms ease-in;
}