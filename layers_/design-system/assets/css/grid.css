  @utility gapped {
    @apply gap-x-(--grid-system-gutter-x);
  }
  
@layer base {
  .padded {
    @apply px-(--grid-system-spacing-x);
  }

  .margined {
    @apply mx-(--grid-system-spacing-x);
  }


  .negative-margin {
    @apply -mx-(--grid-system-spacing-x);
  }

  .grid-standard {
    @apply grid grid-cols-[repeat(var(--grid-system-columns),_minmax(0,_1fr))] gap-x-(--grid-system-gutter-x) px-(--grid-system-spacing-x);
  }

  /* grid-standard without margin */
  .grid-standard-full {
    @apply grid grid-cols-[repeat(var(--grid-system-columns),_minmax(0,_1fr))] gap-x-(--grid-system-gutter-x);
  }

  .grid-footer-mf {
    @apply grid grid-cols-[repeat(var(--grid-system-columns),_minmax(0,_1fr))];
  }
  .grid-footer-mf > * {
    @apply lg:px-(--grid-system-gutter-x) lg:first:pl-0 lg:last:pr-0 lg:border-r last:border-r-0 border-white-opacity-white-30%;
  }
}
