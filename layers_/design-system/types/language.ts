import type { MenuItem } from '../components/Organisms/Menu/OrganismsMenuDropdown.props'

export interface LanguageItem {
  label: string
  value: string
  icon: string
}

export interface LanguageConfig {
  iconPattern?: (locale: string) => string
  labelPattern?: (locale: string) => string
  fallbackLocale?: string
}

export interface LanguageMenuOptions {
  includeCurrentLanguage?: boolean
  menuLabel?: string
  customFilter?: (language: LanguageItem, currentLocale: string) => boolean
}

export interface LanguageMenuItem extends MenuItem {
  value: string
  icon: string
}

export interface LanguageUtils {
  createLanguageItem: (locale: string, ts: (key: string) => string, config?: LanguageConfig) => LanguageItem
  createLanguageMenuItem: (language: LanguageItem, getUrl: (locale: string) => string) => LanguageMenuItem
  filterLanguages: (languages: LanguageItem[], currentLocale: string, options?: LanguageMenuOptions) => LanguageItem[]
}
