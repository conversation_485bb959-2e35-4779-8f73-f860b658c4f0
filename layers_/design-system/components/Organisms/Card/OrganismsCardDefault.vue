<script setup lang="ts">
import type { OrganismsCardDefaultProps } from './OrganismsCardDefault.props.ts'

defineProps<OrganismsCardDefaultProps>()
</script>

<template>
  <OrganismsCard :image="image">
    <AtomsTag v-if="tag" :label="tag" />
    <OrganismsCardTitle>
      {{ title }}
    </OrganismsCardTitle>
    <OrganismsCardSubtitle>
      {{ description }}
    </OrganismsCardSubtitle>
    <OrganismsCardDate :date="date" />
    <OrganismsCardLocation>
      {{ location }}
    </OrganismsCardLocation>
  </OrganismsCard>
</template>
