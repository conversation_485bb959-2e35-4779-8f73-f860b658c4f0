import type { StoryObj } from '@storybook/vue3'
import AtomsTag from '../../Atoms/AtomsTag.vue'
import OrganismsCard from './OrganismsCard.vue'
import OrganismsCardTitle from './OrganismsCardTitle.vue'
import OrganismsCardSubtitle from './OrganismsCardSubtitle.vue'
import OrganismsCardLocation from './OrganismsCardLocation.vue'
import OrganismsCardDate from './OrganismsCardDate.vue'

// Define metadata for the component
const meta = {
  title: 'Organisms/Card',
  component: OrganismsCard,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component: `
### Component Architecture

The Card component follows a modular composition pattern, breaking down the card into smaller, reusable components:

- **OrganismsCard**: The main container component that handles the layout and styling of the card
- **OrganismsCardTitle**: Displays the main title of the card
- **OrganismsCardSubtitle**: Shows secondary information or product details
- **OrganismsCardLocation**: Displays location information with an icon
- **OrganismsCardDate**: Shows date information
- **AtomsTag**: Optional tag component for categorization

### Usage Guidelines

1. **Basic Structure**
   - Always start with the \`OrganismsCard\` component as the container
   - Add an image using the \`image\` prop
   - Use slots to add content components as needed

2. **When to Use Each Component**
   - Use \`OrganismsCardTitle\` for the main heading of the card
   - Use \`OrganismsCardSubtitle\` for secondary information (e.g., product names, descriptions)
   - Use \`OrganismsCardLocation\` when displaying location information
   - Use \`OrganismsCardDate\` for date-related information
   - Use \`AtomsTag\` for categorization or status indicators

3. **Modularity Benefits**
   - Components can be used independently or in combination
   - Each component maintains its own styling and behavior
   - Easy to extend with new components without modifying existing ones
   - Consistent styling across all card variations

4. **Responsive Design**
   - The card adapts to its container width
   - Content components stack vertically for optimal readability
   - Image maintains aspect ratio across all screen sizes

### Example Usage

\`\`\`vue
<OrganismsCard :image="imageUrl">
  <AtomsTag label="Category" />
  <OrganismsCardTitle>Main Title</OrganismsCardTitle>
  <OrganismsCardSubtitle>Subtitle</OrganismsCardSubtitle>
  <OrganismsCardLocation>Location</OrganismsCardLocation>
  <OrganismsCardDate :date="date" />
</OrganismsCard>
\`\`\`
        `,
      },
    },
  },
  argTypes: {
    image: {
      control: 'text',
      description: 'URL of the card image',
    },
  },
  args: {
    image: {
      src: 'https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80',
      alt: 'Card Image',
      width: 1470,
      height: 1040,
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

// Wrapper component to constrain width
const StoryWrapper = {
  template: '<div class="max-w-sm mx-auto"><slot /></div>',
}

// Base story with all components
export const CompleteCard: Story = {
  render: args => ({
    components: {
      OrganismsCard,
      OrganismsCardTitle,
      OrganismsCardSubtitle,
      OrganismsCardLocation,
      OrganismsCardDate,
      AtomsTag,
      StoryWrapper,
    },
    setup() {
      return { args }
    },
    template: `
      <StoryWrapper>
        <OrganismsCard v-bind="args">
          <AtomsTag label="LAW OFFICE" />
          <OrganismsCardTitle>Shulman Rodgers Gandal Pordy & Ecker</OrganismsCardTitle>
          <OrganismsCardSubtitle>Skyfold Classic 51™</OrganismsCardSubtitle>
          <OrganismsCardLocation>Washington, DC</OrganismsCardLocation>
          <OrganismsCardDate date="2025-12-26" />
        </OrganismsCard>
      </StoryWrapper>
    `,
  }),
}

// Minimal card with just image
export const ImageOnly: Story = {
  render: args => ({
    components: { OrganismsCard, StoryWrapper },
    setup() {
      return { args }
    },
    template: `
      <StoryWrapper>
        <OrganismsCard v-bind="args" />
      </StoryWrapper>
    `,
  }),
}

// Card with tag and title
export const WithTagAndTitle: Story = {
  render: args => ({
    components: { OrganismsCard, AtomsTag, OrganismsCardTitle, StoryWrapper },
    setup() {
      return { args }
    },
    template: `
      <StoryWrapper>
        <OrganismsCard v-bind="args">
          <AtomsTag label="CORPORATE" />
          <OrganismsCardTitle>Google Headquarters</OrganismsCardTitle>
        </OrganismsCard>
      </StoryWrapper>
    `,
  }),
}

// Card with location and date
export const WithLocationAndDate: Story = {
  render: args => ({
    components: { OrganismsCard, OrganismsCardLocation, OrganismsCardDate, StoryWrapper },
    setup() {
      return { args }
    },
    template: `
      <StoryWrapper>
        <OrganismsCard v-bind="args">
          <OrganismsCardLocation>New York, NY</OrganismsCardLocation>
          <OrganismsCardDate date="2024/12/15" />
        </OrganismsCard>
      </StoryWrapper>
    `,
  }),
}

// Card with subtitle
export const WithSubtitle: Story = {
  render: args => ({
    components: { OrganismsCard, OrganismsCardSubtitle, StoryWrapper },
    setup() {
      return { args }
    },
    template: `
      <StoryWrapper>
        <OrganismsCard v-bind="args">
          <OrganismsCardSubtitle>Dorma Variflex 100</OrganismsCardSubtitle>
        </OrganismsCard>
      </StoryWrapper>
    `,
  }),
}

// Comprehensive showcase of different card combinations
export const AllVariants: Story = {
  render: () => ({
    components: {
      OrganismsCard,
      OrganismsCardTitle,
      OrganismsCardSubtitle,
      OrganismsCardLocation,
      OrganismsCardDate,
      AtomsTag,
      StoryWrapper,
    },
    setup() {
      return {
        image: {
          src: 'https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80',
          alt: 'Card Image',
          width: 1470,
          height: 1040,
        },
      }
    },
    template: `

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <!-- Complete card -->
          <OrganismsCard :image="image">
            <AtomsTag label="LAW OFFICE" />
            <OrganismsCardTitle>Shulman Rodgers Gandal Pordy & Ecker</OrganismsCardTitle>
            <OrganismsCardSubtitle>Skyfold Classic 51™</OrganismsCardSubtitle>
            <OrganismsCardLocation>Washington, DC</OrganismsCardLocation>
            <OrganismsCardDate date="2025-24-26" />
          </OrganismsCard>

          <!-- Card with tag and title -->
          <OrganismsCard :image="image">
            <AtomsTag label="CORPORATE" />
            <OrganismsCardTitle>Google Headquarters</OrganismsCardTitle>
          </OrganismsCard>

          <!-- Card with location and date -->
          <OrganismsCard :image="image">
            <OrganismsCardLocation>New York, NY</OrganismsCardLocation>
            <OrganismsCardDate date="2024/12/15" />
          </OrganismsCard>

          <!-- Card with subtitle -->
          <OrganismsCard :image="image">
            <OrganismsCardSubtitle>Dorma Variflex 80</OrganismsCardSubtitle>
          </OrganismsCard>

          <!-- Image only card -->
          <OrganismsCard :image="image" />

          <!-- Card with title and subtitle -->
          <OrganismsCard :image="image">
            <OrganismsCardTitle>Stanford University</OrganismsCardTitle>
            <OrganismsCardSubtitle>Dorma Variflex 80</OrganismsCardSubtitle>
          </OrganismsCard>
        </div>
    `,
  }),
}
