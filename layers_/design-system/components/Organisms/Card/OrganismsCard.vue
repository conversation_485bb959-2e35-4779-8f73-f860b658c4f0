<script setup lang="ts">
import type { OrganismsCardProps } from './OrganismsCard.props'

defineProps<OrganismsCardProps>()

const imageError = ref(false)

const handleImageError = () => {
  imageError.value = true
}
</script>

<template>
  <div class="bg-neutral rounded-2xl shadow-lg overflow-hidden flex flex-col h-full" :class="containerClass">
    <div
      v-if="image"
      class="relative"
    >
      <NuxtImg
        v-if="!imageError"
        :src="image.src"
        :alt="image.alt"
        :width="image.width"
        :height="image.height"
        class="w-full h-full object-cover aspect-4/3"
        @error="handleImageError"
      />
      <OrganismsCardImagePlaceholder v-else />
    </div>
    <div
      v-if="$slots.default"
      class="p-padding-md flex flex-col gap-padding-xxs flex-1"
      :class="contentClass"
    >
      <slot />
    </div>
  </div>
</template>
