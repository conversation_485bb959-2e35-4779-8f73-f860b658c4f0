import type { Meta, StoryObj } from '@storybook/vue3'
import { ref } from 'vue'
import OrganismsFileUpload from './OrganismsFileUpload.vue'

const meta: Meta<typeof OrganismsFileUpload> = {
  title: 'Organisms/FileUpload',
  component: OrganismsFileUpload,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A file upload component for single file uploads with file validation and preview capabilities.',
      },
    },
  },
  argTypes: {
    accept: {
      control: 'object',
      description: 'Accepted file types (MIME types)',
    },
    maxSize: {
      control: 'number',
      description: 'Maximum file size in bytes',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the component is disabled',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {},
  render: args => ({
    components: { OrganismsFileUpload },
    setup() {
      const selectedFile = ref<File | null>(null)
      return { args, selectedFile }
    },
    template: `
      <div class="p-6">
        <OrganismsFileUpload
          v-bind="args"
          v-model="selectedFile"
          @change="(file: File | null) => console.log('File changed:', file)"
          @error="(error: string) => console.log('Error:', error)"
        />
        <div v-if="selectedFile" class="mt-4 p-4 bg-gray-100 rounded">
          <h3 class="font-semibold">Selected File:</h3>
          <p>Name: {{ selectedFile.name }}</p>
          <p>Size: {{ (selectedFile.size / 1024 / 1024).toFixed(2) }} MB</p>
          <p>Type: {{ selectedFile.type }}</p>
        </div>
      </div>
    `,
  }),
}

export const WithFileTypeRestriction: Story = {
  args: {
    accept: ['image/*', 'text/csv'],
  },
  render: args => ({
    components: { OrganismsFileUpload },
    setup() {
      const selectedFile = ref<File | null>(null)
      return { args, selectedFile }
    },
    template: `
      <div class="p-6">
        <div class="mb-4">
          <h3 class="font-semibold mb-2">File Upload - Images and Documents Only</h3>
          <p class="text-sm text-gray-600 mb-4">Accepts: images/*, text/csv</p>
        </div>
        <OrganismsFileUpload
          v-bind="args"
          v-model="selectedFile"
          @change="(file: File | null) => console.log('File changed:', file)"
          @error="(error: string) => alert('Error: ' + error)"
        />
      </div>
    `,
  }),
}

export const SmallFileSize: Story = {
  args: {
    maxSize: 1048576, // 1MB
  },
  render: args => ({
    components: { OrganismsFileUpload },
    setup() {
      const selectedFile = ref<File | null>(null)
      return { args, selectedFile }
    },
    template: `
      <div class="p-6">
        <div class="mb-4">
          <h3 class="font-semibold mb-2">File Upload - 1MB Limit</h3>
          <p class="text-sm text-gray-600 mb-4">Maximum file size: 1MB</p>
        </div>
        <OrganismsFileUpload
          v-bind="args"
          v-model="selectedFile"
          @change="(file: File | null) => console.log('File changed:', file)"
          @error="(error: string) => alert('Error: ' + error)"
        />
      </div>
    `,
  }),
}

export const Disabled: Story = {
  args: {
    disabled: true,
  },
  render: args => ({
    components: { OrganismsFileUpload },
    setup() {
      const selectedFile = ref<File | null>(null)
      return { args, selectedFile }
    },
    template: `
      <div class="p-6">
        <div class="mb-4">
          <h3 class="font-semibold mb-2">Disabled File Upload</h3>
          <p class="text-sm text-gray-600 mb-4">Component is disabled</p>
        </div>
        <OrganismsFileUpload
          v-bind="args"
          v-model="selectedFile"
        />
      </div>
    `,
  }),
}
