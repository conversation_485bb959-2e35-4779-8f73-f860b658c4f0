<script setup lang="ts">
import type { OrganismsCardCOMProps } from './OrganismsCardCOM.props.ts'

defineProps<OrganismsCardCOMProps>()
</script>

<template>
  <NuxtLink :to="to" :class="[{ 'hover:bg-hover hover:text-invert hover:border-neutral': to }, 'group bg-neutral pt-padding-xs pr-padding-xs pb-padding-md pl-padding-xxl flex flex-col gap-2 min-h-38 border-b-2 border-l-2 border-focus rounded-extra-small relative flex-1']">
    <div class="ml-auto grow">
      <div v-if="to">
        <AtomsIcon v-if="external" name="GenExternalLink" class="text-[2rem]" />
        <div v-else>
          <AtomsIcon
            name="GenWindowIconHover"
            class="text-[2rem] group-hover:block! hidden!"
          />
          <AtomsIcon
            name="GenWindowIcon"
            class="text-[2rem] group-hover:hidden! block!"
          />
        </div>
      </div>
    </div>
    <h3 class="subtitle-3 font-semibold break-words">
      {{ label }}
    </h3>
  </NuxtLink>
</template>
