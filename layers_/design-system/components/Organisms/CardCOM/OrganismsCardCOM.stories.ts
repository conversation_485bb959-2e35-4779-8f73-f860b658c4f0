import type { Meta, StoryObj } from '@storybook/vue3'
import OrganismsCardCOM from './OrganismsCardCOM.vue'

// Define metadata for the component
const meta: Meta<typeof OrganismsCardCOM> = {
  title: 'Organisms/CardCOM',
  component: OrganismsCardCOM,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        // Minimal description as requested
        component: 'Card component used within the COM (Customer Order Management) context.',
      },
    },
    // Add a dark background similar to index.vue for better visibility
    backgrounds: {
      default: 'dark',
      values: [{ name: 'dark', value: '#333333' }], // Example dark color
    },
  },
  argTypes: {
    label: {
      control: 'text',
      description: 'The main title displayed on the card',
    },
    to: {
      control: 'text',
      description: 'The URL destination when the card is clicked',
    },
    external: {
      control: 'boolean',
      description: 'If true, displays an external link icon; otherwise, displays a window icon.',
    },
  },
  args: {
    // Default args based on the first example in index.vue
    label: 'Modernfold Expands Production Facilities and Reduces Lead Times',
    to: 'https://www.modernfold.com/en-US/document/34c89590-e3ee-11eb-9833-7debb9b2ecfd?open=true',
    external: true,
  },
}

export default meta
type Story = StoryObj<typeof meta>

// Wrapper component to provide layout context similar to index.vue
const StoryWrapper = {
  template: `
    <div class="flex gap-4 p-4">
      <slot />
    </div>
  `,
}

// Default Story: Uses the default args (external = true)
export const Default: Story = {
  args: {},
}

// Internal Link Story: Explicitly sets external to false
export const InternalLink: Story = {
  args: {
    external: false,
    label: 'View Internal Document',
    to: '/internal/docs/some-document',
  },
}

// Showcase of multiple cards as seen in index.vue
export const Showcase: Story = {
  render: () => ({
    components: {
      OrganismsCardCOM,
      StoryWrapper,
    },
    setup() {
      // Define args for each card in the showcase
      const cardArgs = [
        {
          label: 'Modernfold Expands Production Facilities and Reduces Lead Times',
          to: 'https://www.modernfold.com/en-US/document/34c89590-e3ee-11eb-9833-7debb9b2ecfd?open=true',
          external: true,
        },
        {
          label: 'Internal Portal Link',
          to: '/portal/dashboard',
          external: false,
        },
        {
          label: 'Skyfold Website Link',
          to: 'https://www.skyfold.com/',
          external: true,
        },
        {
          label: 'Another Internal Page',
          to: '/internal/settings',
          external: false,
        },
      ]
      return { cardArgs }
    },
    template: `
      <StoryWrapper>
        <OrganismsCardCOM v-for="(card, index) in cardArgs" :key="index" v-bind="card" />
      </StoryWrapper>
    `,
  }),
  // Disable controls for the showcase story as args are defined internally
  parameters: {
    controls: { hideNoControlsWarning: true, disabled: true },
  },
}
