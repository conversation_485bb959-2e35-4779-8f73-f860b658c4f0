<script setup lang='ts'>
const props = defineProps<{
  refineSearch: (value: string) => void
}>()

const searchValue = defineModel<string>({ default: '' })

watch(searchValue, () => {
  props.refineSearch(searchValue.value)
})
</script>

<template>
  <div>
    <OrganismsSearchBar
      v-model="searchValue"
      variant="contextual"
      :placeholder="$ts('portfolio.filters.searchSomething')"
    />
  </div>
</template>
