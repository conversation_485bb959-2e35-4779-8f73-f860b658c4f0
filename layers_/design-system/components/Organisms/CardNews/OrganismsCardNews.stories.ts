import type { StoryObj } from '@storybook/vue3'
import OrganismsCardNews from './OrganismsCardNews.vue'

// Define metadata for the component
const meta = {
  title: 'Organisms/CardNews',
  component: OrganismsCardNews,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component: `
### Component Description

The \`OrganismsCardNews\` component displays a news item, typically containing a title, an icon, a publication date, and a link destination. It's styled with a dark, blurred background suitable for overlays.

### Usage Guidelines

1.  **Basic Structure**
    *   Use the \`OrganismsCardNews\` component directly.
    *   Provide the \`title\` prop for the news headline.
    *   Provide the \`date\` prop using any valid date string format (e.g., 'YYYY-MM-DDTHH:mm:ss', 'MM/DD/YYYY', 'Month Day, Year'). If the date string is not valid, the date will not be displayed and an error will be logged to the console.
    *   Provide the \`to\` prop with the URL for the link (e.g., path to a PDF or external site).

### Example Usage

\`\`\`vue
<OrganismsCardNews
  title="Modernfold Expands Production Facilities and Reduces Lead Times"
  date="2024-03-28T12:30:00"
  to="/path/to/document.pdf"
/>
\`\`\`
        `,
      },
    },
  },
  argTypes: {
    title: {
      control: 'text',
      description: 'The main title/headline of the news card',
    },
    date: {
      control: 'text',
      description:
        'The publication date of the news item. Accepts any valid date string format. If invalid, the date wont be displayed.',
    },
    to: {
      control: 'text',
      description: 'The URL destination when the card is clicked',
    },
  },
  args: {
    title: 'Modernfold Expands Production Facilities and Reduces Lead Times',
    date: '2024-03-28T12:30:00',
    to: 'https://example.com/document.pdf', // Default link for stories
  },
}

export default meta
type Story = StoryObj<typeof meta>

// Wrapper component to constrain width and provide context like in index.vue
const StoryWrapper = {
  template: `
    <div class="mt-8 p-padding-md rounded-extra-small relative bg-[url('https://images.unsplash.com/photo-1742943679521-f4736500a471?q=80&w=3870&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D')] bg-cover bg-center after:content-[''] after:absolute after:inset-0 after:bg-blue-800/40 after:z-1">
      <div class="relative z-10 max-w-sm">
        <slot />
      </div>
    </div>
  `,
}

// Default story replicating the usage in index.vue
export const Default: Story = {
  args: {},
  render: args => ({
    components: {
      OrganismsCardNews,
      StoryWrapper,
    },
    setup() {
      return { args }
    },
    template: `
      <StoryWrapper>
        <OrganismsCardNews v-bind="args" />
      </StoryWrapper>
    `,
  }),
}

// Story with a very long title to test text wrapping/balance
export const LongTitle: Story = {
  args: {
    title:
      'Exciting News: Dormakaba Announces Major Breakthrough in Sustainable Access Solutions, Paving the Way for Greener Buildings Globally and Reducing Environmental Impact Significantly Across Product Lines',
    date: '2024-07-15T09:00:00',
    to: 'https://example.com/long-title-news.pdf',
  },
  render: args => ({
    components: {
      OrganismsCardNews,
      StoryWrapper,
    },
    setup() {
      return { args }
    },
    template: `
      <StoryWrapper>
        <OrganismsCardNews v-bind="args" />
      </StoryWrapper>
    `,
  }),
}

// Story with an invalid date to show error handling (check console)
export const InvalidDate: Story = {
  args: {
    title: 'News Item with Invalid Date',
    date: 'invalid-date-string',
    to: 'https://example.com/invalid-date-news.pdf',
  },
  render: args => ({
    components: {
      OrganismsCardNews,
      StoryWrapper,
    },
    setup() {
      return { args }
    },
    template: `
      <StoryWrapper>
        <OrganismsCardNews v-bind="args" />
      </StoryWrapper>
    `,
  }),
}
