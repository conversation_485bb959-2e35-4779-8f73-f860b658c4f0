<script setup lang="ts">
import type { OrganismsCardNewsProps } from './OrganismsCardNews.props'

const props = defineProps<OrganismsCardNewsProps>()
const formattedDate = props.date ? useFormattedDate(props.date) : undefined
</script>

<template>
  <div class="flex flex-col gap-2 bg-black-opacity-black-80% text-invert p-padding-md rounded-extra-small border-b-2 border-invert backdrop-blur-xs hover:bg-hover cursor-pointer min-h-31">
    <a
      :href="to"
      target="_blank"
      rel="noopener noreferrer"
      class="absolute inset-0 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
      :aria-label="`Open ${title} in new tab`"
    />
    <div class="flex items-top gap-2 grow">
      <AtomsIcon name="ExtPDFIcon" class="shrink-0 text-[2rem]" />
      <h3 class="body-1 text-balance">
        {{ title }}
      </h3>
    </div>
    <p v-if="formattedDate" class="caption-1 ml-auto">
      <time :datetime="formattedDate">{{ formattedDate }}</time>
    </p>
  </div>
</template>
