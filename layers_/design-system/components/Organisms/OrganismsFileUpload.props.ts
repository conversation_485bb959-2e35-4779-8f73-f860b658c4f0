export interface OrganismsFileUploadProps {
  /**
   * Accepted file types (MIME types or file extensions)
   * @example ['image/*', '.pdf', '.doc']
   */
  accept?: `${string}/${string}`[]

  /**
   * Maximum file size in bytes
   * @default 10485760 (10MB)
   */
  maxSize?: number

  /**
   * Whether the component is disabled
   * @default false
   */
  disabled?: boolean

  /**
   * Custom CSS classes
   */
  class?: string

  /**
   * Currently selected file
   */
  modelValue?: File | null

  /**
   * Whether the component is read only
   * @default false
   */
  readOnly?: boolean
}
