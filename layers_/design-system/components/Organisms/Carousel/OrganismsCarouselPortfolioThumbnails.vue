<script setup lang="ts">
import type { EmblaOptionsType } from 'embla-carousel'
import type { OrganismsCarouselPortfolioThumbnailsProps } from './OrganismsCarouselPortfolioThumbnails.props'

const props = defineProps<OrganismsCarouselPortfolioThumbnailsProps>()

const options: EmblaOptionsType = {
  loop: true,
  startIndex: 0,
  axis: 'x',
  align: 'start',
  breakpoints: {
    '(min-width: 768px)': { axis: 'y' },
  },
}

const emit = defineEmits<{
  onSelectedThumbnail: [id: number]
}>()

const { emblaRef, emblaApi, scrollNext, scrollPrev } = useEmblaCarousel(options)

const selectSlide = (index: number) => {
  emit('onSelectedThumbnail', index)
}

watchEffect(() => {
  if (emblaApi.value) {
    emblaApi.value.scrollTo(props.selectedSlideIndex)
  }
})

defineExpose({
  scrollNext,
  scrollPrev,
})
</script>

<template>
  <div ref="emblaRef" class="embla ">
    <div :class="[slides.length < 4 ? 'justify-start' : 'justify-between', 'embla__container md:flex-col items-center md:!m-0']">
      <div
        v-for="(slide, index) in slides"
        :key="`slide-${index}`"
        class="embla__slide md:!p-0 flex justify-center items-center"
        @click="selectSlide(index)"
      >
        <img
          :id="`thumbnail-${index}`"
          :src="slide.url"
          :title="slide.title"
          width="1200"
          height="900"
          :alt="slide.alt"
          :class="[selectedSlideIndex === index ? 'border-2 border-accent' : 'border border-neutral', 'aspect-[4/3]  object-cover h-auto cursor-pointer']"
        >
      </div>
    </div>
  </div>
</template>

  <style scoped>
  .embla {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    --gap: 0.25rem;
    --cards: 4;
  }

  .embla__container {
    display: flex;
    height: 100%;
    margin: 0 calc(var(--gap) * -1);

  }

  .embla__slide {
    transform: translate3d(0, 0, 0);
    flex: 0 0 calc(100% / var(--cards));
    padding: 0 var(--gap);
    min-height: 0;
  }
</style>
