import type { StoryObj } from '@storybook/vue3'
import OrganismsCarouselCards from './OrganismsCarouselCards.vue'

// Define metadata for the component
const meta = {
  title: 'Organisms/Carousels/CarouselCards',
  component: OrganismsCarouselCards,
  args: {
    slides: [
      {
        title: 'Slide 1',
        alt: 'Description for Slide 1',
        url: 'https://picsum.photos/400/300',
      },
      {
        title: 'Slide 2',
        alt: 'Description for Slide 2',
        url: 'https://picsum.photos/400/300',
      },
      {
        title: 'Slide 3',
        alt: 'Description for Slide 3',
        url: 'https://picsum.photos/400/300',
      },
      {
        title: 'Slide 4',
        alt: 'Description for Slide 4',
        url: 'https://picsum.photos/400/300',
      },
      {
        title: 'Slide 5',
        alt: 'Description for Slide 5',
        url: 'https://picsum.photos/400/300',
      },
      {
        title: 'Slide 6',
        alt: 'Description for Slide 6',
        url: 'https://picsum.photos/400/300',
      },
    ],
    seeAllUrl: '/',
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => ({
    components: { OrganismsCarouselCards },
    setup() {
      return { args }
    },
    template: ` 
    <div class="bg-blue-900 lg:grid grid-cols-12 h-screen pt-13">
      <div class="lg:col-span-8 xl:col-span-9 hidden lg:block">
        PLACEHOLDER
      </div>
      <OrganismsCarouselCards v-bind="args" class="col-span-full lg:pl-6 lg:pr-8 xl:pr-0 xl:pl-8 lg:col-span-4 xl:col-span-3 max-h-[600px] mx-auto">
        <div v-for="(slide, index) in args.slides" :key="index" class="pl-padding-md lg:pl-0 last:pr-4 lg:last:pr-0">
          <div class="rounded-xs bg-black-opacity-black-80% h-[120px] w-[280px] flex items-center justify-center text-white">
            {{ slide.title }}
          </div>
        </div>
      </OrganismsCarouselCards>
    </div>
    `,
  }),
}
