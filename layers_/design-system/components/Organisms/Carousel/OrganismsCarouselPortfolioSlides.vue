<script setup lang="ts">
import type { EmblaOptionsType, EmblaPluginType } from 'embla-carousel'
import Autoplay from 'embla-carousel-autoplay'
import type { OrganismsCarouselPortfolioSlidesProps } from './OrganismsCarouselPortfolioSlides.props'

const props = withDefaults(defineProps<OrganismsCarouselPortfolioSlidesProps>(), {
  hasAutoplay: false,
  delay: 4000,
  stopOnMouseEnter: true,
})

const emit = defineEmits<{
  onSlideChange: [id: number]
}>()

const autoplayPlugin: EmblaPluginType = Autoplay({ delay: props.delay, stopOnMouseEnter: props.stopOnMouseEnter, stopOnInteraction: false })

const options: EmblaOptionsType = {
  loop: true,
  startIndex: 0,
}

const { emblaRef, emblaApi } = useEmblaCarousel(options, props.hasAutoplay ? [autoplayPlugin] : [])

watchEffect(() => {
  if (emblaApi.value) {
    emblaApi.value.scrollTo(props.selectedSlideIndex)
  }
})

onMounted(() => {
  if (!emblaApi.value) return

  emblaApi.value.on('select', () => {
    if (!emblaApi.value) return
    emit('onSlideChange', emblaApi.value.selectedScrollSnap())
  })
})

onUnmounted(() => {
  if (emblaApi.value) {
    emblaApi.value.destroy()
  }
})
</script>

<template>
  <div ref="emblaRef" class="embla overflow-hidden">
    <div class="embla__container flex">
      <div
        v-for="(slide, index) in slides"
        :key="index"
        class="embla__slide flex-[0_0_100%] min-w-0 w-full aspect-[4/3]"
      >
        <img
          :id="`img-${index}`"
          :src="slide.url"
          :title="slide.title"
          width="1200"
          height="900"
          :alt="slide.alt"
          class="aspect-[4/3] object-cover h-auto w-full"
        >
      </div>
    </div>
  </div>
</template>
