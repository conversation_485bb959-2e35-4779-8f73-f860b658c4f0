<script setup lang="ts">
import type { EmblaCarouselType } from 'embla-carousel'
import type { OrganismsCarouselPortfolioProps } from './OrganismsCarouselPortfolio.props'

const props = defineProps <OrganismsCarouselPortfolioProps>()

const thumbnailsCarousel = ref<EmblaCarouselType | null>(null)

const scrollNext = () => {
  thumbnailsCarousel.value?.scrollNext()
}

const scrollPrev = () => {
  thumbnailsCarousel.value?.scrollPrev()
}

const selectedSlideIndex = ref(0)
</script>

<template>
  <div class="flex flex-col md:grid md:grid-cols-8 lg:grid-cols-12">
    <div class="md:col-span-7 lg:col-span-10">
      <OrganismsCarouselPortfolioSlides
        v-bind="props"
        :selected-slide-index="selectedSlideIndex"
        @on-slide-change="selectedSlideIndex = $event"
      />
    </div>
    <div class="md:col-span-1 lg:col-span-2 py-padding-xl md:px-padding-xl lg:px-padding-xxl md:h-0 md:min-h-full flex flex-col">
      <div :class="[{ 'px-8 md:px-0': slides.length < 4 }, 'flex md:flex-col h-full justify-between items-center']">
        <button
          v-if="slides.length > 4"
          aria-label="Previous Slide"
          class="cursor-pointer h-8"
          @click="scrollPrev"
        >
          <AtomsIcon
            name="NavChevronUp"
            class="text-[2rem] !shrink-0 -rotate-90 md:rotate-0"
          />
        </button>

        <OrganismsCarouselPortfolioThumbnails
          ref="thumbnailsCarousel"
          :selected-slide-index="selectedSlideIndex"
          :slides="slides"
          @on-selected-thumbnail="selectedSlideIndex = $event"
        />

        <button
          v-if="slides.length > 4"
          aria-label="Next Slide"
          class="cursor-pointer h-8"
          @click="scrollNext"
        >
          <AtomsIcon
            name="NavChevronDown"
            class="text-[2rem] !shrink-0 -rotate-90 md:rotate-0"
          />
        </button>
      </div>
    </div>
  </div>
</template>
