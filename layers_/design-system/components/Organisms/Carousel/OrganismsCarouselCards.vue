<script setup lang="ts">
import type { EmblaOptionsType } from 'embla-carousel'
import type { OrganismsCarouselCardsProps } from './OrganismsCarouselCards.props'

defineProps<OrganismsCarouselCardsProps>()

const options: EmblaOptionsType = {
  loop: false,
  startIndex: 0,
  axis: 'x',
  align: 'start',
  breakpoints: {
    '(min-width: 1024px)': { active: false, axis: 'y' },
  },
}

const { emblaRef, emblaApi } = useEmblaCarousel(options)

const selectedIndex = ref(0)
const scrollSnaps = ref<number[]>([])

const onSelect = () => {
  if (emblaApi.value) {
    selectedIndex.value = emblaApi.value.selectedScrollSnap()
  }
}

const handleDotNavigation = (index: number) => {
  if (emblaApi.value) emblaApi.value.scrollTo(index)
}

const updateScrollSnaps = () => {
  if (emblaApi.value) {
    scrollSnaps.value = emblaApi.value.scrollSnapList()
  }
}

onMounted(() => {
  if (emblaApi.value) {
    updateScrollSnaps()
    emblaApi.value.on('select', onSelect)
    emblaApi.value.on('resize', updateScrollSnaps)
    onSelect()
  }
})

onUnmounted(() => {
  if (emblaApi.value) {
    emblaApi.value.destroy()
  }
})
</script>

<template>
  <div class="flex flex-col max-lg:-mx-(--grid-system-spacing-x) lg:h-full lg:w-full">
    <div class="w-full flex justify-between items-center pb-padding-sm text-invert lg:px-0 shrink-0 max-lg:px-(--grid-system-spacing-x)">
      <h4 class="headline-4">
        {{ $ts("organisms.carouselCards.whatsNew") }}
      </h4>
      <AtomsLink inverted :to="seeAllUrl">
        {{ $ts("organisms.carouselCards.seeAll") }}
      </AtomsLink>
    </div>
    <div ref="emblaRef" class="embla relative w-full h-full overflow-hidden lg:overflow-y-auto grow">
      <div class="embla__container flex h-full first:pl-0 pl-padding-md lg:gap-3 lg:pl-0 lg:flex-col items-center *:max-lg:not-last:pr-padding-md max-lg:first:ml-(--grid-system-spacing-x) w-full">
        <slot />
      </div>
    </div>
    <div class="pl-padding-md lg:pl-0 lg:hidden flex items-center gap-[5px] pt-padding-sm">
      <button
        v-for="(n, index) in scrollSnaps"
        :key="n"
        :class="[index === selectedIndex ? 'w-1.5 h-1.5' : 'w-[3px] h-[3px]', 'cursor-pointer bg-neutral rounded-full']"
        @click="handleDotNavigation(index)"
      />
    </div>
  </div>
</template>
