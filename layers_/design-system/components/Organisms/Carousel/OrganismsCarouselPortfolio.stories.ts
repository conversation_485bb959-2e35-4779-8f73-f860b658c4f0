import type { StoryObj } from '@storybook/vue3'
import OrganismsCarouselPortfolio from './OrganismsCarouselPortfolio.vue'

// Define metadata for the component
const meta = {
  title: 'Organisms/Carousels/CarouselPortfolio',
  component: OrganismsCarouselPortfolio,
  args: {
    slides: [
      {
        title: 'Slide 1',
        alt: 'Description for Slide 1',
        url: 'https://picsum.photos/400/300',
      },
      {
        title: 'Slide 2',
        alt: 'Description for Slide 2',
        url: 'https://picsum.photos/400/300',
      },
      {
        title: 'Slide 3',
        alt: 'Description for Slide 3',
        url: 'https://picsum.photos/400/300',
      },
      {
        title: 'Slide 4',
        alt: 'Description for Slide 4',
        url: 'https://picsum.photos/400/300',
      },
      {
        title: 'Slide 5',
        alt: 'Description for Slide 5',
        url: 'https://picsum.photos/400/300',
      },
      {
        title: 'Slide 6',
        alt: 'Description for Slide 6',
        url: 'https://picsum.photos/400/300',
      },
    ],
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => ({
    components: { OrganismsCarouselPortfolio },
    setup() {
      return { args }
    },
    template: ` 
    <div class="max-w-[1200px] mx-auto">
    <OrganismsCarouselPortfolio v-bind="args"></OrganismsCarouselPortfolio> 
    </div>
    `,
  }),
}

export const withAutoplay: Story = {
  render: args => ({
    components: { OrganismsCarouselPortfolio },
    setup() {
      return { args }
    },
    template: ` 
    <div class="max-w-[1200px] mx-auto">
    <OrganismsCarouselPortfolio v-bind="args"></OrganismsCarouselPortfolio> 
    </div>
    `,
  }),
  args: {
    hasAutoplay: true,
    delay: 4000,
    stopOnMouseEnter: true,
  },
}

export const ThreeSlidesVersion: Story = {
  render: args => ({
    components: { OrganismsCarouselPortfolio },
    setup() {
      return { args }
    },
    template: ` 
    <div class="max-w-[1200px] mx-auto">
    <OrganismsCarouselPortfolio v-bind="args"></OrganismsCarouselPortfolio>
    </div>
    `,
  }),
  args: {
    slides: [
      {
        title: 'Slide 1',
        alt: 'Description for Slide 1',
        url: 'https://picsum.photos/400/300',
      },
      {
        title: 'Slide 2',
        alt: 'Description for Slide 2',
        url: 'https://picsum.photos/400/300',
      },
      {
        title: 'Slide 3',
        alt: 'Description for Slide 3',
        url: 'https://picsum.photos/400/300',
      },
    ],
  },
}
