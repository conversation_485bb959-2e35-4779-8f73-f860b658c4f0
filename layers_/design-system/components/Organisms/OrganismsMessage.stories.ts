import type { Meta, StoryObj } from '@storybook/vue3'
import OrganismsMessage from './OrganismsMessage.vue'

const meta: Meta<typeof OrganismsMessage> = {
  title: 'Organisms/Message',
  component: OrganismsMessage,
  tags: ['autodoc'],
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=4073-2427',
      },
    ],
  },
  argTypes: {
    title: {
      control: 'text',
    },
    description: {
      control: 'text',
    },
    status: {
      control: 'select',
      options: ['info', 'success', 'alert', 'error'],
    },
    size: {
      control: 'select',
      options: ['s', 'l'],
    },
  },
  args: {
    title: 'Message sent successfully',
    description: 'Et harum quidem rerum facilis est et expedita distinctio. Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus.',
    status: 'info',
    size: 'l',
  },
  decorators: [
    () => ({
      template: '<div class="flex flex-col gap-8"><story/></div>',
    }),
  ],
}

export default meta
type Story = StoryObj<typeof OrganismsMessage>

export const Success: Story = {
  render: args => ({
    setup() {
      return { args }
    },
    components: { OrganismsMessage },
    template: `
      <div>
        <p>L</p>
        <OrganismsMessage
          v-bind=args
          size="l"
        />
      </div>

      <div>
        <p>S</p>
        <OrganismsMessage
          v-bind=args
          size="s"
        />
      </div>
    `,
  }),
  args: {
    title: 'Message sent successfully',
    description: 'Et harum quidem rerum facilis est et expedita distinctio. Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus.',
    status: 'success',
  },
}

export const Alert: Story = {
  render: args => ({
    setup() {
      return { args }
    },
    components: { OrganismsMessage },
    template: `
      <div>
        <p>L</p>
        <OrganismsMessage
          v-bind=args
          size="l"
        />
      </div>

      <div>
        <p>S</p>
        <OrganismsMessage
          v-bind=args
          size="s"
        />
      </div>
    `,
  }),
  args: {
    title: 'Pay attention! Your password is expired. Please, change your password. Max lenght is three lines',
    description: 'Et harum quidem rerum facilis est et expedita distinctio. Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus.',
    status: 'alert',
  },
}

export const Error: Story = {
  render: args => ({
    setup() {
      return { args }
    },
    components: { OrganismsMessage },
    template: `
      <div>
        <p>L</p>
        <OrganismsMessage
          v-bind=args
          size="l"
        />
      </div>

      <div>
        <p>S</p>
        <OrganismsMessage
          v-bind=args
          size="s"
        />
      </div>
    `,
  }),
  args: {
    title: 'Error!',
    description: 'Et harum quidem rerum facilis est et expedita distinctio. Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus.',
    status: 'error',
  },
}

export const Info: Story = {
  render: args => ({
    setup() {
      return { args }
    },
    components: { OrganismsMessage },
    template: `
      <div>
        <p>L</p>
        <OrganismsMessage
          v-bind=args
          size="l"
        />
      </div>

      <div>
        <p>S</p>
        <OrganismsMessage
          v-bind=args
          size="s"
        />
      </div>
    `,
  }),
  args: {
    title: 'Info for you',
    description: 'Et harum quidem rerum facilis est et expedita distinctio. Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus.',
    status: 'info',
  },
}
