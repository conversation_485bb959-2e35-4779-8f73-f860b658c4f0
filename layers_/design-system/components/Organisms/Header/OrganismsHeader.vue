<script setup lang="ts">
import type { MenuItem } from '@design-system/components/Organisms/Menu/OrganismsMenuDropdown.props'
import type { OrganismsHeaderProps } from './OrganismsHeader.props'
import type { SiteType } from '~~/env'

const config = useRuntimeConfig()
const site: SiteType = config.public.site as SiteType
const { ts, $getLocale, localePath } = useI18n()
const { currentLanguage, languageMenu } = useLanguage()

const { data: menuItems } = await useFetch<MenuItem[]>(() => `/api/${$getLocale()}/storyblok/menus/header`, {
  key: `menu-header`,
})

const props = withDefaults(defineProps<OrganismsHeaderProps>(), {
  hideSearch: false,
})
const header = tv({
  slots: {
    root: 'bg-neutral z-10 relative',
    container: '*:flex *:items-center *:justify-between *:py-padding-sm *:md:pt-padding-xl *:md:pb-padding-md *:container *:gap-2 *:md:gap-4 *:lg:gap-8 border-b border-neutral',
    nav: 'bg-brand hidden lg:block isolate',
    navList: 'container flex gap-8',
  },
  variants: {
    anatomy: {
      default: {
        root: 'bg-neutral shadow-300',
      },
      transparent: {
        root: 'bg-transparent',
        nav: 'bg-transparent',
      },
    },
  },
  defaultVariants: {
    anatomy: 'default',
  },
})

const headerClass = computed(() => {
  return header({
    anatomy: props.anatomy,
  })
})

// Ref for the header element
const headerRef = ref<HTMLElement | null>(null)
const headerHightCSSVar = useCssVar('--header-height')

const menuAccount: MenuItem[] = [
  {
    label: 'Account',
    to: '#',
    items: [
      {
        label: ts('header.account.publicWebsite'),
        target: '_blank',
        to: site === 'modernfold' ? 'https://www.modernfold.com' : 'https://www.skyfold.com',
        icon: 'GenWorldIcon',
      },
      {
        label: ts('header.account.changePassword'),
        to: localePath('/change-password'),
        icon: 'GenEditIcon',
      },
      {
        label: ts('header.account.logout'),
        target: '_self',
        external: true,
        to: '/auth0/logout',
        icon: 'NavLogout',

      },
    ],
  },
]

useResizeObserver(headerRef, (entries) => {
  const entry = entries[0]
  const height = entry?.contentRect.height
  headerHightCSSVar.value = `${height}px`
})

onMounted(() => {
  nextTick(() => {
    const height = headerRef.value?.getBoundingClientRect().height
    headerHightCSSVar.value = `${height}px`
  })
})

const route = useRoute()

const isSelected = (item?: MenuItem): boolean => {
  if (!item || route.path === '/') return false

  if (normalizeUrl(route.path) === normalizeUrl(item.to || '')) return true

  if (item.items?.length) {
    return item.items.some(child => isSelected(child))
  }
  return false
}

const navItems = computed(() => {
  return menuItems.value?.map(item => ({
    ...item,
    selected: isSelected(item),
  }))
})
</script>

<template>
  <header ref="headerRef" :class="headerClass.root() ">
    <div :class="headerClass.container()">
      <div>
        <OrganismsMenuMobile
          v-if="!hideNav"
          :theme="anatomy === 'transparent' ? 'inverted' : 'default'"
          :menu-items="menuItems || []"
        >
          <template #before-bottom>
            <slot name="menu-mobile-before-bottom" />
          </template>
        </OrganismsMenuMobile>
        <div v-else class="lg:hidden" />

        <div class="flex flex-col gap-sm items-start" :class="{ 'lg:max-w-[278px]': $slots['after-logo'] }">
          <OrganismsHeaderLogo :brand="site" :variant="anatomy === 'transparent' ? 'white' : 'colored'" />
          <slot name="after-logo" />
        </div>

        <div class="min-w-8 lg:flex-1">
          <OrganismsSearch v-if="!hideSearch" />
        </div>
        <div v-if="!hideNav" class="gap-xl hidden lg:flex *:text-nowrap *:antialiased">
          <template v-for="item in languageMenu" :key="item.label">
            <OrganismsMenuDropdown
              :link="{
                label: currentLanguage?.label || '',
                icon: { name: currentLanguage?.icon || '', local: false },
                inverted: anatomy === 'transparent',
              }"
              :items="item.items"
              is-end-aligned
              is-account-language-variant
            />
          </template>
          <template v-for="item in menuAccount" :key="item.label">
            <OrganismsMenuDropdown
              :link="{
                label: $ts('header.account.title'),
                icon: { name: 'NavAccessIcon' },
                inverted: anatomy === 'transparent',
              }"
              :items="item.items"
              is-end-aligned
              is-account-language-variant
            />
          </template>
        </div>
      </div>
    </div>
    <nav :class="headerClass.nav()">
      <div :class="headerClass.navList()">
        <template v-for="item in navItems" :key="item.label">
          <OrganismsMenuDropdown
            v-if="!item.hidden"
            :link="{
              asHeaderLink: true,
              label: item.label,
              to: item.to,
              external: item.external,
              selected: item.selected,
              inverted: anatomy === 'transparent',
              disabled: hideNav,
            }"
            :items="item.items"
          />
        </template>
      </div>
    </nav>
  </header>
</template>
