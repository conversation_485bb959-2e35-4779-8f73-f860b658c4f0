import type { StoryObj } from '@storybook/vue3'
import OrganismsHeader from './OrganismsHeader.vue'
import OrganismsHeaderLogo from './OrganismsHeaderLogo.vue'

// Define metadata for the component
const meta = {
  title: 'Organisms/Header',
  component: OrganismsHeader,
  argTypes: {
    anatomy: {
      control: 'select',
      options: ['default', 'transparent'],
      description: 'The visual style of the header. Transparent is typically used for hero sections with dark backgrounds.',
    },
    hideSearch: {
      control: 'boolean',
      description: 'Controls the visibility of the search bar in the header.',
    },
  },
  args: {
    anatomy: 'default',
    hideSearch: false,
  },
  parameters: {
    layout: 'fullscreen',
  },
}

export default meta
type Story = StoryObj<typeof meta>

// Modernfold variants
export const ModernfoldDefault: Story = {
  render: args => ({
    components: { OrganismsHeader, OrganismsHeaderLogo },
    setup() {
      return { args }
    },
    template: `
      <OrganismsHeader v-bind="args">
        <template #logo>
          <OrganismsHeaderLogo brand="modernfold" variant="colored" />
        </template>
      </OrganismsHeader>
    `,
  }),
}

export const ModernfoldTransparent: Story = {
  args: {
    anatomy: 'transparent',
    hideSearch: true,
  },
  decorators: [
    story => ({
      components: { story },
      template: '<div class="bg-blue-800"><story /></div>',
    }),
  ],
  render: args => ({
    components: { OrganismsHeader, OrganismsHeaderLogo },
    setup() {
      return { args }
    },
    template: `
      <OrganismsHeader v-bind="args">
        <template #logo>
          <OrganismsHeaderLogo brand="modernfold" variant="white" />
        </template>
      </OrganismsHeader>
    `,
  }),
}

// Skyfold variants
export const SkyfoldDefault: Story = {
  render: args => ({
    components: { OrganismsHeader, OrganismsHeaderLogo },
    setup() {
      return { args }
    },
    template: `
      <OrganismsHeader v-bind="args">
        <template #logo>
          <OrganismsHeaderLogo brand="skyfold" variant="colored" />
        </template>
      </OrganismsHeader>
    `,
  }),
}

export const SkyfoldTransparent: Story = {
  args: {
    anatomy: 'transparent',
    hideSearch: true,
  },
  decorators: [
    story => ({
      components: { story },
      template: '<div class="bg-blue-800"><story /></div>',
    }),
  ],
  render: args => ({
    components: { OrganismsHeader, OrganismsHeaderLogo },
    setup() {
      return { args }
    },
    template: `
      <OrganismsHeader v-bind="args">
        <template #logo>
          <OrganismsHeaderLogo brand="skyfold" variant="white" />
        </template>
      </OrganismsHeader>
    `,
  }),
}

// Comprehensive showcase
export const AllVariants: Story = {
  render: () => ({
    components: { OrganismsHeader, OrganismsHeaderLogo },
    setup() {
      return {}
    },
    template: `
      <div class="space-y-8">
        <!-- Modernfold Default -->
        <div>
          <h3 class="text-lg font-bold mb-2">Modernfold Default</h3>
          <OrganismsHeader>
            <template #logo>
              <OrganismsHeaderLogo brand="modernfold" variant="colored" />
            </template>
          </OrganismsHeader>
        </div>

        <!-- Modernfold Transparent -->
        <div class="bg-blue-800">
          <h3 class="text-lg font-bold mb-2 text-white">Modernfold Transparent</h3>
          <OrganismsHeader anatomy="transparent" hide-search>
            <template #logo>
              <OrganismsHeaderLogo brand="modernfold" variant="white" />
            </template>
          </OrganismsHeader>
        </div>

        <!-- Skyfold Default -->
        <div>
          <h3 class="text-lg font-bold mb-2">Skyfold Default</h3>
          <OrganismsHeader>
            <template #logo>
              <OrganismsHeaderLogo brand="skyfold" variant="colored" />
            </template>
          </OrganismsHeader>
        </div>

        <!-- Skyfold Transparent -->
        <div class="bg-blue-800">
          <h3 class="text-lg font-bold mb-2 text-white">Skyfold Transparent</h3>
          <OrganismsHeader anatomy="transparent" hide-search>
            <template #logo>
              <OrganismsHeaderLogo brand="skyfold" variant="white" />
            </template>
          </OrganismsHeader>
        </div>
      </div>
    `,
  }),
}
