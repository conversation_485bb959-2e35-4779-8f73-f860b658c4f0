<script setup lang="ts">
import useBrandLogo from '@design-system/composables/useHeaderLogo'
import type { UseHeaderLogoProps } from '@design-system/composables/useHeaderLogo.props'

const props = defineProps<UseHeaderLogoProps>()

const { localePath } = useI18n()
const { logoName } = useBrandLogo(props)
</script>

<template>
  <div class="h-8 md:h-9.5 lg:h-11 lg:w-full md:shrink-0 relative w-auto">
    <NuxtLink :to="localePath('/')" class="absolute inset-0 block peer" />
    <AtomsIcon
      :name="logoName"
      class="h-full w-auto lg:mr-auto"
      mode="svg"
    />
  </div>
</template>
