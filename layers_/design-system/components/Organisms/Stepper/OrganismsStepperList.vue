<script setup lang="ts" generic="T extends string">
import type { OrganismsStepperListProps } from './OrganismsStepperList.props'

defineProps<OrganismsStepperListProps<T>>()
defineEmits<{
  (e: 'onStepClick', step: T): void
}>()
</script>

<template>
  <ul class="w-full flex gap-6 justify-between overflow-x-auto [scrollbar-width:none] [&::-webkit-scrollbar]:hidden" aria-label="Steps" role="list">
    <li v-for="step in steps" :key="step.id" class="flex flex-auto justify-center">
      <OrganismsStepperStep
        :id="step.id"
        :title="step.title"
        :index="step.index + 1"
        :selected="step.id === activeStep.id"
        :disabled="step.id !== activeStep.id && step.index > lastCompletedIndex"
        @on-click-handler="$emit('onStepClick', step.id)"
      />
    </li>
  </ul>
</template>
