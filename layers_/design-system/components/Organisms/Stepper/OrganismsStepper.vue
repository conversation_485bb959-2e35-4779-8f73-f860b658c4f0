<script setup lang="ts" generic="T extends string">
import type { OrganismsStepperProps } from './OrganismsStepper.props'

const props = defineProps<OrganismsStepperProps<T>>()
const emits = defineEmits<{
  (e: 'onStepChange', step: T): void
}>()

const {
  values: stepValues,
  keys: stepKeys,
  active: activeStep,
  isLast: isLastStep,
  isFirst: isFirstStep,
  lastCompleted: lastCompletedStep,
  goToStepByName,
  goToPreviousStep,
  goToNextStep,
  direction,
} = useStepNavigator(props.steps)

watch(activeStep, activeStep => emits('onStepChange', activeStep.id), { immediate: true })
</script>

<template>
  <OrganismsStepperHeader
    :steps="stepValues"
    :active-step="activeStep"
    :last-completed-index="lastCompletedStep?.index ?? 0"
    @on-step-change="goToStepByName"
  />
  <div class="w-full mt-padding-sm relative max-md:overflow-hidden">
    <div v-for="(key, index) in stepKeys" :key="key">
      <Transition
        :enter-from-class="direction === 'forward' ? 'max-md:translate-x-[calc(100%+var(--grid-system-gutter-x))]' : 'max-md:-translate-x-[calc(100%+var(--grid-system-gutter-x))]'"
        :enter-active-class="animateOnMobile ? 'max-md:duration-300 transition-transform ease max-md:absolute max-md:inset-0' : ''"
        enter-to-class="max-md:translate-x-0"
        :leave-from-class="animateOnMobile ? 'translate-x-0' : 'hidden'"
        :leave-active-class="animateOnMobile ? 'max-md:duration-300 md:hidden! transition-transform ease' : ''"
        :leave-to-class="direction === 'forward' ? 'max-md:-translate-x-[calc(100%+var(--grid-system-gutter-x))]' : 'max-md:translate-x-[calc(100%+var(--grid-system-gutter-x))]'"
      >
        <div
          v-show="index === activeStep.index"
        >
          <slot
            :name="key"
            :step-name="key"
            :go-to-step-by-name="goToStepByName"
            :go-to-next-step="goToNextStep"
          />
        </div>
      </Transition>
    </div>
  </div>
  <template v-if="$slots.actions">
    <slot
      name="actions"
      :active-step="activeStep"
      :has-next-step="!isLastStep"
      :has-previous-step="!isFirstStep"
      :is-completed="activeStep.isCompleted"
      :go-to-next-step="goToNextStep"
      :go-to-previous-step="goToPreviousStep"
    />
  </template>
</template>
