import type { Meta, StoryObj } from '@storybook/vue3'
import { ref } from 'vue'
import OrganismsStepperHeader from './OrganismsStepperHeader.vue'
import OrganismsStepperDrawer from './OrganismsStepperDrawer.vue'
import OrganismsStepperList from './OrganismsStepperList.vue'

const meta: Meta<typeof OrganismsStepperHeader> = {
  title: 'Organisms/Stepper',
  tags: ['autodoc'],
  argTypes: {
    steps: {
      control: 'object',
    },
    lastCompletedIndex: {
      control: 'number',
    },
    activeStep: {
      control: false,
    },
  },
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=4688-8615&p=f&t=eO3PlxqLz8SporPH-0',
      },
    ],
  },
}

export default meta
type Story = StoryObj<typeof OrganismsStepperHeader>

const steps = [
  { id: 'step-1', title: 'Step 1', index: 0, completed: true },
  { id: 'step-2', title: 'Step 2', index: 1, completed: true },
  { id: 'step-3', title: 'Step 3', index: 2, completed: true },
  { id: 'step-4', title: 'Step 4', index: 3, completed: true },
  { id: 'step-5', title: 'Step 5', index: 4, completed: false },
]

export const Default: Story = {
  render: args => ({
    components: { OrganismsStepperHeader },
    setup() {
      const activeStep = ref(steps[2])
      const handleActiveStep = (_step: string) => {
        activeStep.value = steps.find(step => step.id === _step) ?? steps[0]
      }

      return { args, activeStep, handleActiveStep }
    },
    template: `
      <OrganismsStepperHeader v-bind="args" :active-step="activeStep" @on-step-change="handleActiveStep" />
    `,
  }),
  args: {
    steps,
    lastCompletedIndex: steps.findLastIndex(step => step.completed),
  },
}

export const Mobile: Story = {
  render: args => ({
    components: { OrganismsStepperDrawer },
    setup() {
      const activeStep = ref(steps[2])
      const handleActiveStep = (_step: string) => {
        activeStep.value = steps.find(step => step.id === _step) ?? steps[0]
      }

      return { args, activeStep, handleActiveStep }
    },
    template: `
      <OrganismsStepperDrawer v-bind="args" :active-step="activeStep" @on-step-click="handleActiveStep" />
    `,
  }),
  args: {
    steps,
    lastCompletedIndex: steps.findLastIndex(step => step.completed),
  },
}

export const Desktop: Story = {
  render: args => ({
    components: { OrganismsStepperList },
    setup() {
      const activeStep = ref(steps[2])
      const handleActiveStep = (_step: string) => {
        activeStep.value = steps.find(step => step.id === _step) ?? steps[0]
      }

      return { args, activeStep, handleActiveStep }
    },
    template: `
      <OrganismsStepperList v-bind="args" :active-step="activeStep" @on-step-click="handleActiveStep" />
    `,
  }),
  args: {
    steps,
    lastCompletedIndex: steps.findLastIndex(step => step.completed),
  },
}
