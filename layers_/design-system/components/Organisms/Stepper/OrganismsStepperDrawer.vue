<script setup lang="ts" generic="T extends string">
import { SelectRoot, SelectTrigger, SelectValue, SelectContent, SelectItem, SelectItemText } from 'reka-ui'
import type { OrderedStepItem } from '@design-system/types/types'
import type { OrganismsStepperDrawerProps } from './OrganismsStepperDrawer.props'

const props = defineProps<OrganismsStepperDrawerProps<T>>()
defineEmits<{
  (e: 'onStepClick', step: T): void
}>()

const drawer = tv({
  base: 'px-4 py-2 bg-accent inline-flex group justify-between items-center gap-1 text-invert rounded-sm focus-visible:outline-2 focus-visible:outline-focus focus-visible:outline-offset-2',
  variants: {
    disabled: {
      true: 'bg-disabled text-disabled',
    },
  },
})

const drawerTriggerClass = computed(() => {
  return drawer({ disabled: props.disabled })
})

const isStepNotReachable = (step: OrderedStepItem<T>) => step.id !== props.activeStep.id && step.index > props.lastCompletedIndex
</script>

<template>
  <div class="relative block w-full *:data-[reka-popper-content-wrapper]:w-full">
    <SelectRoot
      :disabled="disabled"
      :model-value="activeStep"
      @update:model-value="(value) => $emit('onStepClick', (value as OrderedStepItem<T>).id)"
    >
      <SelectTrigger class="w-full" :class="drawerTriggerClass" :aria-label="$ts('organisms.stepper.select')">
        <SelectValue as-child>
          <template #default="{ modelValue }">
            <div class="flex justify-between items-center w-full gap-1">
              <OrganismsStepperStep
                id="step"
                class="p-0!"
                as="div"
                :index="(modelValue as OrderedStepItem<T>).index + 1"
                :title="(modelValue as OrderedStepItem<T>).title"
                selected
                inverted
                :disabled="disabled"
              />
              <span v-if="(modelValue as OrderedStepItem<T>)?.index !== undefined" class="caption-1">{{ (modelValue as OrderedStepItem<T>).index + 1 }}/{{ steps.length }}</span>
            </div>
          </template>
        </SelectValue>
        <AtomsIcon name="NavChevronDown" class="text-[2rem] group-data-[state=open]:rotate-180 duration-300" aria-hidden="true" />
      </SelectTrigger>
      <SelectContent
        position="popper"
        side="bottom"
        position-strategy="absolute"
        :side-offset="4"
        :body-lock="false"
        class="bg-light w-full rounded-sm border-neutral border shadow-200 z-20"
      >
        <SelectViewport>
          <SelectItem
            v-for="step in steps"
            :key="step.id"
            :value="step"
            class="px-4 py-2 :not-[:last-child]:mb-1 outline-offset-[-2px] focus-visible:outline-2 group/step cursor-pointer"
            :class="{ 'pointer-events-none': isStepNotReachable(step) }"
            :disabled="isStepNotReachable(step)"
          >
            <SelectItemText class="flex w-auto">
              <OrganismsStepperStep
                id="step"
                class="p-0! outline-0!"
                as="div"
                :index="step.index + 1"
                :title="step.title"
                :selected="step.id === activeStep.id"
                :disabled="isStepNotReachable(step)"
              />
            </SelectItemText>
          </SelectItem>
        </SelectViewport>
      </SelectContent>
    </SelectRoot>
  </div>
</template>
