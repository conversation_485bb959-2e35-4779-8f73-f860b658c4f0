import type { Meta, StoryObj } from '@storybook/vue3'
import OrganismsStepperStep from './OrganismsStepperStep.vue'

const meta: Meta<typeof OrganismsStepperStep> = {
  title: 'Organisms/Stepper/Step',
  component: OrganismsStepperStep,
  tags: ['autodoc'],
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=4719-1462&t=l2i56nkwxGxEWcmG-4',
      },
    ],
  },
}

export default meta
type Story = StoryObj<typeof OrganismsStepperStep>

export const Default: Story = {
  render: args => ({
    components: { OrganismsStepperStep },
    setup() {
      return { args }
    },
    template: `
      <OrganismsStepperStep v-bind="args" />
    `,
  }),
  args: {
    title: 'Step',
    id: 'step',
    index: 1,
    selected: false,
    disabled: false,
    locked: false,
  },
}
