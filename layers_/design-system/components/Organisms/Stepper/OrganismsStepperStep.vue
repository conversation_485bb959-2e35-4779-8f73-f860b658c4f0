<script setup lang="ts">
import type { OrganismsStepProps } from './OrganismsStepperStep.props'

const props = defineProps<OrganismsStepProps>()
const emit = defineEmits<{
  (e: 'onClickHandler', id: string): void
}>()

const step = tv({
  slots: {
    container: 'group/step flex flex-wrap items-center justify-center gap-2 body-1 text-accent p-2 hover:text-hover cursor-pointer focus-visible:outline-2 outline-offset-[-2px] outline-accent',
    index: 'flex-shrink-0 flex justify-center items-center w-5 h-5 rounded-full caption-1 bg-neutral border border-accent group-hover/step:bg-light group-hover/step:border-hover group-hover/step:color-hover',
  },
  variants: {
    selected: {
      true: {
        container: 'text-accent font-bold',
        index: 'bg-accent text-invert border-accent group-hover/step:bg-hover group-hover/step:border-hover',
      },
    },
    inverted: {
      true: {
        container: 'text-invert',
        index: 'bg-neutral text-accent border-invert group-hover/step:bg-neutral group-hover/step:border-invert',
      },
    },
    disabled: {
      true: {
        container: 'text-disabled pointer-events-none',
        index: 'bg-hidden border-disabled text-disabled',
      },
    },
    locked: {
      true: {
        container: 'text-disabled font-bold pointer-events-none',
        index: 'bg-disabled border-disabled text-placeholder',
      },
    },
  },
})

const stepClass = computed(() => {
  return step({
    selected: props.selected && !props.disabled,
    disabled: props.disabled,
    locked: props.locked,
    inverted: props.inverted,
  })
})

const clickHandler = () => {
  if (!props.disabled) {
    emit('onClickHandler', props.id)
  }
}

const as = computed(() => {
  if (props.disabled) {
    return 'div'
  }

  return props.as ?? 'button'
})

const hasIndex = computed(() => props.index !== undefined && typeof props.index === 'number')
</script>

<template>
  <component
    :is="as"
    :class="stepClass.container()"
    :aria-disabled="disabled"
    @click="clickHandler()"
  >
    <span v-if="hasIndex" :class="stepClass.index()">{{ index }}</span>
    <span v-if="title" class="">{{ title }}</span>
  </component>
</template>
