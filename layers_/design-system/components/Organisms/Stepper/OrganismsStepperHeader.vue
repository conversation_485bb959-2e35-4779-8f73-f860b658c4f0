<script setup lang="ts" generic="T extends string">
import type { OrganismsStepperHeaderProps } from './OrganismsStepperHeader.props'

const props = defineProps<OrganismsStepperHeaderProps<T>>()
const emit = defineEmits<{
  (e: 'onStepChange', step: T): void
}>()

const args = computed(() => {
  return {
    steps: props.steps,
    activeStep: props.activeStep,
    lastCompletedIndex: props.lastCompletedIndex,
  }
})
</script>

<template>
  <div class="hidden lg:block">
    <OrganismsStepperList
      v-bind="args"
      @on-step-click="(step) => emit('onStepChange', step)"
    />
  </div>
  <div class="lg:hidden">
    <OrganismsStepperDrawer
      v-bind="args"
      @on-step-click="(step) => emit('onStepChange', step)"
    />
  </div>
</template>
