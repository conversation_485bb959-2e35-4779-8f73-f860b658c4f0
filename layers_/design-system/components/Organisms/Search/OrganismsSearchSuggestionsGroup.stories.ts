import type { StoryObj } from '@storybook/vue3'
import OrganismsSearchSuggestionsGroup from './OrganismsSearchSuggestionsGroup.vue'
import OrganismsSearchSuggestionItem from './OrganismsSearchSuggestionItem.vue'

const meta = {
  title: 'Organisms/Search/SuggestionsGroup',
  component: OrganismsSearchSuggestionsGroup,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component: `
### Component Architecture

Represents a category section within the suggestions dropdown.

- **title**: header text for the group
- Contains a list of **OrganismsSearchSuggestionItem** children

### Props

| Prop  | Description        | Required | Default |
| ----- | ------------------ | -------- | ------- |
| title | Group heading text | yes      | —       |
| size  | 'small' or 'large' | no       | small   |

### Usage

\`\`\`vue
<OrganismsSearchSuggestionsGroup title="Category Name">
  <OrganismsSearchSuggestionItem to="/item1">
    <template #title>Item Name</template>
    <template #description>Item description</template>
  </OrganismsSearchSuggestionItem>
</OrganismsSearchSuggestionsGroup>
\`\`\`
        `,
      },
    },
  },
  argTypes: {
    title: { control: 'text', description: 'Group heading text' },
    size: { control: 'inline-radio', options: ['small', 'large'], description: 'Size variant' },
  },
  args: {
    title: 'Category',
    size: 'small',
  },
} as const

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => ({
    components: { OrganismsSearchSuggestionsGroup, OrganismsSearchSuggestionItem },
    setup() { return { args } },
    template: `
      <div style="width: 300px;">
        <OrganismsSearchSuggestionsGroup v-bind="args">
          <OrganismsSearchSuggestionItem to="/item1">
            <template #title>Item One</template>
            <template #description>Description one</template>
          </OrganismsSearchSuggestionItem>
          <OrganismsSearchSuggestionItem to="/item2">
            <template #title>Item Two</template>
            <template #description>Description two</template>
          </OrganismsSearchSuggestionItem>
        </OrganismsSearchSuggestionsGroup>
      </div>
    `,
  }),
}

export const Large: Story = {
  args: { size: 'large' },
  render: args => ({
    components: { OrganismsSearchSuggestionsGroup, OrganismsSearchSuggestionItem },
    setup() { return { args } },
    template: `
      <div style="width: 300px;">
        <OrganismsSearchSuggestionsGroup v-bind="args">
          <OrganismsSearchSuggestionItem to="/item1" size="large">
            <template #title>Item One</template>
            <template #description>Description one</template>
          </OrganismsSearchSuggestionItem>
          <OrganismsSearchSuggestionItem to="/item2" size="large">
            <template #title>Item Two</template>
            <template #description>Description two</template>
          </OrganismsSearchSuggestionItem>
        </OrganismsSearchSuggestionsGroup>
      </div>
    `,
  }),
}
