<script lang="ts" setup>
import type { SearchSuggestionsGroupProps } from './OrganismsSearchSuggestionsGroup.props'

withDefaults(defineProps<SearchSuggestionsGroupProps>(), {
  size: 'small',
})
</script>

<template>
  <div>
    <div
      class="bg-light border-b border-neutral"
      :class="{
        'px-padding-md py-padding-sm': size === 'small',
        '*:container py-padding-sm': size === 'large',
      }"
    >
      <p
        class="uppercase text-neutral"
        :class="{
          'section-1': size === 'small',
          'subtitle-1 font-semibold': size === 'large',
        }"
      >
        {{ title }}
      </p>
    </div>
    <ul class="bg-light">
      <slot />
    </ul>
  </div>
</template>
