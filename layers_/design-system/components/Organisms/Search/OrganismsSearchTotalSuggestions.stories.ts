import type { StoryObj } from '@storybook/vue3'
import OrganismsSearchTotalSuggestions from './OrganismsSearchTotalSuggestions.vue'

const meta = {
  title: 'Organisms/Search/TotalSuggestions',
  component: OrganismsSearchTotalSuggestions,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component: `
### Component Architecture

Displays current & total number of results in the suggestions dropdown.

### Props

| Prop    | Description                           | Required |
| ------- | ------------------------------------- | -------- |
| current | Number of currently displayed results | yes      |
| total   | Total number of search results        | yes      |

### Usage

\`\`\`vue
<OrganismsSearchTotalSuggestions :current="10" :total="50" />
\`\`\`
        `,
      },
    },
  },
  argTypes: {
    current: { control: 'number', description: 'Number of displayed results' },
    total: { control: 'number', description: 'Number of total results' },
  },
  args: {
    current: 5,
    total: 100,
  },
} as const

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {}

export const CustomNumbers: Story = {
  args: { current: 1, total: 20 },
}
