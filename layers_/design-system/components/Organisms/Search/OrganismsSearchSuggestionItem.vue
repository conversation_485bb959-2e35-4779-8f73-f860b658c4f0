<script lang="ts" setup>
import type { OrganismsSearchSuggestionItemProps } from './OrganismsSearchSuggestionItem.props'

withDefaults(defineProps<OrganismsSearchSuggestionItemProps>(), {
  size: 'small',
})
</script>

<template>
  <li
    class="relative group"
    :class="{
      'not-last:mb-1': size === 'large',
    }"
    role="option"
    aria-selected="false"
  >
    <NuxtLink
      :to="to"
      class="absolute inset-0 block peer"
      :aria-label="ariaLabel"
    />
    <span
      class="py-padding-md peer-hover:bg-hover peer-hover:text-invert transition text-accent peer-focus:bg-hover flex flex-col gap-2 peer-focus:text-invert bg-neutral"
      :class="{
        '*:container': size === 'large',
        'px-padding-md': size === 'small',
      }"
    >
      <span class="flex justify-between items-center gap-xs *:pointer-events-none">
        <slot name="prefix" />
        <span class="flex flex-col gap-2 flex-1 overflow-hidden w-60">
          <slot />
          <span class="block text-body-sm font-bold">
            <slot name="title" />
          </span>
          <span v-if="$slots.description" class="underline underline-offset-2 text-link-sm overflow-hidden text-ellipsis text-nowrap **:text-ellipsis **:text-nowrap **:overflow-hidden">
            <slot name="description" />
          </span>
        </span>
        <slot name="suffix" />
      </span>
    </span>
  </li>
</template>
