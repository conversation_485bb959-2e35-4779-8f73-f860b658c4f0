<script setup lang="ts">
import type { SuggestionsType } from '@design-system/mock/searchSuggestions'
import type { SearchProps } from './OrganismsSearch.props'

const props = withDefaults(defineProps<SearchProps>(), {
  variant: 'default',
})

const query = ref('')
const isSearchbarVisible = ref(false)
const { isOpen: isModalOpen } = useSearchSuggestionsDropdown(query)
const { localePath } = useI18n()

const { data: searchSuggestions } = useSearch(query)

const hasSuggestions = (suggestions: SuggestionsType): boolean => {
  return suggestions.categories.some(category => category.suggestions.length > 0)
}

const handleSearchbarToggle = () => {
  isSearchbarVisible.value = !isSearchbarVisible.value
}

const route = useRoute()
watch(() => route.fullPath, () => {
  if (!import.meta.client || !isModalOpen) return
  // @ts-expect-error ?. is runtime safe
  document.activeElement?.blur()
})

const handleSearchbarSubmit = () => {
  navigateTo(`${localePath('/search')}?q=${query.value}`)
}

const searchStyles = tv({
  slots: {
    mobileOverlay: 'absolute h-[calc(100vh-var(--header-height))] bottom-0 inset-0 bg-neutral lg:hidden top-(--header-height) transition-all after:absolute after:-top-full after:left-0 after:w-full after:h-full after:shadow-300 after:pointer-events-none',

    root: 'w-full lg:!block group/search',

    suggestions: 'lg:group-not-focus-within/search:opacity-0 lg:group-not-focus-within/search:scale-97 lg:group-not-focus-within/search:blur-xs lg:group-not-focus-within/search:pointer-events-none',
  },
  variants: {
    variant: {
      homepage: {
        root: 'transition-all',
        suggestions: 'max-h-[calc(100%-var(--search-bar-height)-0.5rem)]',
      },
      contextual: {
        root: '',
      },
      default: {
        root: 'max-lg:top-(--header-height) absolute max-lg:mt-4 max-lg:md:mt-14 max-lg:left-0 max-lg:container transition-all lg:relative',
      },
    },
    suggestionsState: {
      open: {},
      closed: {},
    },
  },
  compoundVariants: [
    {
      variant: 'homepage',
      suggestionsState: 'open',
      class: {
        root: 'mt-0 not-focus-within:mt-30',
      },
    },
    {
      variant: 'homepage',
      suggestionsState: 'closed',
      class: {
        root: 'mt-30',
      },
    },
  ],
})

const searchClasses = computed(() => {
  return searchStyles({
    variant: props.variant,
    suggestionsState: isModalOpen.value ? 'open' : 'closed',
  })
})
</script>

<template>
  <Transition
    enter-from-class="opacity-0"
    leave-to-class="opacity-0"
  >
    <div
      v-if="isSearchbarVisible"
      :class="searchClasses.mobileOverlay()"
      @click="handleSearchbarToggle"
    />
  </Transition>
  <Transition
    enter-from-class="scale-97 opacity-0 blur-xs"
    leave-to-class="scale-97 opacity-0 blur-xs"
  >
    <div
      v-show="isSearchbarVisible || props.variant === 'homepage'"
      :class="searchClasses.root()"
    >
      <OrganismsSearchBar
        v-model="query"
        :variant="props.variant"
        :placeholder="props.placeholder"
        @submit="handleSearchbarSubmit"
      />
      <Transition
        enter-from-class="scale-97 opacity-0 blur-xs"
        leave-to-class="scale-97 opacity-0 blur-xs"
      >
        <!-- with tabindex, the dropdown is focusable and can be closed by clicking outside -->
        <OrganismsSearchSuggestions
          v-if="isModalOpen && searchSuggestions"
          :class="searchClasses.suggestions()"
          tabindex="0"
        >
          <template v-if="searchSuggestions && hasSuggestions(searchSuggestions)">
            <OrganismsSearchTotalSuggestions
              :total="searchSuggestions.results_number.total"
              :current="searchSuggestions.results_number.current"
            />
            <template v-for="category in searchSuggestions.categories">
              <OrganismsSearchSuggestionsGroup
                v-if="category.suggestions?.length > 0"
                :key="category.id"
                :title="category.name"
              >
                <OrganismsSearchSuggestionItem
                  v-for="suggestion in category.suggestions"
                  :key="suggestion.id"
                  :to="suggestion.to"
                  :aria-label="$ts('search.goTo', { label: suggestion.name })"
                >
                  <template #prefix>
                    <AtomsIcon
                      v-if="suggestion.prefixIcon"
                      :name="suggestion.prefixIcon"
                      class="text-6xl"
                      aria-hidden="true"
                    />
                  </template>
                  <AtomsTag v-if="suggestion.tag" :label="suggestion.tag" />
                  <template #title>
                    {{ suggestion.name }}
                  </template>

                  <template v-if="suggestion.description" #description>
                    <UtilsMarkdown :content="suggestion.description.replace(query, `**${query}**`)" />
                  </template>
                  <template #suffix>
                    <AtomsIcon
                      name="NavArrowRight"
                      class="text-3xl group-hover:opacity-100 opacity-0"
                      aria-hidden="true"
                    />
                  </template>
                </OrganismsSearchSuggestionItem>
              </OrganismsSearchSuggestionsGroup>
            </template>
          </template>
          <template v-else>
            <OrganismsSearchSuggestionsNoResults />
          </template>
          <div class="px-padding-md py-padding-lg max-w-80 mx-auto hidden lg:block">
            <AtomsButton
              as="link"
              :to="`${localePath('/search')}?q=${query}`"
              class="w-full"
              :aria-label="$ts('search.seeAll')"
              :state="hasSuggestions(searchSuggestions) ? 'default' : 'disabled'"
            >
              {{ $ts('search.seeAll') }}
            </AtomsButton>
          </div>
        </OrganismsSearchSuggestions>
      </Transition>
    </div>
  </Transition>
  <div v-if="isModalOpen && searchSuggestions" class="lg:hidden px-padding-md py-padding-lg fixed bottom-0 left-0 bg-white w-full">
    <AtomsButton
      as="link"
      :to="`${localePath('/search')}?q=${query}`"
      class="w-full"
      :aria-label="$ts('search.seeAll')"
      :state="hasSuggestions(searchSuggestions) ? 'default' : 'disabled'"
    >
      {{ $ts('search.seeAll') }}
    </AtomsButton>
  </div>
  <AtomsIconButton
    class="lg:hidden"
    :class="props.variant === 'homepage' ? 'hidden' : ''"
    :state="isSearchbarVisible ? 'selected' : 'default'"
    :aria-label="isSearchbarVisible ? $ts('search.closeSearch') : $ts('search.openSearch')"
    @click="handleSearchbarToggle"
  >
    <template #icon>
      <AtomsIcon :name="isSearchbarVisible ? 'NavCloseIcon' : 'NavSearch'" aria-hidden="true" />
    </template>
  </AtomsIconButton>
</template>
