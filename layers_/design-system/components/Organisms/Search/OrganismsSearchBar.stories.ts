import type { StoryObj } from '@storybook/vue3'
import OrganismsSearchBar from './OrganismsSearchBar.vue'

const meta = {
  title: 'Organisms/Search/SearchBar',
  component: OrganismsSearchBar,
  argTypes: {
    placeholder: { control: 'text', description: 'Input placeholder text' },
  },
  args: {
    placeholder: 'Search here...',
  },
} as const

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {}

export const CustomPlaceholder: Story = {
  args: { placeholder: 'Find items...' },
}

export const Contextual: Story = {
  args: {
    placeholder: 'Search for products...',
    variant: 'contextual',
  },
}

export const Homepage: Story = {
  args: {
    variant: 'homepage',
    placeholder: 'How can we help?',
  },
  decorators: [
    () => ({
      template: '<div class="bg-accent min-h-screen flex items-center justify-center p-padding-xxl"><story /></div>',
    }),
  ],
}
