import type { StoryObj } from '@storybook/vue3'
import OrganismsSearchSuggestions from './OrganismsSearchSuggestions.vue'
import OrganismsSearchSuggestionsGroup from './OrganismsSearchSuggestionsGroup.vue'
import OrganismsSearchSuggestionItem from './OrganismsSearchSuggestionItem.vue'

const meta = {
  title: 'Organisms/Search/SuggestionsWrapper',
  component: OrganismsSearchSuggestions,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component: `
### Component Architecture

Wraps suggestion group(s) in a styled dropdown container.

### Usage

\`\`\`vue
<OrganismsSearchSuggestions>
  <OrganismsSearchSuggestionsGroup title="Examples">
    <OrganismsSearchSuggestionItem to="/foo">
      <template #title>Example 1</template>
      <template #description>Desc 1</template>
    </OrganismsSearchSuggestionItem>
  </OrganismsSearchSuggestionsGroup>
</OrganismsSearchSuggestions>
\`\`\`
        `,
      },
    },
  },
} as const

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: () => ({
    components: {
      OrganismsSearchSuggestions,
      OrganismsSearchSuggestionsGroup,
      OrganismsSearchSuggestionItem,
    },
    template: `
      <div style="position: relative; width: 300px;">
        <OrganismsSearchSuggestions>
          <OrganismsSearchSuggestionsGroup title="Category">
            <OrganismsSearchSuggestionItem to="/item1">
              <template #title>Item 1</template>
              <template #description>Description for item 1</template>
            </OrganismsSearchSuggestionItem>
            <OrganismsSearchSuggestionItem to="/item2">
              <template #title>Item 2</template>
              <template #description>Description for item 2</template>
            </OrganismsSearchSuggestionItem>
          </OrganismsSearchSuggestionsGroup>
        </OrganismsSearchSuggestions>
      </div>
    `,
  }),
}
