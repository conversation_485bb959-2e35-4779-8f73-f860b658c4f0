<script lang="ts" setup>
import type { SearchBarProps } from './OrganismsSearchBar.props'

const ERASABLE_LIMIT = 2
const query = defineModel<string>()

const emit = defineEmits<{
  (e: 'submit'): void
}>()

const { $ts } = useI18n()
const props = withDefaults(defineProps<SearchBarProps>(), {
  variant: 'default',
})

const root = useTemplateRef('root')
const cssVarHeight = useCssVar('--search-bar-height')
const inputRef = useTemplateRef('input')
const isErasable = computed(() => query.value && query.value.length >= ERASABLE_LIMIT)
const getButtonIcon = () => {
  return isErasable.value ? 'NavCloseSquareContainedLine' : 'NavSearch'
}
const placeholder = computed(
  () => props.placeholder || $ts('search.placeholder'),
)

const { height } = useElementSize(root)
watch(height, () => {
  cssVarHeight.value = `${height.value}px`
}, { immediate: true })

const classes = tv({
  slots: {
    root: 'flex items-center border-neutral w-full pr-1 overflow-hidden transition-all duration-300 bg-neutral',
    input: 'w-full focus:outline-none caret-(--color-stroke-accent)',
  },
  variants: {
    variant: {
      default: {
        root: 'rounded-full border focus-within:border-focus focus-within:shadow-100',
        input: 'px-padding-md h-10.5',

      },
      contextual: {
        root: 'rounded-sm border focus-within:border-focus focus-within:shadow-100',
        input: 'px-padding-xs h-8',
      },
      homepage: {
        root: 'bg-transparent border-b-2 border-neutral focus-within:outline-none relative',
        input: 'text-invert headline-1 placeholder:text-invert caret-invert resize-none',
      },
    },
  },
  defaultVariants: {
    variant: 'default',
  },
})

const handleButtonClick = () => {
  if (isErasable.value) {
    query.value = ''
    return
  }
  inputRef.value?.focus()
}
const handleSubmit = () => {
  emit('submit')
}

const searchBarClasses = computed(() => classes({ variant: props.variant }))
</script>

<template>
  <div
    ref="root"
    :class="searchBarClasses.root()"
  >
    <template v-if="variant === 'homepage'">
      <div
        :class="searchBarClasses.input()"
        class="break-all opacity-0 max-h-[calc(var(--text-heading-xl)_*_3)] pointer-events-none"
        aria-hidden="true"
      >
        {{ query || placeholder }}
      </div>
      <div
        class="absolute inset-0 pr-9"
      >
        <textarea
          ref="input"
          v-model="query"
          autofocus
          :aria-label="$ts('search.placeholder')"
          :class="searchBarClasses.input()"
          :placeholder="placeholder"
          @keyup.enter="handleSubmit"
        />
      </div>
    </template>
    <template v-else>
      <input
        ref="input"
        v-model="query"
        :aria-label="$ts('search.placeholder')"
        :class="searchBarClasses.input()"
        :placeholder="placeholder"
        @keyup.enter="handleSubmit"
      >
    </template>
    <AtomsIconButton class="focus-visible:ring-offset-0 shrink-0" :anatomy="variant === 'contextual' ? 'tertiary' : 'secondary'" @click="handleButtonClick()">
      <template #icon>
        <AtomsIcon :name="getButtonIcon()" class="transition-all" />
      </template>
    </AtomsIconButton>
  </div>
</template>
