import type { StoryObj } from '@storybook/vue3'
import OrganismsSearchSuggestionsNoResults from './OrganismsSearchSuggestionsNoResults.vue'

const meta = {
  title: 'Organisms/Search/SuggestionsNoResults',
  component: OrganismsSearchSuggestionsNoResults,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component: `
### Component Architecture

Shows a placeholder when there are no search suggestions.

### Usage

\`\`\`vue
<OrganismsSearchSuggestionsNoResults />
\`\`\`
        `,
      },
    },
  },
} as const

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {}
