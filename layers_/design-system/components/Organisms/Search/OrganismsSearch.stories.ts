import type { StoryObj } from '@storybook/vue3'
import OrganismsSearch from './OrganismsSearch.vue'

const meta = {
  title: 'Organisms/Search',
  component: OrganismsSearch,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component: `
### Component Architecture

The OrganismsSearch component composes:
- **OrganismsSearchBar**: the input and action button
- **OrganismsSearchSuggestions**: the dropdown wrapper
- **OrganismsSearchMobile**: mobile toggle button

It handles toggling visibility and integrates suggestion groups and items.

### Usage Guidelines

- Use **OrganismsSearch** as a standalone search interface in headers or pages.
- It manages the query model and dropdown state internally.
- Slots and props of subcomponents can be customized by importing them directly if needed.
        `,
      },
    },
  },
} as const

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => ({
    components: { OrganismsSearch },
    setup() {
      return { args }
    },
    template: `<div class="max-w-md mx-auto"><OrganismsSearch v-bind="args" /></div>`,
  }),
}
