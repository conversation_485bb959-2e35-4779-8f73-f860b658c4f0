<script lang="ts" setup>
import type { OrganismsSearchTotalSuggestionsProps } from './OrganismsSearchTotalSuggestions.props'

defineProps<OrganismsSearchTotalSuggestionsProps>()
</script>

<template>
  <div class="flex items-center justify-between px-padding-md py-padding-xs text-placeholder text-caption-sm">
    {{ $tn(current) }}
    {{ $ts('search.resultsNumber') }}
    {{ $tn(total) }}
  </div>
</template>
