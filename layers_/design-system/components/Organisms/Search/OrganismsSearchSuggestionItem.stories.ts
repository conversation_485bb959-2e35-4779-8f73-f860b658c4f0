import type { StoryObj } from '@storybook/vue3'
import AtomsIcon from '../../Atoms/AtomsIcon.vue'
import AtomsTag from '../../Atoms/AtomsTag.vue'
import OrganismsSearchSuggestionItem from './OrganismsSearchSuggestionItem.vue'

const meta = {
  title: 'Organisms/Search/SuggestionItem',
  component: OrganismsSearchSuggestionItem,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component: `
### Component Architecture

Represents a single suggestion row within a SuggestionsGroup.

It uses:
- A NuxtLink covering the entire row
- Slots for prefix (icon), title, description, and suffix (icon/button)

It also exposes a default slot for arbitrary content (e.g., an <AtomsTag> badge).

### Props

| Prop  | Description                        | Required | Default |
| ----- | ---------------------------------- | -------- | ------- |
| to    | Route path for the suggestion link | yes      | —       |
| size  | Small or large display variant     | no       | small   |
| icon  | (unused) placeholder for future use| no       | —       |

### Usage

\`\`\`vue
<OrganismsSearchSuggestionItem to="/item1">
  <template #prefix><AtomsIcon name="NavSearch" /></template>
  <template #title>Suggestion Title</template>
  <template #description>Description text</template>
  <template #suffix><AtomsIcon name="NavArrowRight" /></template>
</OrganismsSearchSuggestionItem>
\`\`\`
        `,
      },
    },
  },
  argTypes: {
    to: { control: 'text', description: 'Link target for suggestion item' },
    size: { control: 'inline-radio', options: ['small', 'large'], description: 'Size variant' },
  },
  args: {
    to: '/example',
    size: 'small',
  },
} as const

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => ({
    components: { OrganismsSearchSuggestionItem, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
<li style="list-style: none; width: 300px;">
  <OrganismsSearchSuggestionItem v-bind="args">
    <template #prefix>
      <AtomsIcon name="ExtPDFIcon" class="text-6xl" />
    </template>
    <template #title>Example Suggestion</template>
    <template #description>Example description text for the suggestion.</template>
    <template #suffix><AtomsIcon name="NavArrowRight" class="text-3xl" /></template>
  </OrganismsSearchSuggestionItem>
</li>
    `,
  }),
}

export const WithTag: Story = {
  render: args => ({
    components: { OrganismsSearchSuggestionItem, AtomsIcon, AtomsTag },
    setup() {
      return { args }
    },
    template: `
<li style="list-style: none; width: 300px;">
  <OrganismsSearchSuggestionItem v-bind="args">
    <template #default>
      <AtomsTag label="CATEGORY" />
    </template>
    <template #title>Example Suggestion</template>
    <template #description>Example description text for the suggestion.</template>
  </OrganismsSearchSuggestionItem>
</li>
    `,
  }),
}
