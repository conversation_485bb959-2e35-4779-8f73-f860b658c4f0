import type { StoryObj } from '@storybook/vue3'
import OrganismsProjectOfTheMonth from './OrganismsProjectOfTheMonth.vue'

// Define metadata for the component
const meta = {
  title: 'Organisms/ProjectOfTheMonth',
  component: OrganismsProjectOfTheMonth,
  args: {
    project: 'Texas A&M Mitchell Physics Bldg College Station, TX USA',
    scope: '2 Stepped Classic 51 Walls',
    architect: '<PERSON>',
    specifyingDealer: 'Styles, New York',
    dealer: 'Construction Architectural Products (TRW)',
    projectImage: {
      src: 'https://picsum.photos/400/300',
      alt: 'Description for the image',
    },
    longDescription: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullamco laboriosam, nisi ut aliquid ex ea commodi consequatur. Duis aute irure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
    descriptionImage: {
      src: 'https://picsum.photos/400/300',
      alt: 'Description for the image',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => ({
    components: { OrganismsProjectOfTheMonth },
    setup() {
      return { args }
    },
    template: `
    <OrganismsProjectOfTheMonth v-bind="args"/>
    `,
  }),
}
