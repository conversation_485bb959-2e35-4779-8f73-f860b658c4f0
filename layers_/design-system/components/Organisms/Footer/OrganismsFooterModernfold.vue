<script setup lang='ts'>
import type { OrganismsFooterModernfoldProps } from './OrganismsFooterModernfold.props'

const props = withDefaults(defineProps<OrganismsFooterModernfoldProps>(), {
  isMiniFooter: false,
})

const isFullFooterShown = computed(() => !props.isMiniFooter && props.linkGrid.length)
</script>

<template>
  <div class="bg-accent">
    <div class="container py-padding-xxl space-y-padding-xxl">
      <div v-if="isFullFooterShown" class="lg:hidden grid-footer-mf *:col-span-full lg:*:col-span-3 gap-y-lg">
        <div v-for="(column, index) in linkGrid" :key="index">
          <OrganismsFooterAccordion :links="column.links" />
        </div>
      </div>
      <div v-if="isFullFooterShown" class="hidden lg:grid grid-footer-mf *:col-span-3">
        <div v-for="(column, index) in linkGrid" :key="index">
          <OrganismsFooterColumn :links="column.links" brand="modernfold" />
        </div>
      </div>

      <OrganismsFooterMini brand="modernfold" />
    </div>
  </div>
</template>
