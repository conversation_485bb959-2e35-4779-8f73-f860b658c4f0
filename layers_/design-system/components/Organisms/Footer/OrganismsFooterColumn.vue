<script setup lang='ts'>
import type { OrganismsFooterColumnProps } from './OrganismsFooterColumn.props'

withDefaults(defineProps<OrganismsFooterColumnProps>(), {
  brand: 'modernfold',
  isSubsection: false,
})
</script>

<template>
  <div
    v-for="(section, index) in links"
    :key="index"
  >
    <p
      v-if="typeof section.title === 'string'"
      class="flex gap-x-xs cursor-default items-center outline-none w-full text-invert section-1 uppercase"
    >
      {{ section.title }}
    </p>
    <AtomsLinkFooter
      v-else
      v-bind="section.title"
      :theme="brand"
      class="py-1"
      :disabled="section.disabled"
    >
      {{ section.title.label }}
    </AtomsLinkFooter>

    <p v-if="section.description" :class="['hidden lg:block text-invert text-body-2', section.disabled ? 'text-disabled!' : '']">
      {{ section.description }}
    </p>

    <div v-if="section.links?.length" :class="['space-y-padding-sm lg:space-y-padding-md mt-padding-sm lg:mt-padding-md', isSubsection ? 'pl-padding-xl' : '']">
      <OrganismsFooterColumn
        :brand="brand"
        :links="section.links"
        is-subsection
      />
    </div>

    <div v-if="section.linkedTags" class="flex flex-wrap gap-xs mt-padding-sm">
      <AtomsFooterButton v-for="(tag, tagIndex) in section.linkedTags" :key="tagIndex" v-bind="tag" />
    </div>
  </div>
</template>
