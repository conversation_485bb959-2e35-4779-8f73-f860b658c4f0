<script setup lang='ts'>
import useBrandLogo from '@design-system/composables/useHeaderLogo'
import type { OrganismsFooterMiniProps } from './OrganismsFooterMini.props'

const props = withDefaults(defineProps<OrganismsFooterMiniProps>(),
  {
    brand: 'modernfold',
  },
)

const { logoName } = useBrandLogo({ brand: props.brand, variant: 'white' })
</script>

<template>
  <div class="flex flex-col lg:flex-row gap-x-xxl gap-y-sm col-span-full text-invert">
    <AtomsIcon
      :name="logoName"
      class="h-8 w-fit"
      mode="svg"
    />
    <p>
      {{ $ts('footer.mini.copyrightStatement', { year: new Date().getFullYear() }) }}
    </p>
  </div>
</template>
