<script setup lang='ts'>
import type { FooterProps } from './OrganismsFooter.props'
import type { OrganismsFooterColumnProps } from './OrganismsFooterColumn.props'

const site = useRuntimeConfig().public.site
const { $getLocale } = useI18n()

const { data: footer, error } = await useFetch<OrganismsFooterColumnProps[]>(() => `/api/${$getLocale()}/storyblok/menus/footer`, {
  key: `menu-footer`,
  default: () => [],
})

if (error.value) {
  console.error(error.value)
}

defineProps<FooterProps>()
</script>

<template>
  <div>
    <OrganismsFooterSkyfold
      v-if="site === 'skyfold'"
      :link-grid="footer ?? []"
      :is-mini-footer="compressed"
    />
    <OrganismsFooterModernfold
      v-else
      :link-grid="footer ?? []"
      :is-mini-footer="compressed"
    />
  </div>
</template>
