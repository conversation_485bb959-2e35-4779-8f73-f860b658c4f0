<script setup lang='ts'>
import type { OrganismsFooterSkyfoldProps } from './OrganismsFooterSkyfold.props'

const props = withDefaults(defineProps<OrganismsFooterSkyfoldProps>(), {
  isMiniFooter: false,
})

const isFullFooterShown = computed(() => !props.isMiniFooter && props.linkGrid.length)
</script>

<template>
  <div class="bg-accent">
    <div class="container py-padding-xxl space-y-padding-xxl">
      <div v-if="isFullFooterShown" class="flex flex-col lg:flex-row gap-x-xxl gap-y-lg">
        <div v-for="(column, index) in linkGrid" :key="index" class="space-y-padding-lg">
          <OrganismsFooterColumn :links="column.links" brand="skyfold" />
        </div>
      </div>
      <div>
        <OrganismsFooterMini brand="skyfold" />
      </div>
    </div>
  </div>
</template>
