import type { Meta, StoryObj } from '@storybook/vue3'
import { footerMockModernfold } from '@design-system/mock/footer'
import OrganismsFooterModernfold from './OrganismsFooterModernfold.vue'

// Define metadata for the component
const meta: Meta<typeof OrganismsFooterModernfold> = {
  title: 'Organisms/Footer Modernfold',
  component: OrganismsFooterModernfold,
  tags: ['autodocs'],
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=4577-571',
      },
    ],
  },
  argTypes: {
    isMiniFooter: {
      control: 'boolean',
    },
    linkGrid: {
      control: 'object',
    },
  },
  args: {
    isMiniFooter: false,
    linkGrid: footerMockModernfold,
  },
  decorators: [
    () => ({
      template: '<div class="theme-modernfold"><story/></div>',
    }),
  ],
}

export default meta
type Story = StoryObj<typeof OrganismsFooterModernfold>

export const Default: Story = {
  render: args => ({
    setup() {
      return { args }
    },
    components: { OrganismsFooterModernfold },
    template: `
      <StoryWrapper>
        <OrganismsFooterModernfold v-bind=args />
      </StoryWrapper>
    `,
  }),
  args: {
    isMiniFooter: false,
  },
}

export const Mini: Story = {
  render: args => ({
    setup() {
      return { args }
    },
    components: { OrganismsFooterModernfold },
    template: `
      <StoryWrapper>
        <OrganismsFooterModernfold  v-bind=args />
      </StoryWrapper>
    `,
  }),
  args: {
    isMiniFooter: true,
  },
}
