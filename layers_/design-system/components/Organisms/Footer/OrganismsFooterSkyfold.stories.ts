import type { Meta, StoryObj } from '@storybook/vue3'
import { footerMockSkyfold } from '@design-system/mock/footer'
import OrganismsFooterSkyfold from './OrganismsFooterSkyfold.vue'

// Define metadata for the component
const meta: Meta<typeof OrganismsFooterSkyfold> = {
  title: 'Organisms/Footer Skyfold',
  component: OrganismsFooterSkyfold,
  tags: ['autodocs'],
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=4577-571',
      },
    ],
  },
  argTypes: {
    isMiniFooter: {
      control: 'boolean',
    },
    linkGrid: {
      control: 'object',
    },
  },
  args: {
    isMiniFooter: false,
    linkGrid: footerMockSkyfold,
  },
  decorators: [
    () => ({
      template: '<div class="theme-skyfold"><story/></div>',
    }),
  ],
}

export default meta
type Story = StoryObj<typeof OrganismsFooterSkyfold>

export const Default: Story = {
  render: args => ({
    setup() {
      return { args }
    },
    components: { OrganismsFooterSkyfold },
    template: `
      <StoryWrapper>
        <OrganismsFooterSkyfold v-bind=args />
      </StoryWrapper>
    `,
  }),
  args: {
    isMiniFooter: false,
  },
}

export const Mini: Story = {
  render: args => ({
    setup() {
      return { args }
    },
    components: { OrganismsFooterSkyfold },
    template: `
      <StoryWrapper>
        <OrganismsFooterSkyfold  v-bind=args />
      </StoryWrapper>
    `,
  }),
  args: {
    isMiniFooter: true,
  },
}
