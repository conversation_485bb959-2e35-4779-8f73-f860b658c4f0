<script setup lang='ts'>
import type { OrganismsFooterAccordionProps } from './OrganismsFooterAccordion.props'
import type { Link } from '~/types/link'

const props = defineProps<OrganismsFooterAccordionProps>()

const getSectionTitle = (title: Link | string): string => {
  return typeof title === 'string'
    ? title ?? ''
    : title?.label ?? ''
}
</script>

<template>
  <AccordionRoot
    v-for="(section, index) in links"
    :key="index"
    v-bind="props"
    type="multiple"
  >
    <AccordionItem :value="index.toString()" class="overflow-hidden space-y-padding-sm">
      <AccordionHeader>
        <AccordionTrigger class="group/trigger flex gap-x-xs cursor-default items-center outline-none w-full text-invert section-1 uppercase">
          <AtomsIcon
            name="NavPlusIcon"
            class="!text-icon-invert group-data-[state=open]/trigger:!hidden lg:!hidden !w-5 !h-5"
            aria-label="Toggle accordion"
          />
          <AtomsIcon
            name="NavMinusIcon"
            class="!text-icon-invert group-data-[state=closed]/trigger:!hidden lg:!hidden !w-5 !h-5 bg-center"
            aria-label="Toggle accordion"
          />
          <span>{{ getSectionTitle(section.title) }}</span>
        </AccordionTrigger>
      </AccordionHeader>
      <AccordionContent class="slide overflow-hidden">
        <div v-if="section.links?.length" class="flex flex-col gap-y-sm">
          <OrganismsFooterColumn
            brand="modernfold"
            :links="section.links"
            is-subsection
          />
        </div>
      </AccordionContent>
    </AccordionItem>
  </AccordionRoot>
</template>
