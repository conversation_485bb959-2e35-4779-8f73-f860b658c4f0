<script setup lang="ts">
import {
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuList,
  NavigationMenuRoot,
  NavigationMenuTrigger,
} from 'reka-ui'

import type { OrganismsMenuDropdownProps } from './OrganismsMenuDropdown.props'

defineProps<OrganismsMenuDropdownProps>()
</script>

<template>
  <NavigationMenuRoot
    class="relative z-40"
  >
    <NavigationMenuList class="flex list-none w-full">
      <NavigationMenuItem class="w-full">
        <NavigationMenuTrigger as-child>
          <AtomsLinkHeader v-if="link.asHeaderLink" v-bind="link" :class="`${items?.length ? 'no-underline!' : ''}`">
            {{ link.label }}
            <AtomsIcon v-if="link.external" name="GenExternalLink" />
          </AtomsLinkHeader>
          <AtomsLink
            v-else
            :inverted="link.inverted"
          >
            <AtomsIcon v-if="link.icon && link.icon.name" :name="link.icon.name" :local="typeof link.icon.local !== 'undefined' ? link.icon.local : true" />
            {{ link.label }}
          </AtomsLink>
        </NavigationMenuTrigger>

        <NavigationMenuContent
          v-if="items?.length"
          :class="[{ 'right-0 mt-2': isAccountLanguageVariant }, 'data-[motion=from-start]:animate-enterFromLeft data-[motion=from-end]:animate-enterFromRight data-[motion=to-start]:animate-exitToLeft data-[motion=to-end]:animate-exitToRight absolute w-full sm:w-auto mt-1']"
        >
          <OrganismsMenuInnerDropdown :items="items" :is-account-language-variant="isAccountLanguageVariant" />
        </NavigationMenuContent>
      </NavigationMenuItem>
    </NavigationMenuList>
  </NavigationMenuRoot>
</template>
