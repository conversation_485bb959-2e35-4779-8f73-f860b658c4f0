<script setup lang="ts">
import {
  DialogContent,
  DialogPortal,
  DialogRoot,
  DialogTrigger,
} from 'reka-ui'
import { ref, watchEffect, computed, watch, nextTick } from 'vue'
import type { MenuItem, MenuLevel } from '@design-system/components/Organisms/Menu/OrganismsMenuDropdown.props'
import type { OrganismsMenuMobileProps } from './OrganismsMenuMobile.props'
import type { SiteType } from '~~/env'

const { $ts } = useI18n()
const props = withDefaults(defineProps<OrganismsMenuMobileProps>(), {
  theme: 'default',
})

const isOpen = ref(false)
const { setIsLocked } = useLockScroll()

const isLanguageModalOpen = useState('isLanguageModalOpen')
const { currentLanguage } = useLanguage()

const history = ref<MenuLevel[]>([])
const activeMenuLevel = ref<MenuLevel>({ items: props.menuItems, label: null })

const config = useRuntimeConfig()
const site: SiteType = config.public.site as SiteType

const activeMenu = computed(() => activeMenuLevel.value.items?.filter(item => !item.hidden))
const previousMenuLabel = computed(() => history.value[history.value.length - 1]?.label ?? $ts('menu.main') as string)
const currentMenuLabel = computed(() => activeMenuLevel.value.label)
const canGoBack = computed(() => history.value.length > 0)

watchEffect(() => {
  if (isOpen.value) setIsLocked(true)
  else setIsLocked(false)

  // Reset pointer-events, as the dialog appends the 'pointer-events: none' style to the body on open, but in this case we want to allow the events on the header
  if (document && document.body) {
    setTimeout(() => {
      document.body.style.pointerEvents = 'auto'
    }, 1)
  }
})

/**
 * Prevent the dialog from closing when the user clicks outside of it
 */
const preventInteractOutside = (event: Event) => {
  event.preventDefault()
}

const goToSubmenu = (item: MenuItem) => {
  if (item.items?.length) {
    history.value.push(activeMenuLevel.value)
    activeMenuLevel.value = { items: item.items, label: item.label ?? null }
  }
}

const goBack = () => {
  const previousLevel = history.value.pop()
  if (previousLevel) {
    activeMenuLevel.value = previousLevel
  }
}

const handleLinkClick = (item: MenuItem) => {
  if (item.items?.length) {
    goToSubmenu(item)
    return
  }
  isOpen.value = false
}

const resetMenu = (items: MenuItem[]) => {
  history.value = []
  activeMenuLevel.value = { items, label: null }
}

// Reset menu when the menu items change
watch(() => props.menuItems, (newItems) => {
  resetMenu(newItems)
}, { deep: true })

// Reset menu when the route changes (including language changes)
const route = useRoute()
watch(() => route.fullPath, () => {
  nextTick(() => {
    resetMenu(props.menuItems)
  })
})

// Reset menu when the dialog is closed
watch(isOpen, (newValue) => {
  if (!newValue) {
    setTimeout(() => {
      resetMenu(props.menuItems)
    }, 300)
  }
})
</script>

<template>
  <div class="lg:hidden">
    <DialogRoot :open="isOpen" @update:open="isOpen = $event">
      <DialogTrigger as-child>
        <AtomsIconButton
          :theme="theme"
          aria-label="open menu mobile"
        >
          <template #icon>
            <AtomsIcon :name="isOpen ? 'NavCloseIcon' : 'NavMenu'" class="!text-[2rem]" />
          </template>
        </AtomsIconButton>
      </DialogTrigger>
      <DialogPortal>
        <Transition name="slide-up">
          <DialogContent
            v-if="isOpen"
            trap-focus
            class="fixed inset-x-0 bottom-0 bg-light flex flex-col lg:hidden after:pointer-events-none top-(--header-height)"
            @interact-outside="preventInteractOutside"
          >
            <Transition name="shift">
              <div
                v-if="canGoBack"
                class="px-4 py-padding-md"
              >
                <div
                  class="flex items-center py-padding-md"
                >
                  <AtomsLink
                    @click="goBack"
                  >
                    <AtomsIcon name="NavArrowLeft" />
                    {{ $ts('menu.back', { label: previousMenuLabel }) }}
                  </AtomsLink>
                </div>
                <p class="body-2 font-bold">
                  {{ currentMenuLabel }}
                </p>
              </div>
            </Transition>

            <div class="flex flex-col justify-between h-full z-1">
              <div class="overflow-y-auto bg-neutral">
                <Transition name="fade" mode="out-in">
                  <div :key="currentMenuLabel ?? 'root'" class="flex flex-col gap-md py-padding-md">
                    <template v-for="item in activeMenu" :key="item.label + item.to">
                      <AtomsLinkMenu
                        :to="item.items?.length ? '' : item.to"
                        class="flex justify-between items-center w-full text-base"
                        :external="item.external"
                        @click="handleLinkClick(item)"
                      >
                        <span class="truncate">{{ item.label }}</span>
                        <AtomsIcon v-if="item.items?.length" name="NavArrowRight" />
                      </AtomsLinkMenu>
                    </template>
                  </div>
                </Transition>
              </div>
              <div class="flex flex-col gap-xxs">
                <slot name="before-bottom" />
                <Transition name="fade" mode="out-in">
                  <div
                    v-if="!canGoBack"
                    class="flex flex-col gap-xl py-padding-xxl items-start px-padding-md bg-neutral"
                  >
                    <!-- Language selector -->
                    <button class="text-accent hover:text-hover focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent leading-none flex items-center gap-xxs underline-offset-2 underline cursor-pointer w-full" @click="isLanguageModalOpen = true">
                      <AtomsIcon
                        v-if="currentLanguage?.icon"
                        :name="currentLanguage?.icon"
                        :local="false"
                        class="text-2xl shrink-0"
                      />
                      <span>{{ currentLanguage?.label }}</span>
                    </button>
                    <MoleculesMobileLanguageSelector />

                    <AtomsLink
                      :to="site === 'modernfold' ? 'https://www.modernfold.com' : 'https://www.skyfold.com'"
                      external
                      target="_blank"
                    >
                      <AtomsIcon name="GenWorldIcon" class="text-[1.5rem]" />
                      {{ $ts('header.account.publicWebsite') }}
                    </AtomsLink>
                    <AtomsLink
                      :to="$localePath('/change-password')"
                    >
                      <AtomsIcon name="NavAccessIcon" />
                      {{ $ts('header.account.changePassword') }}
                    </AtomsLink>
                    <AtomsLink
                      to="/auth0/logout"
                      target="_self"
                      external
                    >
                      <AtomsIcon name="NavLogout" class="text-[1.5rem]" />
                      {{ $ts('header.account.logout') }}
                    </AtomsLink>
                  </div>
                </Transition>
              </div>
            </div>
          </DialogContent>
        </Transition>
      </DialogPortal>
    </DialogRoot>
  </div>
</template>

<style scoped>
.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.3s cubic-bezier(0.19, 1, 0.22, 1);
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateX(-100%);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.15s ease-in-out, filter 0.15s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  filter: blur(1px);
}

.shift-enter-active,
.shift-leave-active {
  transition: margin-top 0.3s ease-in-out, opacity 0.3s ease-in-out;
  will-change: margin-top, opacity;
  opacity: 1;
  margin-top: 0;
}

.shift-enter-from,
.shift-leave-to {
  opacity: 0;
  margin-top: -110px;
}
</style>
