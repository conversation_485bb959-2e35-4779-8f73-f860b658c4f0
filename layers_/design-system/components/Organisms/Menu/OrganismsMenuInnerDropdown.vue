<script setup lang="ts">
import {
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuList,
  NavigationMenuTrigger,
} from 'reka-ui'
import AtomsLink from '@design-system/components/Atoms/Link/AtomsLink.vue'
import AtomsLinkMenu from '@design-system/components/Atoms/Link/AtomsLinkMenu.vue'

import type { OrganismsMenuInnerDropdownProps } from './OrganismsMenuDropdown.props'

withDefaults(defineProps<OrganismsMenuInnerDropdownProps>(), {
  level: 0,
})
</script>

<template>
  <NavigationMenuSub>
    <NavigationMenuList :class="{ 'bg-brand rounded-md min-w-65 shadow-lg outline-none': true, 'bg-neutral': isAccountLanguageVariant, 'ml-1': level !== 0 }">
      <NavigationMenuItem v-for="item in items" :key="item.label" class="relative z-1">
        <NavigationMenuTrigger as-child>
          <NavigationMenuLink
            v-if="isAccountLanguageVariant"
            :as="AtomsLink"
            :to="item.to"
            :target="item.target"
            :external="item.external"
            hierarchy="secondary"
            class="flex items-center gap-xxs px-padding-md py-padding-xs text-accent bg-neutral rounded-md hover:bg-hover hover:text-invert "
          >
            <AtomsIcon
              v-if="item.icon"
              :name="item.icon"
              :local="item.icon.includes('circle-flags') ? false : true"
              :class="[{ 'mr-1': item.icon.includes('circle-flags') }, 'text-xl shrink-0']"
            />
            {{ item.label }}
          </NavigationMenuLink>
          <NavigationMenuLink
            v-else-if="!item.hidden"
            :as="AtomsLinkMenu"
            :to="item.to"
            :external="item.external"
            :target="item.external ? '_blank' : undefined"
          >
            {{ item.label }}
            <AtomsIcon
              v-if="item.items?.length"
              name="NavArrowRight"
              class="w-4 h-4"
            />
            <AtomsIcon v-else-if="item.external" class="w-4 h-4" name="GenExternalLink" />
          </NavigationMenuLink>
        </NavigationMenuTrigger>
        <NavigationMenuContent class="absolute top-0 left-full rounded-xl max-h-[calc(100vh-1rem-var(--header-height))] overflow-y-auto">
          <OrganismsMenuInnerDropdown
            :items="item.items"
            :level="level + 1"
          />
        </NavigationMenuContent>
      </NavigationMenuItem>
    </NavigationMenuList>
  </NavigationMenuSub>
</template>
