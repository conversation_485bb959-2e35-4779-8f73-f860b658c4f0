import type { AtomsLinkProps } from '../../Atoms/Link/AtomsLink.props'
import type { Link } from '~/types/link'

export type MenuItem = Link & {
  items?: MenuItem[]
  icon?: string
  hidden?: boolean
}

export type OrganismsMenuDropdownProps = {
  link: AtomsLinkProps & { asHeaderLink?: boolean, selected?: boolean, icon?: { name: string, local?: boolean }, disabled?: boolean }
  items?: MenuItem[]
  isAccountLanguageVariant?: boolean
}

export type OrganismsMenuInnerDropdownProps = Omit<OrganismsMenuDropdownProps, 'link'> & {
  level?: number
}

export type MenuLevel = {
  items: MenuItem[]
  label: string | null
}
