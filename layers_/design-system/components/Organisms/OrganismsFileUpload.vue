<script setup lang="ts">
import * as v from 'valibot'
import type { OrganismsFileUploadProps } from './OrganismsFileUpload.props'

const props = withDefaults(defineProps<OrganismsFileUploadProps>(), {
  maxSize: 10485760,
  disabled: false,
  readOnly: false,
})

const emit = defineEmits(['update:modelValue', 'change', 'error'])

const { t } = useI18n()
const fileInputRef = ref<HTMLInputElement>()

const selectedFile = computed({
  get: () => props.modelValue,
  set: (value: File | null) => emit('update:modelValue', value),
})

const fileSchema = v.pipe(
  v.file(),
  v.mimeType(props.accept as `${string}/${string}`[] || []),
  v.maxSize(props.maxSize),
)

const validateFile = (file: File): boolean => {
  try {
    v.parse(fileSchema, file)
    return true
  }
  catch (error) {
    if (error instanceof v.ValiError) {
      const firstIssue = error.issues[0]
      emit('error', firstIssue?.message || t('fileUpload.invalidFile'))
    }
    return false
  }
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files

  if (!files || files.length === 0) return

  const file = files[0]
  if (file && validateFile(file)) {
    selectedFile.value = file
    emit('change', file)
  }
  target.value = ''
}

const openFileDialog = () => {
  if (!props.disabled) {
    fileInputRef.value?.click()
  }
}

const removeFile = () => {
  selectedFile.value = null
  emit('update:modelValue', null)
}

const downloadFile = (file: File) => {
  const url = URL.createObjectURL(file)
  const a = document.createElement('a')
  a.href = url
  a.download = file.name
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const formatExtension = (extension: string) => {
  return extension.includes('/') ? `.${extension.split('/')[1]}` : extension
}

const accept = computed(() => {
  return props.accept?.map(type => formatExtension(type)).join('|')
})
</script>

<template>
  <div :class="props.class">
    <input
      ref="fileInputRef"
      type="file"
      :accept="accept"
      :disabled="disabled"
      class="sr-only"
      @change="handleFileSelect"
    >
    <AtomsButton
      v-if="!selectedFile && !readOnly"
      :disabled="disabled"
      anatomy="secondary"
      class="w-full md:max-w-80 md:mx-auto"
      @click.prevent="openFileDialog"
    >
      {{ t('fileUpload.uploadFile') }}
    </AtomsButton>

    <div
      v-else-if="!readOnly"
      class="space-y-4"
    >
      <div
        v-if="selectedFile"
        class="flex items-center gap-2 justify-between"
      >
        <AtomsLink
          :label="(selectedFile as File).name"
          class="flex items-center gap-2 flex-1 min-w-0"
          @click="downloadFile(selectedFile as File)"
        >
          <template #default>
            <AtomsIcon :name="getFileIcon(selectedFile.name)" />
            <span class="truncate">{{ (selectedFile as File).name }}</span>
          </template>
        </AtomsLink>

        <AtomsButton
          size="icon"
          anatomy="secondary"
          :aria-label="t('fileUpload.removeFile')"
          @click.prevent="removeFile"
        >
          <template #icon>
            <AtomsIcon name="GenTrashIcon" />
          </template>
        </AtomsButton>
      </div>

      <AtomsButton
        anatomy="secondary"
        :disabled="disabled"
        class="w-full md:max-w-80 md:mx-auto"
        @click.prevent="openFileDialog"
      >
        {{ t('fileUpload.replaceFile') }}
      </AtomsButton>
    </div>

    <div v-else-if="selectedFile && readOnly">
      <AtomsLink
        :label="(selectedFile as File).name"
        class="flex items-center gap-2 flex-1 min-w-0"
        @click="downloadFile(selectedFile as File)"
      >
        <template #default>
          <AtomsIcon :name="getFileIcon(selectedFile.name)" />
          <span class="truncate">{{ (selectedFile as File).name }}</span>
        </template>
      </AtomsLink>
    </div>
  </div>
</template>
