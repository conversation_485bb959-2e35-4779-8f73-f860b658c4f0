<script lang="ts" setup>
import type { OrganismsFormCardProps } from './OrganismsFormCard.props'
import OrganismsFormCard from './OrganismsFormCard.vue'

defineEmits<{
  edit: [id: string]
}>()

const data = defineModel<Record<string, unknown>>()

withDefaults(defineProps<OrganismsFormCardProps>(), {
  headerGap: 'xs',
  readOnly: false,
  showEditButton: true,
})
</script>

<template>
  <div class="[&:not(:last-child)]:mb-1">
    <OrganismsFormCard
      :id="id"
      :title="title"
      :header-gap="headerGap"
      :read-only="readOnly"
      :show-edit-button="showEditButton"
      @edit="$emit('edit', id)"
    >
      <template #header>
        <slot name="header" />
      </template>
      <template #default>
        <FormKit
          :id="id"
          v-model="data"
          type="group"
          :disabled="readOnly"
        >
          <slot />
        </FormKit>
      </template>
    </OrganismsFormCard>
  </div>
</template>
