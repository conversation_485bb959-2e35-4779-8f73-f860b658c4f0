<script lang="ts" setup>
import type { OrganismsFormCardProps } from './OrganismsFormCard.props'

defineEmits<{
  edit: [id: string]
}>()

withDefaults(defineProps<OrganismsFormCardProps>(), {
  headerGap: 'xs',
  readOnly: false,
  isFormkitGroup: true,
  showEditButton: true,
})
</script>

<template>
  <div
    class="bg-neutral p-padding-md md:p-padding-xxl rounded-sm flex flex-col [&:not(:last-child)]:mb-1"
    :class="`gap-${headerGap}`"
  >
    <div v-if="$slots.header || title || readOnly">
      <div class="flex items-center justify-between gap-2">
        <h3 v-if="title" class="subtitle-3 font-semibold">
          {{ title }}
        </h3>
        <span v-else />

        <AtomsButton
          v-if="readOnly && showEditButton"
          anatomy="tertiary"
          size="m"
          as="button"
          type="button"
          @click="$emit('edit', id)"
        >
          {{ $ts('forms.shared.actions.edit') }}
        </AtomsButton>
      </div>
      <slot name="header" />
    </div>
    <div class="grid md:grid-cols-2 gap-xl">
      <slot />
    </div>
  </div>
</template>
