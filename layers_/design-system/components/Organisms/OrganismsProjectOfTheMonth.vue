<script setup lang="ts">
import type { OrganismsProjectOfTheMonthProps } from './OrganismsProjectOfTheMonth.props'

const props = defineProps<OrganismsProjectOfTheMonthProps>()
</script>

<template>
  <div class="container py-padding-md lg:py-padding-xxl space-y-6">
    <h2 class="headline-2">
      {{ $ts("organisms.projectOfTheMonth.title") }}
    </h2>
    <div class="flex flex-col md:flex-row gap-6 ">
      <img :src="projectImage.src" :alt="projectImage.alt" class="w-full md:w-1/2 aspect-[4/3] object-cover">
      <div class="w-full md:w-1/2">
        <ul class="flex flex-col gap-4">
          <li v-for="key in ['project', 'scope', 'architect', 'specifyingDealer', 'dealer']" :key="key">
            <p class="body-1 font-semibold">
              {{ $ts(`organisms.projectOfTheMonth.${key}`) }}
            </p>
            <p class="body-1">
              {{ props[key as keyof OrganismsProjectOfTheMonthProps] }}
            </p>
          </li>
        </ul>
      </div>
    </div>
    <div v-if="longDescription && descriptionImage" class="flex flex-col md:flex-row gap-6 items-center">
      <div class="w-full md:w-1/2">
        <p>{{ longDescription }}</p>
      </div>
      <img :src="descriptionImage.src" :alt="descriptionImage.alt" class="w-full md:w-1/2 aspect-[4/3] object-cover">
    </div>
  </div>
</template>
