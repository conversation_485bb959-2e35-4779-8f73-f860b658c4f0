<script lang="ts" setup>
import type { OrganismsRecapCardProps } from './OrganismsRecapCard.props'

defineProps<OrganismsRecapCardProps>()
</script>

<template>
  <OrganismsCard
    content-class="px-padding-sm"
    container-class="border border-neutral"
  >
    <div class="flex flex-col gap-md">
      <div>
        <div class="flex items-center justify-between gap-sm">
          <OrganismsCardTitle>{{ title }}</OrganismsCardTitle>
          <AtomsIcon :name="icon" class="shrink-0 text-[2.5rem]" />
        </div>
        <ul class="-mx-padding-sm *:px-padding-sm *:even:bg-light">
          <li v-for="item in list" :key="item.label" class="body-1 flex items-baseline gap-xxs">
            <span class="body-2 font-bold text-nowrap">
              {{ item.label }}
            </span>
            <span class="break-all">
              {{ item.value }}
            </span>
          </li>
        </ul>
      </div>
    </div>
    <div v-if="$slots.actions" class="flex items-end justify-end gap-sm flex-1">
      <slot name="actions" />
    </div>
  </OrganismsCard>
</template>
