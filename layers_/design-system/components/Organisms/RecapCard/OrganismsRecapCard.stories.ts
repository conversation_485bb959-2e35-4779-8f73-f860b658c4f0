import type { StoryObj } from '@storybook/vue3'
import AtomsButton from '../../Atoms/AtomsButton.vue'
import AtomsIconButton from '../../Atoms/AtomsIconButton.vue'
import AtomsIcon from '../../Atoms/AtomsIcon.vue'
import OrganismsRecapCard from './OrganismsRecapCard.vue'

const meta = {
  title: 'Organisms/RecapCard',
  component: OrganismsRecapCard,
  tags: [],
  argTypes: {
    title: {
      control: 'text',
      description: 'The main title of the recap card.',
    },
    icon: {
      control: 'text',
      description: 'The name of the icon to display in the top-right corner (e.g., GenWall, GenParticipants).',
    },
  },
  args: {
    title: 'Sample Recap Title',
    icon: 'GenWall',
  },
}

export default meta
type Story = StoryObj<typeof meta>

const defaultListItems = [
  { label: 'Quantity', value: '1' },
  { label: 'Lenght', value: '3000 mm' },
  { label: 'Ceiling Height', value: '122 mm' },
  { label: 'Beam height', value: '122 mm' },
  { label: 'Motor', value: 'Standard' },
  { label: 'Perimeter seal color', value: 'Black' },
  { label: 'Panel reveal/joint color', value: 'Black' },
  { label: 'Finish', value: 'Unfinished' },
]

const participantListItems = [
  { label: 'AIA Member', value: 'Yes' },
  { label: 'Certificate request', value: 'Yes' },
  { label: 'AIA Membership Number', value: '32141231' },
  { label: 'Email address', value: '<EMAIL>' },
]

const StoryWrapper = {
  template: '<div class="max-w-md mx-auto p-4"><slot /></div>',
}

export const Default: Story = {
  render: args => ({
    components: {
      OrganismsRecapCard,
      AtomsButton,
      AtomsIconButton,
      AtomsIcon,
      StoryWrapper,
    },
    setup() {
      return { args }
    },
    template: `
      <StoryWrapper>
        <OrganismsRecapCard :title="args.title" :icon="args.icon" :list="args.list">
          <template #actions>
            <AtomsIconButton>
              <template #icon><AtomsIcon name="GenTrashIcon"/></template>
            </AtomsIconButton>
            <AtomsButton size="m" anatomy="secondary">
              Secondary Action
              <template #icon><AtomsIcon name="GenExternalLink"/></template>
            </AtomsButton>
            <AtomsButton size="m">Primary Action</AtomsButton>
          </template>
        </OrganismsRecapCard>
      </StoryWrapper>
    `,
  }),
  args: {
    title: 'Classic 55',
    icon: 'GenWall',
    list: defaultListItems,
  },
}

export const WithoutActions: Story = {
  render: args => ({
    components: { OrganismsRecapCard, StoryWrapper, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
      <StoryWrapper>
        <OrganismsRecapCard :title="args.title" :icon="args.icon" :list="args.list"/>
      </StoryWrapper>
    `,
  }),
  args: {
    title: 'John Doe',
    icon: 'GenPerson',
    list: participantListItems,
  },
}
