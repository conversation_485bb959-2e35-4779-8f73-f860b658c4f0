<script setup lang="ts">
import type { OrganismsMessageProps } from './OrganismsMessage.props'

const props = withDefaults(defineProps<OrganismsMessageProps>(), {
  status: 'info',
  size: 'l',
})

const statusIcon = computed(() => {
  switch (props.status) {
    case 'info':
      return 'GenInformationIcon'
    case 'success':
      return 'NavCheckIcon'
    case 'alert':
      return 'GenHelpIcon'
    case 'error':
      return 'GenAttentionIcon'
    default:
      return 'GenInformationIcon'
  }
})

const message = tv({
  slots: {
    container: 'max-w-80 flex flex-col gap-y-xxs rounded-extra-small shadow-[0_20px_50px_0_rgb(0_0_0/0.1)]',
    headingContainer: 'flex gap-x-xxs items-center',
    title: 'text-neutral overflow-hidden',
    icon: 'shrink-0',
  },
  variants: {
    size: {
      l: {
        container: 'p-padding-md',
        title: 'subtitle-3',
        icon: '!w-12 !h-12',
      },
      s: {
        container: 'py-padding-xxs px-padding-xs border',
        title: 'body-2 font-bold line-clamp-3',
        icon: '!w-6 !h-6',
      },
    },
    status: {
      info: {
        container: 'bg-info border-focus shadow-none',
        icon: 'text-info',
      },
      success: {
        container: 'bg-success border-success',
        icon: 'text-success',
      },
      alert: {
        container: 'bg-warning border-warning',
        headingContainer: 'py-padding-xs',
        icon: 'text-warning',
      },
      error: {
        container: 'bg-alert border-alert',
        icon: 'text-alert',
      },
    },
  },
})

const messageClasses = computed(() => message({ ...props }))
</script>

<template>
  <div :class="messageClasses.container()">
    <div :class="messageClasses.headingContainer()">
      <AtomsIcon :name="statusIcon" :class="messageClasses.icon()" />
      <p :class="messageClasses.title()">
        {{ title }}
      </p>
    </div>
    <p v-if="description && size === 'l'" class="body-2 text-neutral">
      {{ description }}
    </p>
  </div>
</template>
