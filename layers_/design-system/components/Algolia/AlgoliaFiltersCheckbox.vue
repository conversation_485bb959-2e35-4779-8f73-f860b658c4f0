<script setup lang="ts">
import type { AlgoliaFiltersCheckboxProps } from './AlgoliaFiltersCheckbox.props'
import AlgoliaHelperCheckboxFilters from './Helpers/AlgoliaHelperCheckboxFilters.vue'

defineProps<AlgoliaFiltersCheckboxProps>()
</script>

<template>
  <div>
    <AisRefinementList :attribute="attribute">
      <template
        #default="{
          items,
          refine,
        }"
      >
        <AlgoliaHelperCheckboxFilters :items>
          <template
            #default="{ value, getChangedOptions }"
          >
            <FormKit
              :id="name"
              :model-value="value"
              type="checkbox"
              :name="name"
              :label="label"
              :options="items"
              decorator-icon="myCheckbox"
              @input="(newValue) => {
                getChangedOptions(newValue)
                  .forEach(value => refine(value ?? ''))
              }"
            />
          </template>
        </AlgoliaHelperCheckboxFilters>
      </template>
    </AisRefinementList>
  </div>
</template>
