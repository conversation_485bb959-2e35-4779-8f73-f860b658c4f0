<script setup lang="ts">
import type { AlgoliaFiltersSortProps } from '@design-system/components/Algolia/AlgoliaFiltersSort.props'

defineProps<AlgoliaFiltersSortProps>()
</script>

<template>
  <div>
    <AisSortBy>
      <template #default="{ items, refine }">
        <FormKit
          :id="name"
          type="dropdown"
          :name="name"
          :placeholder="$ts('portfolio.filters.sort')"
          :options="items"
          select-icon="NavChevronDown"
          selected-icon="NavDiamond"
          :classes="{
            selector: 'bg-transparent rounded-none border-0 border-b-1 !border-accent group-data-[expanded]:!border-hover',
            inner: '!shadow-none flex-col',
            selectIcon: '[&>svg]:w-6 [&>svg]:h-6 text-accent group-data-[expanded]:text-hover',
            selectedIcon: '[&>svg]:w-6 [&>svg]:h-6 !text-accent',
            placeholder: '!section-1 uppercase !text-accent group-data-[expanded]:!text-hover !font-section-sm',
            option: 'text-accent group-hover/item:text-invert',
            listitem: 'group/item !bg-white hover:!bg-hover rounded-xs first:rounded-t-none pl-padding-md py-padding-xs',
            dropdownWrapper: 'border border-hover group-data-[expanded]:shadow-[0_15px_20px_rgba(0,0,0,0.05]',
          }"
          @input="(value?: string) => { refine(value ?? '') }"
        >
          <template #selection="{ option }">
            <p class="!section-1 uppercase text-accent group-data-[expanded]:text-hover h-full">
              <span>
                {{ $ts('portfolio.filters.sort') }} {{ option.label }}
              </span>
            </p>
          </template>
        </FormKit>
      </template>
    </AisSortBy>
  </div>
</template>
