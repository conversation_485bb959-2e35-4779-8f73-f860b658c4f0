<script setup lang="ts">
import type { AlgoliaFiltersDropdownProps } from './AlgoliaFiltersDropdown.props'
import AlgoliaHelperSelectionFilters from './Helpers/AlgoliaHelperSelectionFilters.vue'

defineProps<AlgoliaFiltersDropdownProps>()
</script>

<template>
  <div :id="name">
    <AisMenuSelect :attribute="attribute">
      <template #default="{ items, refine }">
        <AlgoliaHelperSelectionFilters :items>
          <template
            #default="{ value }"
          >
            <FormKit
              :id="name"
              :key="value"
              :value="value"
              type="dropdown"
              :name="name"
              :label="label"
              :placeholder="placeholder"
              :options="items"
              select-icon="NavChevronDown"
              selected-icon="NavCheckIcon"
              @input="(value) => { refine(value ?? '') }"
            />
          </template>
        </AlgoliaHelperSelectionFilters>
      </template>
    </AisMenuSelect>
  </div>
</template>
