<script setup lang="ts">
import type { AlgoliaFiltersAutocompleteProps } from './AlgoliaFiltersAutocomplete.props'
import AlgoliaHelperSelectionFilters from './Helpers/AlgoliaHelperSelectionFilters.vue'

defineProps<AlgoliaFiltersAutocompleteProps>()
</script>

<template>
  <div :id="name">
    <AisMenuSelect :attribute="attribute">
      <template #default="{ items, refine }">
        <AlgoliaHelperSelectionFilters :items>
          <template
            #default="{ value }"
          >
            <FormKit
              :id="name"
              :key="value"
              :value="value"
              type="autocomplete"
              :name="name"
              :label="label"
              :placeholder="placeholder"
              :options="items"
              clear-search-on-open
              open-on-focus
              selection-removable
              @input="(value) => { refine(value ?? '') }"
            />
          </template>
        </AlgoliaHelperSelectionFilters>
      </template>
    </AisMenuSelect>
  </div>
</template>
