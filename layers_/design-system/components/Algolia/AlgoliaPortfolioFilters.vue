<script setup lang='ts'>
import OrganismsWrapperFilterSearch from '../Organisms/OrganismsWrapperFilterSearch.vue'

const searchValue = ref('')

const handleClear = (refineClearAll: () => void) => {
  searchValue.value = ''
  refineClearAll()
}
</script>

<template>
  <div class="h-[calc(100svh-140px)] overflow-y-auto lg:overflow-y-visible lg:h-auto relative bg-neutral space-y-padding-xl rounded-xs px-padding-md py-padding-xl">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-x-6 gap-y-xl mb-20 lg:mb-0">
      <div class="flex items-center justify-between md:max-lg:col-span-2">
        <h4 class="headline-4">
          {{ $ts('portfolio.filters.title') }}
        </h4>
        <AisClearRefinements>
          <template #default="{ refine, canRefine }">
            <AtomsLink :disabled="!canRefine && !searchValue" @click="handleClear(refine)">
              {{ $ts('portfolio.filters.reset') }}
            </AtomsLink>
          </template>
        </AisClearRefinements>
      </div>

      <div class="md:col-span-2 lg:col-span-full grid grid-cols-subgrid *:col-span-1">
        <AisSearchBox>
          <template #default="{ refine }">
            <OrganismsWrapperFilterSearch v-model="searchValue" :refine-search="refine" />
          </template>
        </AisSearchBox>
      </div>

      <AlgoliaFiltersDropdown
        name="product.name"
        attribute="product.name"
        :placeholder="$ts('portfolio.filters.selectSomething')"
        :label="$ts('portfolio.filters.labels.product__name')"
      />
      <AlgoliaFiltersDropdown
        name="project_type.name"
        attribute="project_type.name"
        :placeholder="$ts('portfolio.filters.selectSomething')"
        :label="$ts('portfolio.filters.labels.project_type__name')"
      />
      <AlgoliaFiltersDropdown
        name="finish_type"
        attribute="finish_type"
        :placeholder="$ts('portfolio.filters.selectSomething')"
        :label="$ts('portfolio.filters.labels.finish_type')"
      />

      <MoleculesAccordionWrapper class="col-span-full">
        <template #title>
          <div class="group/trigger py-padding-xxs flex gap-x-xs justify-between items-center outline-none w-full text-accent section-1 uppercase cursor-default border-b border-neutral">
            <span> {{ $ts('portfolio.filters.advanced') }}</span>
            <AtomsIcon
              name="NavPlusIcon"
              class="!text-icon-accent group-data-[state=open]/trigger:!hidden !w-5 !h-5"
              :aria-label="$ts('portfolio.filters.toggleAdvanced')"
            />
            <AtomsIcon
              name="NavMinusIcon"
              class="!text-icon-accent group-data-[state=closed]/trigger:!hidden !w-5 !h-5"
              :aria-label="$ts('portfolio.filters.toggleAdvanced')"
            />
          </div>
        </template>
        <template #default>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-x-6 gap-y-xl pt-padding-xl">
            <AlgoliaFiltersCheckbox
              class="md:col-span-2 lg:col-span-full"
              name="special_finish"
              attribute="special_finish"
              :label="$ts('portfolio.filters.labels.specialFinish')"
            />
            <AlgoliaFiltersAutocomplete
              name="brand"
              attribute="brand"
              :placeholder="$ts('portfolio.filters.writeSomething')"
              :label="$ts('portfolio.filters.labels.brand')"
            />
            <AlgoliaFiltersAutocomplete
              name="architect_design"
              attribute="architect_design"
              :placeholder="$ts('portfolio.filters.writeSomething')"
              :label="$ts('portfolio.filters.labels.architect_design')"
            />
            <AlgoliaFiltersAutocomplete
              name="dealer"
              attribute="dealer"
              :placeholder="$ts('portfolio.filters.writeSomething')"
              :label="$ts('portfolio.filters.labels.dealer')"
            />
            <AlgoliaFiltersAutocomplete
              name="country.name"
              attribute="country.name"
              :placeholder="$ts('portfolio.filters.writeSomething')"
              :label="$ts('portfolio.filters.labels.country__name')"
            />
            <AlgoliaFiltersAutocomplete
              name="state"
              attribute="state"
              :placeholder="$ts('portfolio.filters.writeSomething')"
              :label="$ts('portfolio.filters.labels.state')"
            />
          </div>
        </template>
      </MoleculesAccordionWrapper>
    </div>
  </div>
</template>
