<script setup lang="ts">
// Helper component which takes a list of items as a prop, where each item has a `value` and an `isRefined` property.
// It computes the `value` of the first item in the list that has `isRefined` set to true.
// The computed `value` is then passed to the default slot as a prop.

const props = defineProps<{
  items: {
    value: string
    isRefined: boolean
  }[] | undefined
}>()

const value = computed(() => props.items?.find(item => item.isRefined)?.value)
</script>

<template>
  <slot :value />
</template>
