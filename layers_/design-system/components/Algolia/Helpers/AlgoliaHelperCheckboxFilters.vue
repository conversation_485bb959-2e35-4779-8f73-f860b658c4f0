<script setup lang="ts">
/**
 * This component serves as a helper for the multi-select checkbox filter in order to expose the following to the slot scope:
 *   - `value`: The array of currently selected filter values.
 *   - `getChangedOptions`: A function to compute the changed options given a new selection.
 */
const props = defineProps<{
  items: {
    value: string
    isRefined: boolean
  }[] | undefined
}>()

// Computes the currently selected (refined) filter values
const value = computed(
  () =>
    props.items
      ?.filter(item => item.isRefined)
      ?.map(item => item.value)
      ?? [],
)

/**
 * @description Determine the difference between the current selected values and a new set of values, enabling tracking of changes in the selection thanks to the utility 'difference'.
 * @param newValue The new set of values to compare against the current selected values
 * @returns The changed options as an array of strings
 */
const getChangedOptions = (newValue: string[] | undefined) => difference(value.value, newValue ?? [])
</script>

<template>
  <slot :value :get-changed-options />
</template>
