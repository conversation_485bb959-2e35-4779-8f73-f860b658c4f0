<script lang="ts">
import { marked } from 'marked'

export default defineComponent({
  props: {
    content: {
      type: String,
      required: true,
    },
    container: {
      type: String,
      required: false,
      default: 'span',
    },
  },

  setup(props) {
    const md = computed(() => {
      return marked(props.content)
    })
    return () =>
      h(props?.container, {
        innerHTML: md.value,
      })
  },
})
</script>
