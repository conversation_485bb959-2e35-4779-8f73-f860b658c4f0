import type { Meta, StoryObj } from '@storybook/vue3'
import MoleculesTooltip from './MoleculesTooltip.vue'

const meta: Meta<typeof MoleculesTooltip> = {
  title: 'Molecules/Tooltip',
  component: MoleculesTooltip,
  tags: ['autodoc'],
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=2152-6918&p=f&m=dev',
      },
    ],
  },
}

export default meta
type Story = StoryObj<typeof MoleculesTooltip>

export const Default: Story = {
  render: args => ({
    components: { MoleculesTooltip },
    setup() {
      return { args }
    },
    template: `
    <div class="p-10 w-full flex justify-center">
        <MoleculesTooltip v-bind="args"/>
      </div>
      `,
  }),
  args: {
    content: 'Qui praesentium architecto et nisi provident qui sint eius vel architecto consequatur in atque veritatis. Ex ipsa vitae qui delectus vitae ex distinctio accusantium ut accusantium rerum.',
    avoidCollisions: true,
  },
}

export const Bottom: Story = {
  render: args => ({
    components: { MoleculesTooltip },
    setup() {
      return { args }
    },
    template: `
    <div class="p-10 w-full flex justify-center">
      <MoleculesTooltip v-bind="args" />
    </div>
    `,
  }),
  args: {
    content: 'Qui praesentium architecto et nisi provident qui sint eius vel architecto consequatur in atque veritatis. Ex ipsa vitae qui delectus vitae ex distinctio accusantium ut accusantium rerum.',
    avoidCollisions: false,
    side: 'bottom',
  },
}

export const Left: Story = {
  render: args => ({
    components: { MoleculesTooltip },
    setup() {
      return { args }
    },
    template: `
    <div class="p-10 w-full flex justify-center">
        <MoleculesTooltip v-bind="args"/>
      </div>
      `,
  }),
  args: {
    content: 'Qui praesentium architecto et nisi provident qui sint eius vel architecto consequatur in atque veritatis. Ex ipsa vitae qui delectus vitae ex distinctio accusantium ut accusantium rerum.',
    avoidCollisions: false,
    side: 'left',
  },
}

export const Right: Story = {
  render: args => ({
    components: { MoleculesTooltip },
    setup() {
      return { args }
    },
    template: `
    <div class="p-10 w-full flex justify-center">
        <MoleculesTooltip v-bind="args"/>
      </div>
      `,
  }),
  args: {
    content: 'Qui praesentium architecto et nisi provident qui sint eius vel architecto consequatur in atque veritatis. Ex ipsa vitae qui delectus vitae ex distinctio accusantium ut accusantium rerum.',
    avoidCollisions: false,
    side: 'right',
  },
}

export const Top: Story = {
  render: args => ({
    components: { MoleculesTooltip },
    setup() {
      return { args }
    },
    template: `
    <div class="p-10 w-full flex justify-center items-center">
        <MoleculesTooltip v-bind="args"/>
      </div>
      `,
  }),
  args: {
    side: 'top',
    content: 'Lorem ipsum',
    avoidCollisions: false,

  },
}
