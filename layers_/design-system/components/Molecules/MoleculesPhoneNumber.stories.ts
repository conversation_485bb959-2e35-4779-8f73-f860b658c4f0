import type { Meta, StoryObj } from '@storybook/vue3'
import MoleculesPhoneNumber from './MoleculesPhoneNumber.vue'

const meta: Meta<typeof MoleculesPhoneNumber> = {
  title: 'Molecules/Phone Number',
  component: MoleculesPhoneNumber,
  tags: ['autodoc'],
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=2121-56',
      },
    ],
  },
  argTypes: {
    prefix: {
      label: {
        control: 'text',
      },
      placeholder: {
        control: 'text',
      },
    },
    number: {
      label: {
        control: 'text',
      },
      placeholder: {
        control: 'text',
      },
    },
    help: {
      control: 'text',
    },
    disabled: {
      control: 'boolean',
    },
    validation: {
      control: 'select',
      options: ['required', ''],
    },
    validationVisibility: {
      control: 'select',
      options: ['blur', 'live', 'dirty'],
    },
    showValidationIcon: {
      control: 'boolean',
      description: 'Show the suffix icon with validation status',
    },
  },
  decorators: [
    () => ({
      template: '<div class="flex flex-col gap-8 max-w-[400px]"><story/></div>',
    }),
  ],
}

export default meta
type Story = StoryObj<typeof MoleculesPhoneNumber>

export const Default: Story = {
  render: args => ({
    setup() {
      const telValue = ref()

      return { args, telValue }
    },
    components: { MoleculesPhoneNumber },
    template: `
      <p>Default</p>

      <MoleculesPhoneNumber
        v-model:tel-value="telValue"
        v-bind="args"
        type="text"
      /> 
      <pre>{{ text }}</pre>
    `,
  }),
  args: {
    prefix: {
      label: 'Prefix',
      placeholder: '0',
    },
    number: {
      label: 'Number',
      placeholder: '************',
    },
    help: 'This is a help message',
    disabled: false,
    validation: 'required',
    validationVisibility: 'live',
    showValidationIcon: true,
    id: 'phone_number',
  },
}

export const Disabled: Story = {
  render: args => ({
    setup() {
      const telValue = ref()

      return { args, telValue }
    },
    components: { MoleculesPhoneNumber },
    template: `
      <p>Disabled</p>

      <MoleculesPhoneNumber
        v-model:tel-value="telValue"
        v-bind="args"
        type="text"
        disabled
      /> 
      <pre>{{ text }}</pre>
    `,
  }),
  args: {
    prefix: {
      label: 'Prefix',
      placeholder: '0',
    },
    number: {
      label: 'Number',
      placeholder: '************',
    },
    disabled: true,
    id: 'phone_number',
  },
}
