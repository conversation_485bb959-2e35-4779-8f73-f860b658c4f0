<script setup lang="ts">
const { currentLanguage, languageMenuMobile } = useLanguage()
const isLanguageModalOpen = useState('isLanguageModalOpen', () => false)
const selectedLanguage = ref(currentLanguage.value?.value)

const pickLanguage = (language: string) => {
  if (language === selectedLanguage.value) {
    return
  }
  selectedLanguage.value = language
}

const confirmSelectedLanguage = (language: string) => {
  const to = languageMenuMobile.value.find(item => item.value === language)?.to
  if (to) {
    navigateTo(to, { external: true })
  }
  isLanguageModalOpen.value = false
}

watch(isLanguageModalOpen, (newVal) => {
  if (!newVal) {
    setTimeout(() => {
      selectedLanguage.value = currentLanguage.value?.value
    }, 300)
  }
})
</script>

<template>
  <MoleculesDialog
    v-model:open="isLanguageModalOpen"
    icon="GenWorldIcon"
    content-class="max-w-88 w-[90dvw]"
    :title="$ts('header.languageSelector.title')"
  >
    <div class="flex flex-col gap-sm">
      <template v-for="item in languageMenuMobile" :key="item.label">
        <button
          :class="[{ 'bg-light': selectedLanguage === item.value }, 'flex items-center gap-xs cursor-pointer w-full px-2.5 py-padding-md']"
          @click="pickLanguage(item.value)"
        >
          <AtomsIcon :name="item.icon" :local="false" class="text-xl" />
          <span class="link-2 text-left underline text-accent grow">
            {{ item.label }}
          </span>
          <AtomsIcon
            v-if="selectedLanguage === item.value"
            name="NavCheckSquareContainedLine"
            class="text-icon-success text-xl"
          />
        </button>
      </template>
    </div>
    <template #actions>
      <div class="flex *:flex-1 gap-xs">
        <AtomsButton
          anatomy="secondary"
          @click="isLanguageModalOpen = false"
        >
          {{ $ts('header.languageSelector.cancel') }}
        </AtomsButton>

        <AtomsButton
          anatomy="primary"

          @click="confirmSelectedLanguage(selectedLanguage!)"
        >
          {{ $ts('header.languageSelector.confirm') }}
        </AtomsButton>
      </div>
    </template>
  </MoleculesDialog>
</template>
