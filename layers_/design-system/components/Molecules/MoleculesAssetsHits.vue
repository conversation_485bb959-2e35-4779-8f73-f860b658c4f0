<script setup lang='ts'>
import type { MoleculesAssetsHitsProps, Item } from './MoleculesAssetsHits.props'

defineProps<MoleculesAssetsHitsProps>()

const emit = defineEmits<{
  'update:currentFolder': [currentFolder: string]
}>()

const { toggleFolder } = useCurrentPageFolder()

const handleClick = (card: Item) => {
  toggleFolder(card.full_slug, [card.parent_full_slug])
  emit('update:currentFolder', card.full_slug)
}
</script>

<template>
  <div class="lg:columns-2 lg:space-y-padding-xl lg:gap-x-(--spacing-gutter)">
    <div
      v-for="card in items"
      :key="card.objectID"
      class="w-full inline-block"
    >
      <AtomsFolderButton
        v-if="card?.content_type === 'resource_folder'"
        :name="card?.name"
        last-update="2025/01/01"
        :text="card?.description"
        @on-click="handleClick(card)"
      />
      <!-- TODO: get last-update and status from algolia -->
      <AtomsFileButton
        v-else
        :id="card.asset.id.toString()"
        :name="card?.name"
        :src="card.asset.filename ?? ''"
        last-update="27-03-2025"
        status="none"
        :show-favorite="false"
      />
    </div>
    <p v-if="items.length === 0" class="p-4">
      {{ $t('assets.noResults') }}
    </p>
  </div>
</template>
