import type { StoryObj } from '@storybook/vue3'
import { sidebarFileSystem } from '../../mock/sidebarFileSystem'
import { useCurrentPageFolder } from '../../composables/useCurrentPageFolder'
import MoleculesSidebarFilesNavigation from './MoleculesSidebarFilesNavigation.vue'

const meta = {
  title: 'Molecules/SidebarFilesNavigation',
  component: MoleculesSidebarFilesNavigation,
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=6089-51568&p=f&t=275rdl14QlGleARq-0',
      },
    ],
  },
  argTypes: {
    parentIds: { control: 'object', description: 'Parent folder path (for recursion)' },
    folders: { control: 'object', description: 'Folder tree structure' },
  },
  args: sidebarFileSystem,
}
export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => ({
    components: { MoleculesSidebarFilesNavigation },
    setup() {
      const { currentFolder } = useCurrentPageFolder()
      return { args, currentFolder }
    },
    template: `
      <div class="flex gap-10">
        <div class="w-[400px]">
          <MoleculesSidebarFilesNavigation v-bind="args" />
        </div>
        <div class="h-[200px] w-[400px] bg-brand p-4 flex flex-col justify-center items-center gap-2">
          <div class="text-xs bg-white/80 rounded p-2 mt-2">
            <strong>Current open tree:</strong>
            <pre>{{ JSON.stringify(currentFolder, null, 2) }}</pre>
          </div>
        </div>
      </div>
    `,
  }),
}
