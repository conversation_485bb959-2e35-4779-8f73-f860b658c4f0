<script setup lang="ts">
import {
  DialogContent,
  DialogPortal,
  DialogRoot,
  DialogTrigger,
} from 'reka-ui'

const emit = defineEmits<{
  (e: 'isOpen', value: boolean): void
}>()

const isOpen = ref(false)
const { setIsLocked } = useLockScroll()

watchEffect(() => {
  if (isOpen.value) {
    setIsLocked(true)
    emit('isOpen', true)
  }
  else {
    setIsLocked(false)
    emit('isOpen', false)
  }

  // Reset pointer-events, as the dialog appends the 'pointer-events: none' style to the body on open, but in this case we want to allow the events on the header
  if (document && document.body) {
    setTimeout(() => {
      document.body.style.pointerEvents = 'auto'
    }, 1000)
  }
})
</script>

<template>
  <DialogRoot :open="isOpen" @update:open="isOpen = $event">
    <DialogTrigger as-child>
      <slot name="button" />
    </DialogTrigger>

    <DialogPortal>
      <Transition name="slide-up">
        <DialogContent
          v-if="isOpen"
          trap-focus
          class="fixed inset-x-0 bottom-0 top-0 z-50 bg-white flex flex-col lg:hidden h-screen"
        >
          <div class="shrink flex justify-end px-padding-md pt-padding-md pb-padding-sm">
            <DialogClose class="h-8">
              <AtomsIcon name="NavCloseIcon" class="text-accent inline-block !w-8 !h-8 cursor-pointer" />
            </DialogClose>
          </div>

          <slot />

          <div class="fixed bg-white lg:static bottom-0 left-0 flex flex-col gap-md w-full p-padding-md lg:p-0 shadow-[0_-15px_20px_0_rgba(0,0,0,0.05)] lg:shadow-none">
            <DialogClose as-child>
              <slot name="closeButton" />
            </DialogClose>
          </div>
        </DialogContent>
      </Transition>
    </DialogPortal>
  </DialogRoot>
</template>

<style scoped>
.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.3s cubic-bezier(0.19, 1, 0.22, 1);
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateX(-100%);
}
</style>
