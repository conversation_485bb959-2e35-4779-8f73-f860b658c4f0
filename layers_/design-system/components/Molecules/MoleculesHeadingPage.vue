<script setup lang='ts'>
import type { MoleculesHeadingPageProps } from './MoleculesHeadingPage.props'

defineProps<MoleculesHeadingPageProps>()
</script>

<template>
  <div class="space-y-padding-xxs">
    <AtomsBreadcrumbs v-if="breadcrumbs?.length" :items="breadcrumbs" />
    <h1 v-if="title" class="headline-1">
      {{ title }}
    </h1>
    <div v-if="description" class="body-1 [&>p>a]:underline">
      <UtilsRichTextContent :content="description" />
    </div>
  </div>
</template>
