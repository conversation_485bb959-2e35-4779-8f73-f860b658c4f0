import type { StoryObj } from '@storybook/vue3'
import { h, ref } from 'vue'
import type { ColumnDef } from '@tanstack/vue-table'
import AtomsTag from '../../Atoms/AtomsTag.vue'
import AtomsLink from '../../Atoms/Link/AtomsLink.vue'
import AtomsIcon from '../../Atoms/AtomsIcon.vue'
import MoleculesTable from './MoleculesTable.vue'

type TableItem = Record<string, unknown> & { id: string | number }

interface User extends TableItem {
  name: string
  age: number
  email: string
  status: 'active' | 'inactive'
}

const meta = {
  title: 'Molecules/Table',
  component: MoleculesTable,
  tags: ['autodocs'],
  argTypes: {
    fixedHeader: {
      control: 'boolean',
      description: 'Whether the header should be fixed while scrolling',
    },
    fixedFirstColumn: {
      control: 'boolean',
      description: 'Whether the first column should be fixed while scrolling horizontally',
    },
    loading: {
      control: 'boolean',
      description: 'Whether the table is in a loading state',
    },
    class: {
      control: 'text',
      description: 'Additional CSS classes to apply to the table',
    },
  },
  args: {
    fixedHeader: true,
    fixedFirstColumn: true,
    loading: false,
  },
}

export default meta
type Story = StoryObj<typeof MoleculesTable<User>>

// Sample data for all stories
const sampleColumns: ColumnDef<User>[] = [
  {
    id: 'name',
    accessorFn: row => (row as User).name,
    header: 'Name',
    meta: { width: '200px' },
    cell: props => h('div', {
      class: 'text-nowrap',
    }, (props.row.original as User).name),
  },
  {
    id: 'age',
    accessorFn: row => (row as User).age,
    header: 'Age',
    meta: { width: '100px' },
  },
  {
    id: 'email',
    accessorFn: row => (row as User).email,
    header: 'Email',
    meta: { width: '250px' },
  },
  {
    id: 'status',
    accessorFn: row => (row as User).status,
    header: 'Status',
    cell: props => h(AtomsTag, {
      label: String((props.row.original as User).status),
    }),
  },
]

const sampleItems: User[] = [
  { id: '1', name: 'John Doe', age: 30, email: '<EMAIL>', status: 'active' },
  { id: '2', name: 'Jane Smith', age: 25, email: '<EMAIL>', status: 'inactive' },
  { id: '3', name: 'Alice Johnson', age: 28, email: '<EMAIL>', status: 'active' },
  { id: '4', name: 'Bob Wilson', age: 35, email: '<EMAIL>', status: 'inactive' },
  { id: '5', name: 'Carol Brown', age: 32, email: '<EMAIL>', status: 'active' },
]

// Base story with default configuration
export const Default: Story = {
  args: {
    columns: sampleColumns,
    items: sampleItems,
  },
}

// Story with loading state
export const Loading: Story = {
  args: {
    columns: sampleColumns,
    items: sampleItems,
    loading: true,
  },
}

// Story with scrollable content
export const Scrollable: Story = {
  args: {
    columns: sampleColumns,
    items: Array(20).fill(null).map((_, index) => ({
      id: String(index + 1),
      name: `User ${index + 1}`,
      age: 20 + Math.floor(Math.random() * 40),
      email: `user${index + 1}@example.com`,
      status: index % 2 === 0 ? 'active' : 'inactive',
    })),
  },
  render: args => ({
    components: { MoleculesTable },
    setup() {
      return { args }
    },
    template: '<div style="height: 400px;"><MoleculesTable v-bind="args" /></div>',
  }),
}

// Story with many columns (horizontal scroll)
export const ManyColumns: Story = {
  args: {
    columns: [
      ...sampleColumns,
      ...sampleColumns.map(col => ({
        ...col,
        id: `${col.id}_2`,
        header: `${col.header} 2`,
      })),
      ...sampleColumns.map(col => ({
        ...col,
        id: `${col.id}_3`,
        header: `${col.header} 3`,
      })),
    ],
    items: sampleItems.map(item => ({
      ...item,
      ...Object.entries(item).reduce((acc, [key, value]) => ({
        ...acc,
        [`${key}_2`]: value,
        [`${key}_3`]: value,
      }), {} as Record<string, unknown>),
    })),
  },
}

// Story with custom cell rendering
export const CustomCells: Story = {
  args: {
    columns: [
      ...sampleColumns.slice(0, -1),
      {
        id: 'actions',
        accessorFn: row => (row as User).status,
        header: 'Actions',
        cell: props => h(AtomsLink, {
          onClick: () => alert(`View details for ${(props.row.original as User).name}`),
          class: 'text-nowrap flex items-center gap-1',
        }, {
          default: () => [
            h('span', 'View Details'),
            h(AtomsIcon, {
              name: 'NavArrowRight',
              size: '36',
            }),
          ],
        }),
      },
    ],
    items: sampleItems,
  },
}

// Story without fixed header and first column
export const NoFixedElements: Story = {
  args: {
    columns: sampleColumns,
    items: sampleItems,
    fixedHeader: false,
    fixedFirstColumn: false,
  },
}

// Story showcasing all features
export const AllFeatures: Story = {
  args: {
    columns: sampleColumns,
    items: sampleItems,
  },
  render: () => ({
    components: { MoleculesTable },
    setup() {
      const loading = ref(false)
      const items = ref(sampleItems)

      const toggleLoading = () => {
        loading.value = !loading.value
      }

      return {
        columns: sampleColumns,
        items,
        loading,
        toggleLoading,
      }
    },
    template: `
      <div class="space-y-4">
        <button @click="toggleLoading" class="px-4 py-2 bg-blue-500 text-white rounded">
          Toggle Loading
        </button>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <h3 class="font-bold mb-2">Fixed Header & First Column</h3>
            <MoleculesTable
              :columns="columns"
              :items="items"
              :loading="loading"
              class="max-h-[300px]"
            />
          </div>
          <div>
            <h3 class="font-bold mb-2">No Fixed Elements</h3>
            <MoleculesTable
              :columns="columns"
              :items="items"
              :loading="loading"
              :fixed-header="false"
              :fixed-first-column="false"
              class="max-h-[300px]"
            />
          </div>
        </div>
      </div>
    `,
  }),
}
