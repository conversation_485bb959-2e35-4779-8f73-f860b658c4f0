<!-- eslint-disable @stylistic/indent -->
<script setup lang="ts" generic="T extends { id: string | number }">
import {
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useVueTable,
} from '@tanstack/vue-table'
import AtomsIcon from '@design-system/components/Atoms/AtomsIcon.vue'
import AtomsButton from '../../Atoms/AtomsButton.vue'
import AtomsSeeMoreLink from '../../Atoms/AtomsSeeMoreLink.vue'
import type { TableProps } from './MoleculesTable.props'

interface TableColumnMeta {
  width?: string
}

const props = withDefaults(defineProps<TableProps<T>>(), {
  fixedHeader: true,
  fixedFirstColumn: true,
  loading: false,
  paginatedTable: false,
})

const table = tv({
  slots: {
    base: 'relative overflow-auto border border-neutral **:border-neutral rounded-sm snap-both snap-mandatory isolate flex',
    wrapper: 'inline-block min-w-full m-0',
    container: 'w-full border-separate border-spacing-0',
    header: 'bg-invert text-invert',
    headerCell: 'px-padding-md py-padding-xs text-left border-r border-b last:border-r-0 snap-start',
    row: 'last:*:border-b-0 *:bg-white even:*:bg-light',
    cell: 'border-r border-b last:border-r-0 px-padding-md py-padding-xs snap-start',
    loadingRow: 'animate-pulse',
    loadingCell: 'text-center py-4',
    navigation: 'flex gap-2',
    navigationLeft: 'left-2 z-30',
    navigationRight: 'right-2 z-30',
    emptyRow: 'text-center py-4',
  },
  variants: {
    fixedHeader: {
      true: {
        header: 'sticky top-0 z-20',
      },
    },
    fixedFirstColumn: {
      true: {},
    },
    scrollable: {
      x: {
        base: 'overflow-x-auto',
      },
      y: {
        base: 'overflow-y-auto',
      },
      both: {
        base: 'overflow-auto',
      },
    },
  },
  compoundVariants: [
    {
      fixedFirstColumn: true,
      class: {
        cell: 'first:sticky first:left-0 first:z-10',
        headerCell: 'first:sticky first:left-0 first:z-20 first:bg-invert first:text-invert',
      },
    },
  ],
})

const tableRef = ref<HTMLTableElement>()
const containerRef = ref<HTMLDivElement>()
const tableInstance = useVueTable({
  get data() {
    return props.items || []
  },
  get columns() {
    return props.columns
  },
  getCoreRowModel: getCoreRowModel(),
  getSortedRowModel: getSortedRowModel(),
  getPaginationRowModel: getPaginationRowModel(),
  enableSortingRemoval: false, // Disable removal of sorting (only toggle between asc/desc)
  initialState: {
    sorting: props.initialSorting || [],
    pagination: {
      pageIndex: 0,
      pageSize: props.paginatedTable ? 10 : (props.items?.length || 1000), // Show all items if not paginated
    },
  },
})

// Bridge between TanStack Table pagination and AtomsSeeMoreLink
const nItemsToShow = computed({
  get: () => tableInstance.getState().pagination.pageSize,
  set: (value: number) => {
    if (props.paginatedTable) {
      tableInstance.setPageSize(value)
    }
  },
})

const tableClasses = computed(() => {
  return table({
    fixedHeader: props.fixedHeader,
    fixedFirstColumn: props.fixedFirstColumn,
    scrollable:
      !props.fixedHeader && !props.fixedFirstColumn
        ? 'both'
        : !props.fixedHeader
          ? 'y'
          : !props.fixedFirstColumn
            ? 'x'
            : undefined,
  })
})

const scrollToNextColumn = () => {
  if (!tableRef.value) return

  const nextScrollPosition = getNextScrollPosition('right')

  tableRef.value.scrollTo({
    left: nextScrollPosition ?? 0,
    behavior: 'smooth',
  })
}

const scrollToPrevColumn = () => {
  if (!tableRef.value) return

  const nextScrollPosition = getNextScrollPosition('left')

  tableRef.value.scrollTo({
    left: nextScrollPosition ?? 0,
    behavior: 'smooth',
  })
}

const getNextScrollPosition = (direction: 'left' | 'right'): number => {
  const row: HTMLTableRowElement | undefined = tableRef.value?.querySelectorAll('tr')?.item(0)
  const cells: HTMLTableCellElement[] = Array.from(row?.children || []) as HTMLTableCellElement[]
  if (!cells?.length) return 0

  let accumulatedWidth = 0
  const currentPosition = cells[0]?.offsetLeft ?? 0
  let nextScrollPosition = 0

  for (let i = 1; i < cells.length; i++) {
    const cellWidth = cells[i]?.offsetWidth || 0
    accumulatedWidth += cellWidth

    if (currentPosition < accumulatedWidth) {
      const previousCellWidth = cells[i - 1]?.offsetWidth || 0
      nextScrollPosition = direction === 'left' ? accumulatedWidth - cellWidth - previousCellWidth : accumulatedWidth
      break
    }
  }

  return nextScrollPosition ?? 0
}

const getFirstColumnWidth = () => {
  const row: HTMLTableRowElement | undefined = tableRef.value?.querySelectorAll('tr')?.item(0)
  const cells: HTMLTableCellElement[] = Array.from(row?.children || []) as HTMLTableCellElement[]
  if (!cells?.length) return 0
  return cells[0]?.offsetWidth || 0
}

const getFirstRowHeight = () => {
  const row: HTMLTableRowElement | undefined = tableRef.value?.querySelectorAll('tr')?.item(0)
  if (!row) return 0
  return row.offsetHeight || 0
}

const firstColumnWidth = ref(0)
const firstRowHeight = ref(0)

const updateDimensions = () => {
  firstColumnWidth.value = getFirstColumnWidth()
  firstRowHeight.value = getFirstRowHeight()
}

const updateScrollableState = () => {
  if (!containerRef.value || !tableRef.value) return false
  return containerRef.value.scrollWidth > tableRef.value.clientWidth
}

const isScrollable = ref(false)

onMounted(() => {
  isScrollable.value = updateScrollableState()
  updateDimensions()

  const resizeObserver = new ResizeObserver(() => {
    isScrollable.value = updateScrollableState()
    updateDimensions()
  })

  if (tableRef.value) {
    resizeObserver.observe(tableRef.value)
  }

  onBeforeUnmount(() => {
    resizeObserver.disconnect()
  })
})
</script>

<template>
  <div>
    <div>
      <div :class="[{ 'flex justify-between items-center': $slots.header }]">
        <slot name="header" />
        <div v-if="isScrollable" class="flex gap-2 justify-end pb-padding-xl">
          <div :class="[tableClasses.navigation(), tableClasses.navigationLeft()]">
            <AtomsButton
              size="icon"
              anatomy="tertiary"
              @click="scrollToPrevColumn"
            >
              <AtomsIcon name="NavArrowLeft" size="36" />
            </AtomsButton>
          </div>
          <div :class="[tableClasses.navigation(), tableClasses.navigationRight()]">
            <AtomsButton
              size="icon"
              anatomy="tertiary"
              @click="scrollToNextColumn"
            >
              <AtomsIcon name="NavArrowRight" size="36" />
            </AtomsButton>
          </div>
        </div>
      </div>
      <div
        ref="tableRef"
        :class="tableClasses.base({ class: props.class })"
        :style="{ scrollPaddingLeft: `${firstColumnWidth}px`, scrollPaddingTop: `${firstRowHeight}px` }"
      >
        <div
          ref="containerRef"
          :class="tableClasses.wrapper()"
        >
          <table :class="tableClasses.container()">
            <thead :class="tableClasses.header()">
              <tr>
                <th
                  v-for="header in tableInstance.getFlatHeaders()"
                  :key="header.id"
                  :class="tableClasses.headerCell()"
                  :style="{ width: (header.column.columnDef.meta as TableColumnMeta | undefined)?.width }"
                >
                  <component
                    :is="header.column.columnDef.header"
                    v-if="typeof header.column.columnDef.header === 'function' || typeof header.column.columnDef.header === 'object'"
                    v-bind="header.getContext()"
                  />
                  <template v-else>
                    {{ header.column.columnDef.header }}
                  </template>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-if="loading"
                :class="tableClasses.loadingRow()"
              >
                <td
                  :colspan="tableInstance.getAllColumns().length"
                  :class="tableClasses.loadingCell()"
                >
                  Loading...
                </td>
              </tr>
              <tr v-else-if="items.length === 0">
                <td :colspan="tableInstance.getAllColumns().length" :class="tableClasses.emptyRow()">
                  {{ $ts('table.noData') }}
                </td>
              </tr>
              <template v-else>
                <tr
                  v-for="row in tableInstance.getRowModel().rows"
                  :key="row.id"
                  :class="tableClasses.row()"
                >
                  <td
                    v-for="cell in row.getVisibleCells()"
                    :key="cell.id"
                    :class="tableClasses.cell()"
                  >
                    <component
                      :is="cell.column.columnDef.cell"
                      v-if="typeof cell.column.columnDef.cell === 'function' || typeof cell.column.columnDef.cell === 'object'"
                      v-bind="cell.getContext()"
                    />
                    <template v-else>
                      {{ cell.column.columnDef.cell }}
                    </template>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <AtomsSeeMoreLink
      v-if="paginatedTable"
      v-model:n-items-to-show="nItemsToShow"
      class="mt-padding-xl"
      :total-data="items?.length"
    />
  </div>
</template>
