<script lang="ts" setup>
import { iconMap } from '../Atoms/AtomsFileButton.props'
import type { MoleculesButtonAssetProps } from './MoleculesButtonAsset.props'

const props = withDefaults(defineProps<MoleculesButtonAssetProps>(), {
  fileType: 'pdf',
  fileDate: new Date().toISOString(),
})

const iconName = computed(() => iconMap[props.fileType] ?? iconMap['pdf'])
</script>

<template>
  <div>
    <p class="body-2 font-bold">
      {{ title }}
    </p>
    <a class="flex gap-2 text-accent cursor-pointer" :href="props.fileUrl" target="_blank">
      <AtomsIcon :name="iconName" class="text-[52px] shrink-0" />
      <div class="flex flex-col">
        <p class="subtitle-2">
          {{ fileName }}
        </p>
        <p class="body-2">
          {{ formattedDate(fileDate) }}
        </p>
      </div>
    </a>
  </div>
</template>
