<script setup lang="ts">
import type { MoleculesSwitchCodeProps } from '@design-system/components/Molecules/MoleculesSwitchCode.props'

const props = withDefaults(defineProps<MoleculesSwitchCodeProps>(), {
  variant: 'header',
})

const model = defineModel({ type: Number })

const duplicateOptions = computed(() => props.options.map(el => ({ label: el.label, value: el.value })))
</script>

<template>
  <div class="flex gap-2 items-center w-full">
    <span v-if="variant === 'header'" class="body-2 text-nowrap pr-padding-xs lg:pr-0">
      {{ $ts('molecules.switchCode.label') }}
    </span>
    <div class="grow">
      <FormKit
        :id="id"
        v-model="model"
        type="dropdown"
        name="switch-code"
        :options="duplicateOptions"
        :deselect="false"
        select-icon="NavChevronDown"
        selected-icon="NavCheckIcon"
        :classes="{
          outer: 'w-full',
          icon: 'h-7 absolute right-0 top-1/2 -translate-y-1/2 overflow-hidden flex items-center',
          selector: ' pl-2 pr-1 inset-ring inset-ring-[var(--color-stroke-neutral)] aria-expanded:inset-ring-[var(--color-stroke-focus)] border-0',
          selection: 'line-clamp-1 [&>.formkit-option]:font-medium',
          option: 'body-2 w-full pr-7 !font-bold data-[checked=false]:!font-normal',
        }"
      />
    </div>
  </div>
</template>
