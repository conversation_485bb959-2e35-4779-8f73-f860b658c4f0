<template>
  <AccordionRoot
    type="single"
    collapsible
    :unmount-on-hide="false"
  >
    <AccordionItem value="filters">
      <AccordionHeader>
        <AccordionTrigger as-child>
          <slot name="title" />
        </AccordionTrigger>
      </AccordionHeader>
      <AccordionContent class="slide">
        <slot />
      </AccordionContent>
    </AccordionItem>
  </AccordionRoot>
</template>
