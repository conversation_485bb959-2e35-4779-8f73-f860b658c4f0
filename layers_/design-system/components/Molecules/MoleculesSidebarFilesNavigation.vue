<script setup lang="ts">
import type { MoleculesSidebarFilesNavigationProps, Folder } from './MoleculesSidebarFilesNavigation.props'

const props = withDefaults(defineProps<MoleculesSidebarFilesNavigationProps>(), {
  parentIds: () => [],
})

const emit = defineEmits<{
  'update:currentFolder': [currentFolder: string | null]
}>()

const { currentFolder, isFolderOpen, toggleFolder } = useCurrentPageFolder()

const handleClick = (folderId: string) => {
  toggleFolder(folderId, props.parentIds)
  emit('update:currentFolder', currentFolder.value.at(-1) ?? null)
}

const hasChildren = (
  folder: Folder,
): folder is Folder & { folders: Folder[] } => {
  return Array.isArray(folder.folders) && folder.folders.length > 0
}
</script>

<template>
  <ul role="tree">
    <li
      v-for="folder in folders"
      :key="folder.id"
      role="treeitem"
    >
      <AtomsSidebarButton
        :title="folder.title"
        :is-expanded="isFolderOpen(folder.id, props.parentIds)"
        :has-children="hasChildren(folder)"
        :aria-controls="'folder-' + folder.id"
        @click="handleClick(folder.id)"
      />
      <MoleculesSidebarFilesNavigation
        v-if="isFolderOpen(folder.id, props.parentIds) && hasChildren(folder)"
        :id="'folder-' + folder.id"
        :folders="folder.folders"
        :parent-ids="[...parentIds, folder.id]"
        class="pl-padding-md"
        :aria-hidden="!isFolderOpen(folder.id, props.parentIds)"
        @update:current-folder="emit('update:currentFolder', $event)"
      />
    </li>
  </ul>
</template>
