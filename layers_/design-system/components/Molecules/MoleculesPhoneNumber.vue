<script setup lang='ts'>
import type { FormKitNode } from '@formkit/core'
import type { MoleculesPhoneNumberProps, PhoneNumber } from './MoleculesPhoneNumber.props'

const props = withDefaults(defineProps<MoleculesPhoneNumberProps>(), {
  name: 'phoneNumber',
})

const groupProps = computed(() => ({
  name: props.name,
  disabled: props.disabled,
  validation: props.validation,
  validationRules: props.validationRules,
  validationVisibility: props.validationVisibility,
  errors: props.errors,
}))

const prefixProps = computed(() => ({
  label: props.prefix.label,
  placeholder: props.prefix.placeholder,
  disabled: props.disabled,
  validation: props.validation,
  validationVisibility: props.validationVisibility,
}))

const numberProps = computed(() => ({
  label: props.number.label,
  placeholder: props.number.placeholder,
  disabled: props.disabled,
  validation: props.validation,
  validationVisibility: props.validationVisibility,
  showValidationIcon: props.showValidationIcon,
}))

const phoneNumber = defineModel<PhoneNumber>()

function requiredGroupValidation(node: FormKitNode) {
  // node children loose reactivity
  // ref: https://discord.com/channels/793529058377072650/1200388807674052668/1200460808308670544
  const children = node.children.map((child) => {
    const observedChild = node.at(['$root', ...node.address.slice(1), child.name])
    return observedChild?.value
  })
  return children.every(val => !!val)
}

const sanitizePrefix = (value: string) => value.replace(/[^0-9]/g, '').slice(0, 3)
const sanitizeNumber = (value: string) => {
  const digits = value.replace(/\D/g, '')

  const parts = []
  if (digits.length > 0) parts.push(digits.slice(0, 3))
  if (digits.length > 3) parts.push(digits.slice(3, 6))
  if (digits.length > 6) parts.push(digits.slice(6, 10))

  return parts.join('-')
}

watch(phoneNumber, (newValue) => {
  if (phoneNumber.value?.prefix && newValue?.prefix)
    phoneNumber.value.prefix = sanitizePrefix(newValue.prefix)

  if (phoneNumber.value?.number && newValue?.number)
    phoneNumber.value.number = sanitizeNumber(newValue.number)
})
</script>

<template>
  <FormKit
    v-bind="groupProps"
    :id="id"
    v-model="phoneNumber"
    type="group"
    :validation-rules="{ required: requiredGroupValidation }"
    messages-class="space-y-2 group-data-[disabled]:hidden"
    message-class="text-alert caption-1"
    :validation-messages="{
      required: $ts('molecules.phoneNumber.required'),
      invalid: $ts('molecules.phoneNumber.invalid'),
    }"
  >
    <div>
      <div class="flex gap-x-2 items-end">
        <FormKit
          v-bind="prefixProps"
          :id="`${id}-prefix`"
          type="text"
          autocomplete="tel-country-code"
          name="prefix"
          validation="number|length:1,3"
          outer-class="max-w-10"
          input-class="w-full"
          label-class="no-asterisk"
          messages-class="hidden"
          help-class="hidden"
        >
          <template #prefix>
            <p class="group-data-[empty]:text-placeholder">
              +
            </p>
          </template>
        </FormKit>
        <FormKit
          v-bind="numberProps"
          :id="`${id}-number`"
          name="number"
          type="tel"
          validation="required|matches:/^[0-9]{3}-[0-9]{3}-[0-9]{4}$/"
          aria-required="true"
          autocomplete="tel-national"
          messages-class="hidden"
          help-class="hidden"
          :label-class="disabled ? 'no-asterisk' : ''"
        />
      </div>
      <p :class="['text-info body-2', disabled ?'hidden' : '']">
        {{ help }}
      </p>
      <FormKitMessages />
    </div>
  </FormKit>
</template>
