<script setup lang="ts">
import type { MoleculesPaginationProps } from './MoleculesPagination.props'

withDefaults(defineProps<MoleculesPaginationProps>(), {
  showEdges: false,
  size: 'S',
  defaultPage: 1,
  total: 0,
  itemsPerPage: 10,
})

const emit = defineEmits<{
  (e: 'clickOnPage', pageNumber: number): void
}>()

const pageNumber = defineModel<number>({
  set(v) {
    emit('clickOnPage', v)
    return v
  },
})
</script>

<template>
  <PaginationRoot
    v-model:page="pageNumber"
    :total="total"
    :sibling-count="size === 'S' ? 1.5 : 4.5"
    :items-per-page="itemsPerPage"
    :show-edges="showEdges"
    :default-page="defaultPage"
    class="w-full"
  >
    <PaginationList
      v-slot="{ items }"
      class="flex items-center justify-between py-padding-sm px-padding-xs"
    >
      <PaginationPrev class="w-8 h-8 flex items-center justify-center bg-brand text-icon-accent disabled:bg-disabled disabled:text-icon-disabled rounded-full mr-4">
        <AtomsIcon
          name="NavArrowLeft"
          aria-label="Navigate to previous page"
        />
      </PaginationPrev>
      <template v-for="(page, index) in items">
        <PaginationListItem
          v-if="page.type === 'page'"
          :key="index"
          class="link-1 underline text-accent data-[selected]:body-2 data-[selected]:text-neutral data-[selected]:text-bold data-[selected]:no-underline "
          :value="Number(page.value.toFixed(0))"
        >
          {{ page.value.toFixed(0) }}
        </PaginationListItem>
        <PaginationEllipsis
          v-else
          :key="page.type"
          :index="index"
          class="w-9 h-9 flex items-center justify-center text-accent"
        >
          &#8230;
        </PaginationEllipsis>
      </template>
      <PaginationNext class="w-8 h-8 flex items-center justify-center bg-brand text-icon-accent disabled:bg-disabled disabled:text-icon-disabled rounded-full ml-4">
        <AtomsIcon
          name="NavArrowRight"
          aria-label="Navigate to next page"
        />
      </PaginationNext>
    </PaginationList>
  </PaginationRoot>
</template>
