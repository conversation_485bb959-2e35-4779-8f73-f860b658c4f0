<script lang="ts" setup>
import type { MoleculesDialogProps } from './MoleculesDialog.props'

withDefaults(defineProps<MoleculesDialogProps>(), {
  withCloseButton: false,
})
const open = defineModel<boolean>()

const styles = tv({
  slots: {
    content: 'fixed z-40 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-neutral flex flex-col gap-xxl rounded-sm',
    title: 'body-1 font-semibold',
    description: 'body-2',
    overlay: 'fixed inset-0 bg-black/60 z-40',
    closeButton: 'flex justify-end p-4',
    innerContent: 'flex flex-col gap-sm p-padding-md',
    actions: 'p-padding-md',
  },
})

const classes = computed(() => styles())
</script>

<template>
  <DialogRoot v-model:open="open">
    <DialogPortal>
      <DialogOverlay :class="classes.overlay({ class: overlayClass })" />
      <Transition name="pop-up">
        <DialogContent :class="classes.content({ class: contentClass })">
          <div v-if="withCloseButton" :class="classes.closeButton({ class: closeButtonClass })">
            <DialogClose as-child>
              <AtomsButton
                anatomy="tertiary"
                size="icon"
                aria-label="Close modal"
                @click="open = false"
              >
                <template #icon>
                  <AtomsIcon name="NavCloseIcon" size="32" />
                </template>
              </AtomsButton>
            </DialogClose>
          </div>
          <div :class="classes.innerContent({ class: innerContentClass })">
            <div class="flex flex-col gap-xxs">
              <AtomsIcon
                v-if="icon"
                :name="icon"
                :class="iconClass"
                class="text-5xl"
              />
              <DialogTitle v-if="title" :class="classes.title({ class: titleClass })">
                {{ title }}
              </DialogTitle>
              <DialogDescription v-if="message" :class="classes.description({ class: descriptionClass })">
                {{ message }}
              </DialogDescription>
            </div>
            <slot />
          </div>
          <div v-if="$slots.actions" :class="classes.actions()">
            <slot name="actions" />
          </div>
        </DialogContent>
      </Transition>
    </DialogPortal>
  </DialogRoot>
</template>

<style scoped>
.pop-up-enter-active, .pop-up-leave-active {
  transition: all 0.2s ease;
}
.pop-up-enter-from, .pop-up-leave-to {
  opacity: 0;
  transform: scale(0.95);
}
</style>
