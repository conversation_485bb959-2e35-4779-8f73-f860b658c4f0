<script setup lang="ts">
import type { MoleculesTooltipProps } from './MoleculesTooltip.props'

withDefaults(defineProps<MoleculesTooltipProps>(), {
  avoidCollisions: true,
  align: 'start',
  defaultOpen: false,
  collisionPadding: 16,
})
</script>

<template>
  <TooltipProvider :delay-duration="300">
    <TooltipRoot :default-open="defaultOpen">
      <TooltipTrigger>
        <AtomsIcon
          name="GenInformationIcon"
          class="text-modernfold-blue-600 !w-6 !h-6"
          aria-label="Information icon"
        />
      </TooltipTrigger>
      <TooltipPortal>
        <TooltipContent
          class=" w-[200px] slide-down-and-fade slide-left-and-fade slide-right-and-fade slide-up-and-fade body-2 text-neutral select-none rounded border border-focus bg-white p-padding-sm will-change-[transform,opacity] break-all overflow-hidden shadow-100"
          :side="side"
          :side-offset="8"
          :align="align"
          :avoid-collisions="avoidCollisions"
          :collision-boundary="collisionBoundary"
          :collision-padding="collisionPadding"
        >
          {{ content }}
        </TooltipContent>
      </TooltipPortal>
    </TooltipRoot>
  </TooltipProvider>
</template>

<style scoped>
@keyframes slideDownAndFade {
  from {
    opacity: 0;
    transform: translateY(-2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeftAndFade {
  from {
    opacity: 0;
    transform: translateX(2px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideUpAndFade {
  from {
    opacity: 0;
    transform: translateY(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideRightAndFade {
  from {
    opacity: 0;
    transform: translateX(-2px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-down-and-fade[data-state="delayed-open"][data-side="top"] {
  animation: slideDownAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1);
}

.slide-left-and-fade[data-state="delayed-open"][data-side="right"] {
  animation: slideLeftAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1);
}

.slide-up-and-fade[data-state="delayed-open"][data-side="bottom"] {
  animation: slideUpAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1);
}

.slide-right-and-fade[data-state="delayed-open"][data-side="left"] {
  animation: slideRightAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1);
}
</style>
