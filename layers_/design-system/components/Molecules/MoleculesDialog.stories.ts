import type { StoryObj } from '@storybook/vue3'
import { ref } from 'vue' // For controlling dialog state in render functions
import AtomsButton from '../Atoms/AtomsButton.vue' // For action buttons and trigger examples
import OrganismsMessage from '../Organisms/OrganismsMessage.vue' // Added for the default slot example
import MoleculesDialog from './MoleculesDialog.vue'

const meta = {
  title: 'Molecules/Dialog',
  component: MoleculesDialog,
  // tags: ['autodocs'], // Consider adding autodocs if appropriate
  parameters: {
    docs: {
      description: {
        component: 'A foundational, accessible dialog component based on headless UI principles, similar to those found in Radix UI. It provides a base for building custom dialog experiences with proper focus management, ARIA attributes, and portal support. The `v-model` controls its visibility.',
      },
    },
  },
  argTypes: {
    modelValue: {
      control: 'boolean',
      description: 'Controls the visibility of the dialog (v-model). Essential for programmatically opening and closing the dialog.',
      table: { category: 'Props' },
    },
    title: {
      control: 'text',
      description: 'The main title text displayed in the dialog. Corresponds to an internal DialogTitle.',
      table: { category: 'Props' },
    },
    message: {
      control: 'text',
      description: 'The descriptive message or content body of the dialog. Corresponds to an internal DialogDescription.',
      table: { category: 'Props' },
    },
    icon: {
      control: 'text',
      description: 'Optional icon name to be displayed near the title or message.',
      table: { category: 'Props' },
    },
    iconClass: {
      control: 'text',
      description: 'CSS classes to apply to the icon for styling (e.g., color, size).',
      table: { category: 'Props' },
    },
    contentClass: {
      control: 'text',
      description: 'CSS classes to apply to the main dialog content area for custom styling.',
      table: { category: 'Props' },
    },
    // Slots
    default: {
      description: 'The main content area of the dialog. Use this slot to inject custom Vue components or HTML structure.',
      table: { category: 'Slots' },
    },
    actions: {
      description: 'A dedicated slot for action buttons (e.g., Confirm, Cancel, Close). Typically contains `AtomsButton` components.',
      table: { category: 'Slots' },
    },
  },
  args: { // Default args for controls
    modelValue: false,
    title: 'Dialog Title',
    message: 'This is the main message content of the dialog. It can be a few sentences long.',
    icon: '',
    iconClass: '',
    contentClass: 'min-w-96', // Default minimum width
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    title: 'Standard Dialog',
    message: 'This is a standard dialog with a title and a message.',
  },
  render: args => ({
    components: { MoleculesDialog, AtomsButton },
    setup() {
      const isOpen = ref(args.modelValue) // Manage open state locally for the story
      const openDialog = () => isOpen.value = true
      // Storybook's updateArgs can be used if direct control via args is preferred
      // const openDialog = () => updateArgs({ modelValue: true });

      return { args, isOpen, openDialog }
    },
    template: `
      <div>
        <AtomsButton @click="openDialog">Open Default Dialog</AtomsButton>
        <MoleculesDialog v-model="isOpen" :title="args.title" :message="args.message" :icon="args.icon" :icon-class="args.iconClass" :content-class="args.contentClass">
          <!-- Default slot content can go here if needed -->
        </MoleculesDialog>
      </div>
    `,
  }),
}

export const InitiallyOpen: Story = {
  args: {
    modelValue: true,
    title: 'Initially Open Dialog',
    message: 'This dialog is open by default when the story loads.',
    icon: 'NavCheckIcon',
    iconClass: 'text-success',
  },
}

export const WithIcon: Story = {
  args: {
    title: 'Dialog With Icon',
    message: 'This dialog features an icon next to its title/message.',
    icon: 'GenAttentionIcon',
    iconClass: 'text-warning-emphasis', // Example icon class
  },
  render: args => ({
    components: { MoleculesDialog, AtomsButton },
    setup() {
      const isOpen = ref(args.modelValue)
      const openDialog = () => isOpen.value = true
      return { args, isOpen, openDialog }
    },
    template: `
      <div>
        <AtomsButton @click="openDialog">Open Dialog with Icon</AtomsButton>
        <MoleculesDialog v-model="isOpen" :title="args.title" :message="args.message" :icon="args.icon" :icon-class="args.iconClass" :content-class="args.contentClass" />
      </div>
    `,
  }),
}

export const WithActionsSlot: Story = {
  args: {
    title: 'Dialog Requiring Action',
    message: 'Please choose an action below.',
  },
  render: args => ({
    components: { MoleculesDialog, AtomsButton },
    setup() {
      const isOpen = ref(args.modelValue)
      const openDialog = () => isOpen.value = true
      const closeDialog = () => isOpen.value = false
      return { args, isOpen, openDialog, closeDialog }
    },
    template: `
      <div>
        <AtomsButton @click="openDialog">Open Dialog with Actions</AtomsButton>
        <MoleculesDialog v-model="isOpen" :title="args.title" :message="args.message" :icon="args.icon" :icon-class="args.iconClass" :content-class="args.contentClass">
          <template #actions>
            <div class="flex gap-sm w-full">
              <AtomsButton anatomy="secondary" @click="closeDialog" class="flex-1">Cancel</AtomsButton>
              <AtomsButton anatomy="primary" @click="closeDialog" class="flex-1">Confirm</AtomsButton>
            </div>
          </template>
        </MoleculesDialog>
      </div>
    `,
  }),
}

export const WithDefaultSlot: Story = {
  args: {
    title: 'Dialog with Custom Content & Actions',
    message: '', // Message can be empty if default slot is primary content focus
  },
  render: args => ({
    components: { MoleculesDialog, AtomsButton, OrganismsMessage }, // Added OrganismsMessage
    setup() {
      const isOpen = ref(args.modelValue)
      const openDialog = () => isOpen.value = true
      const closeDialog = () => isOpen.value = false // Added for actions slot
      return { args, isOpen, openDialog, closeDialog }
    },
    template: `
      <div>
        <AtomsButton @click="openDialog">Open Dialog with Custom Content & Actions</AtomsButton>
        <MoleculesDialog v-model="isOpen" :title="args.title" :message="args.message" :icon="args.icon" :icon-class="args.iconClass" :content-class="args.contentClass">
          <OrganismsMessage 
            title="Important Information"
            description="This is an OrganismsMessage component rendered within the default slot of the MoleculesDialog. You can place any complex content here."
            status="info"
            size="l"
          />
          <template #actions>
            <div class="flex gap-sm w-full">
              <AtomsButton anatomy="secondary" @click="closeDialog" class="flex-1">Close</AtomsButton>
              <AtomsButton anatomy="primary" @click="closeDialog" class="flex-1">Proceed</AtomsButton>
            </div>
          </template>
        </MoleculesDialog>
      </div>
    `,
  }),
}
