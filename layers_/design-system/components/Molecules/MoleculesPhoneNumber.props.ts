export type MoleculesPhoneNumberProps = {
  id: string
  prefix: {
    label?: string
    placeholder?: string
  }
  number: {
    label?: string
    placeholder?: string
  }
  name?: string
  disabled?: boolean
  help?: string
  validation?: string
  validationRules?: Record<
    string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (node: FormKitNode, ...args: any[]) => boolean | Promise<boolean>
  >
  validationMessages?: Record<
    string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  string | ((ctx: { node: FormKitNode, name: string, args: any[] }) => string)
  >
  validationVisibility?: 'blur' | 'live' | 'dirty' | 'submit'
  errors?: string[]
  showValidationIcon?: boolean
}

export type PhoneNumber = {
  prefix: string
  number: string
}
