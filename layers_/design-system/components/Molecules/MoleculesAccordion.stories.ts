import type { Meta, StoryObj } from '@storybook/vue3'
import MoleculesAccordion from './MoleculesAccordion.vue'

const meta: Meta<typeof MoleculesAccordion> = {
  title: 'Molecules/Accordion',
  component: MoleculesAccordion,
  tags: ['autodoc'],
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=2152-6918&p=f&m=dev',
      },
    ],
  },
}

export default meta
type Story = StoryObj<typeof MoleculesAccordion>

export const Single: Story = {
  render: args => ({
    components: { MoleculesAccordion },
    setup() {
      return { args }
    },
    template: `
    <div class="p-4">
      <MoleculesAccordion v-bind="args" />
    </div>
    `,
  }),
  args: {
    items: [
      {
        id: 'item-1',
        title: 'Lorem 1',
        content: 'Qui praesentium architecto et nisi provident qui sint eius vel architecto consequatur in atque veritatis. Ex ipsa vitae qui delectus vitae ex distinctio accusantium ut accusantium rerum.',
      },
      {
        id: 'item-2',
        title: 'Lorem 2',
        content: 'Est expedita tempora qui repellat illo ea rerum dolores aut iste omnis ut minus doloribus et fuga officia. In veniam unde et laboriosam expedita ut quia molestias quo aperiam provident! Ea laboriosam voluptatum ad eligendi omnis aut nihil dicta eos omnis magnam et sunt rerum qui suscipit accusamus et consectetur facilis.',
      },
      {
        id: 'item-3',
        title: 'Lorem 3',
        content: 'Est expedita tempora qui repellat illo ea rerum dolores aut iste omnis ut minus doloribus et fuga officia. In veniam unde et laboriosam expedita ut quia molestias quo aperiam provident! Ea laboriosam voluptatum ad eligendi omnis aut nihil dicta eos omnis magnam et sunt rerum qui suscipit accusamus et consectetur facilis.',
      },
    ],
  },
}

export const Multiple: Story = {
  render: args => ({
    components: { MoleculesAccordion },
    setup() {
      return { args }
    },
    template: `
    <div class="p-4">
      <MoleculesAccordion v-bind="args" />
    </div>
    `,
  }),
  args: {
    items: [
      {
        id: 'item-1',
        title: 'Lorem 1',
        content: 'Qui praesentium architecto et nisi provident qui sint eius vel architecto consequatur in atque veritatis. Ex ipsa vitae qui delectus vitae ex distinctio accusantium ut accusantium rerum.',
      },
      {
        id: 'item-2',
        title: 'Lorem 2',
        content: 'Est expedita tempora qui repellat illo ea rerum dolores aut iste omnis ut minus doloribus et fuga officia. In veniam unde et laboriosam expedita ut quia molestias quo aperiam provident! Ea laboriosam voluptatum ad eligendi omnis aut nihil dicta eos omnis magnam et sunt rerum qui suscipit accusamus et consectetur facilis.',
      },
      {
        id: 'item-3',
        title: 'Lorem 3',
        content: 'Est expedita tempora qui repellat illo ea rerum dolores aut iste omnis ut minus doloribus et fuga officia. In veniam unde et laboriosam expedita ut quia molestias quo aperiam provident! Ea laboriosam voluptatum ad eligendi omnis aut nihil dicta eos omnis magnam et sunt rerum qui suscipit accusamus et consectetur facilis.',
      },
    ],
    type: 'multiple',
    defaultValue: ['item-1', 'item-2'],
  },
}

export const Disabled: Story = {
  render: args => ({
    components: { MoleculesAccordion },
    setup() {
      return { args }
    },
    template: `
    <div class="p-4">
      <MoleculesAccordion v-bind="args" />
    </div>
    `,
  }),
  args: {
    items: [
      {
        id: 'item-1',
        title: 'Lorem 1',
        content: 'Qui praesentium architecto et nisi provident qui sint eius vel architecto consequatur in atque veritatis. Ex ipsa vitae qui delectus vitae ex distinctio accusantium ut accusantium rerum.',
      },
      {
        id: 'item-2',
        title: 'Lorem 2',
        content: 'Est expedita tempora qui repellat illo ea rerum dolores aut iste omnis ut minus doloribus et fuga officia. In veniam unde et laboriosam expedita ut quia molestias quo aperiam provident! Ea laboriosam voluptatum ad eligendi omnis aut nihil dicta eos omnis magnam et sunt rerum qui suscipit accusamus et consectetur facilis.',
      },
      {
        id: 'item-3',
        title: 'Lorem 3',
        content: 'Est expedita tempora qui repellat illo ea rerum dolores aut iste omnis ut minus doloribus et fuga officia. In veniam unde et laboriosam expedita ut quia molestias quo aperiam provident! Ea laboriosam voluptatum ad eligendi omnis aut nihil dicta eos omnis magnam et sunt rerum qui suscipit accusamus et consectetur facilis.',
      },
    ],
    type: 'multiple',
    defaultValue: ['item-1'],
    disabled: true,
  },
}
