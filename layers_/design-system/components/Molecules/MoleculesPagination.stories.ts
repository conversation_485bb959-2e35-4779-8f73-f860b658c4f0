import type { Meta, StoryObj } from '@storybook/vue3'
import MoleculesPagination from './MoleculesPagination.vue'

const meta: Meta<typeof MoleculesPagination> = {
  title: 'Molecules/Pagination',
  component: MoleculesPagination,
  tags: ['autodocs'],
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=2176-2600&t=bN7JMVRCC2vU4Ohf-0',
      },
    ],
  },
  decorators: [() => ({ template: '<div class="p-4"><story/></div>' })],
  argTypes: {
    size: {
      control: 'select',
      options: ['S', 'M'],
      description: 'The size of the pagination component, S = 343px, M = 720px',
    },
    total: {
      control: 'number',
      description: 'The total number of items in your list',
    },
    itemsPerPage: {
      control: 'number',
      description: 'The number of items per page',
    },
    defaultPage: {
      control: 'number',
      description: 'The value of the page that should be active when initially rendered. Use when you do not need to control the value state.',
    },
    showEdges: {
      control: 'boolean',
      description: 'When true, always show first page, last page, and ellipsis',
    },
  },
  args: {
    size: 'M',
    total: 1000,
    itemsPerPage: 10,
    showEdges: false,
  },
}

export default meta
type Story = StoryObj<typeof MoleculesPagination>

export const S: Story = {
  render: args => ({
    components: { MoleculesPagination },
    setup() {
      const currentPage = ref(1)
      return { args, currentPage }
    },
    template: `
      <MoleculesPagination v-model="currentPage" v-bind="args" />
      <p>Current page: {{ currentPage }}</p>
    `,
  }),
  args: {
    size: 'S',
    total: 100,
  },
}

export const M: Story = {
  render: args => ({
    components: { MoleculesPagination },
    setup() {
      const currentPage = ref(1)
      return { args, currentPage }
    },
    template: `
      <MoleculesPagination v-model="currentPage" v-bind="args" />
      <p>Current page: {{ currentPage }}</p>
    `,
  }),
}

export const ShowEdges: Story = {
  render: args => ({
    components: { MoleculesPagination },
    setup() {
      return { args }
    },
    template: `
      <MoleculesPagination v-bind="args" />
    `,
  }),
  args: {
    showEdges: true,
  },
}
