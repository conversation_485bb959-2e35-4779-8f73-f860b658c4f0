<script setup lang="ts">
import type { MoleculesAttributesBoxProps } from './MoleculesAttributesBox.props'

defineProps<MoleculesAttributesBoxProps>()
</script>

<template>
  <div class="container grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 py-padding-md lg:py-padding-xxl">
    <div v-for="attribute in attributes" :key="attribute.label">
      <p class="body-1 font-semibold">
        {{ attribute.label }}
      </p>
      <p class="body-1">
        {{ attribute.value }}
      </p>
    </div>
  </div>
</template>
