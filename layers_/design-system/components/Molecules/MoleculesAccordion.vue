<script setup lang="ts">
import { Accordi<PERSON><PERSON><PERSON><PERSON>, AccordionHeader, Accordion<PERSON>tem, AccordionRoot, AccordionTrigger } from 'reka-ui'
import type { MoleculesAccordionProps } from './MoleculesAccordion.props'

const props = withDefaults(defineProps<MoleculesAccordionProps>(), {
  type: 'multiple',
})
</script>

<template>
  <AccordionRoot
    v-bind="props"
    class="flex flex-col gap-xs"
    :class="{ 'opacity-50': props.disabled }"
  >
    <template
      v-for="item in items"
      :key="item.id"
    >
      <AccordionItem
        class="overflow-hidden border border-neutral bg-neutral"
        :value="item.id"
      >
        <AccordionHeader>
          <AccordionTrigger class="flex gap-xxs cursor-default items-center px-padding-md md:px-6 headline-4 outline-none group w-full leading-tight py-padding-xs text-neutral">
            <AtomsIcon
              name="NavChevronDown"
              class="text-icon-hover ease-in transition-transform duration-300 group-data-[state=open]:rotate-180 md:text-[2.5rem] text-[2rem]"
              aria-label="Expand/Collapse"
            />
            <span class="py-0 md:py-padding-xxs">{{ item.title }}</span>
          </AccordionTrigger>
        </AccordionHeader>
        <AccordionContent class="text-neutral bg-neutral slide overflow-hidden body-1 border-t data-[state=closed]:border-t-0 border-neutral">
          <div class="p-padding-md md:p-padding-xl">
            {{ item.content }}
          </div>
        </AccordionContent>
      </AccordionItem>
    </template>
  </AccordionRoot>
</template>
