/** *
https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=4064-43740&m=dev */

<script lang="ts" setup>
import type { AtomsIconButtonProps } from './AtomsIconButton.props'

const props = withDefaults(defineProps<AtomsIconButtonProps>(), {
  state: 'default',
  theme: 'default',
})

const iconButton = tv({
  base: '',
  variants: {
    state: {
      selected: 'bg-transparent text-accent hover:text-hover hover:bg-transparent',
    },
    theme: {
      default: '',
      inverted: 'text-invert',
    },
  },
})

const iconButtonClasses = computed(() => {
  return iconButton({
    state: props.state === 'selected' ? 'selected' : undefined,
    theme: props.theme,
    class: props.class,
  },
  )
})

const iconButtonProps = computed(() => {
  const baseProps = {
    ...props,
    state: props.state === 'selected' ? 'default' : props.state,
  }

  return {
    ...baseProps,
    as: 'button' as const,
    to: undefined,
  }
})
</script>

<template>
  <AtomsButton
    size="icon"
    v-bind="iconButtonProps"
    :class="iconButtonClasses"
  >
    <template #icon>
      <slot name="icon" />
    </template>
  </AtomsButton>
</template>
