import type { Meta, StoryObj } from '@storybook/vue3'
import AtomsFileButton from './AtomsFileButton.vue'
import { iconMap } from './AtomsFileButton.props'

const meta: Meta<typeof AtomsFileButton> = {
  title: 'Atoms/FileButton',
  component: AtomsFileButton,
  tags: ['autodocs'],
  argTypes: {
    name: {
      control: 'text',
      description: 'Name of the file',
    },
    lastUpdate: {
      control: 'text',
      description: 'Last update date',
    },
    fileType: {
      control: 'select',
      options: Object.keys(iconMap),
      description: 'Type of the file',
    },
    status: {
      control: 'select',
      options: ['expired', 'effective', 'none'],
      description: 'Status of the file',
    },
    statusDate: {
      control: 'text',
      description: 'Expiration/Effective date',
    },
    showFavorite: {
      control: 'boolean',
      description: 'Show favorite icon',
    },
    isFavorite: {
      control: 'boolean',
      description: 'Is favorite',
    },
    onOnClick: { action: 'submitted' },

  },
  parameters: {
    viewport: { defaultViewport: 'desktop' },
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=6089-51569&p=f&t=GsqCuX3PNxbRCXBB-0',
      },
    ],
  },
}

export default meta
type Story = StoryObj<typeof AtomsFileButton>

export const Default: Story = {
  render: args => ({
    components: { AtomsFileButton },
    setup() {
      return { args }
    },
    template: `
    <div class="p-10 w-[500px]">
      <AtomsFileButton v-bind="args" @on-click="console.log('Hello World')"/>
    </div>
    `,
  }),
  args: {
    name: 'Lorem_ipsum_dolor_sit_amet.pdf', lastUpdate: '27-03-2025', fileType: 'mp4', status: 'none', showFavorite: false,
  },
}

export const DefaultMobile: Story = {
  parameters: {
    viewport: { defaultViewport: 'iphone14' },
  },
  render: args => ({
    components: { AtomsFileButton },
    setup() {
      return { args }
    },
    template: `
    <div class="bg-brand">
      <AtomsFileButton v-bind="args" @on-click="console.log('Hello World')"/>
    </div>
    `,
  }),
  args: { ...Default.args },
}

export const withStatus: Story = {
  render: args => ({
    components: { AtomsFileButton },
    setup() {
      return { args }
    },
    template: `
    <div class="p-10 w-[500px]">
      <AtomsFileButton v-bind="args" @on-click="console.log('Hello World')"/>
    </div>
    `,
  }),
  args: {
    name: 'Lorem_ipsum_dolor_sit_amet.pdf', fileType: 'mp4', lastUpdate: '27-03-2025', status: 'effective', statusDate: '21-03-2025', showFavorite: false,
  },
}

export const withStatusMobile: Story = {
  parameters: {
    viewport: { defaultViewport: 'iphone14' },
  },
  render: args => ({
    components: { AtomsFileButton },
    setup() {
      return { args }
    },
    template: `
    <div class="bg-brand">
      <AtomsFileButton v-bind="args" @on-click="console.log('Hello World')"/>
    </div>
    `,
  }),
  args: { ...withStatus.args },
}

export const ShowFavorite: Story = {
  render: args => ({
    components: { AtomsFileButton },
    setup() {
      const addToFavorite = ref(false)
      return { args, addToFavorite }
    },
    template: `
    <div class="p-10 w-[500px]">
      <AtomsFileButton v-bind="args" @on-click="console.log('Hello World')"  @on-favorite-click="addToFavorite = !addToFavorite" :is-favorite="addToFavorite"/>
    </div>
    `,
  }),
  args: {
    name: 'Lorem_ipsum_dolor_sit_amet.pdf', fileType: 'mp4', status: 'none', lastUpdate: '27-03-2025', showFavorite: true,
  },
}

export const ShowFavoriteMobile: Story = {
  parameters: {
    viewport: { defaultViewport: 'iphone14' },
  },
  render: args => ({
    components: { AtomsFileButton },
    setup() {
      const addToFavorite = ref(false)
      return { args, addToFavorite }
    },
    template: `
    <div class="bg-brand">
      <AtomsFileButton v-bind="args" @on-click="console.log('Hello World')"  @on-favorite-click="addToFavorite = !addToFavorite" :is-favorite="addToFavorite"/>
    </div>
    `,
  }),
  args: { ...ShowFavorite.args },
}
