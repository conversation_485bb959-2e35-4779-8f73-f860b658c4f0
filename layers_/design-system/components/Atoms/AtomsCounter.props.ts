import type { FormKitNode } from '@formkit/core'

export type AtomsCounterProps = {
  name: string
  minValue?: number
  maxValue?: number
  disabled?: boolean
  validation?: string
  validationRules?: Record<
    string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (node: FormKitNode, ...args: any[]) => boolean | Promise<boolean>
  >
  validationMessages?: Record<
    string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    string | ((ctx: { node: FormKitNode, name: string, args: any[] }) => string)
  >
  validationVisibility?: 'blur' | 'live' | 'dirty' | 'submit'
  errors?: string[]
}
