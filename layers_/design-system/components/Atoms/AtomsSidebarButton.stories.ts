import type { StoryObj } from '@storybook/vue3'
import { ref } from 'vue'
import AtomsSidebarButton from './AtomsSidebarButton.vue'

const meta = {
  title: 'Atoms/SidebarButton',
  component: AtomsSidebarButton,
  argTypes: {
    title: { control: 'text', description: 'Main label of the button' },
    subtitle: { control: 'text', description: 'Optional subtitle' },
    isExpanded: { control: 'boolean', description: 'Whether the folder is expanded' },
    hasChildren: { control: 'boolean', description: 'Whether the folder has children' },
    onClick: { action: 'clicked' },
  },
  args: {
    title: 'Folder',
    subtitle: 'Subtitle',
    isExpanded: false,
    hasChildren: true,
  },
}
export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => ({
    components: { AtomsSidebarButton },
    setup() {
      return { args }
    },
    template: '<AtomsSidebarButton v-bind="args" @click="args.onClick" />',
  }),
}

export const Expanded: Story = {
  args: { isExpanded: true },
  render: args => ({
    components: { AtomsSidebarButton },
    setup() {
      return { args }
    },
    template: '<AtomsSidebarButton v-bind="args" @click="args.onClick" />',
  }),
}

export const NoChildren: Story = {
  args: { hasChildren: false },
  render: args => ({
    components: { AtomsSidebarButton },
    setup() {
      return { args }
    },
    template: '<AtomsSidebarButton v-bind="args" @click="args.onClick" />',
  }),
}

export const Interactive: Story = {
  render: () => ({
    components: { AtomsSidebarButton },
    setup() {
      const isExpanded = ref(false)
      return { isExpanded }
    },
    template: `
      <AtomsSidebarButton
        title="Interactive Folder"
        subtitle="Click to expand/collapse"
        :is-expanded="isExpanded"
        :has-children="true"
        @click="isExpanded = !isExpanded"
      />
    `,
  }),
}
