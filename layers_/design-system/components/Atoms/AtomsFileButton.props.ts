export type AtomsFileButtonProps = {
  name: string
  id: string
  src: string
  lastUpdate: string
  fileType?: FileType
  status?: 'expired' | 'effective' | 'none'
  statusDate?: string
  showFavorite?: boolean
  isFavorite?: boolean
}

export const fileExtentionsList = ['pdf', 'xsl', 'docx', 'dwg', 'jpg', 'mp4', 'png', 'ppt', 'rar', 'zip'] as const
export type FileType = typeof fileExtentionsList[number]

export const iconMap: { [key in FileType]: string } = {
  pdf: 'ExtPDFIcon',
  xsl: 'ExtXSLIcon',
  docx: 'ExtDOCXIcon',
  dwg: 'ExtDWGIcon',
  jpg: 'ExtJPGIcon',
  mp4: 'ExtMP4Icon',
  png: 'ExtPNGIcon',
  ppt: 'ExtPPTIcon',
  rar: 'ExtRARIcon',
  zip: 'ExtZIPIcon',
}
