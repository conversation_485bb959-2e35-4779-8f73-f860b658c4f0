<script setup lang="ts">
import type { AtomsFolderButtonProps } from './AtomsFolderButton.props'

defineProps<AtomsFolderButtonProps>()

defineEmits<{
  (e: 'onClick'): void
}>()
</script>

<template>
  <button
    class="group w-full bg-neutral text-accent hover:text-hover px-padding-md py-padding-xs lg:bg-brand lg:hover:bg-hover lg:text-neutral lg:hover:text-invert lg:p-padding-md lg:shadow-100 lg:rounded-sm  cursor-pointer"
    @click="$emit('onClick')"
  >
    <div class="flex items-center gap-xxs lg:items-start lg:justify-between">
      <AtomsIcon name="GenFolderOpened" class="group-hover:!block !hidden group-hover:text-icon-hover group-hover:lg:text-icon-invert !w-13 !h-13 lg:order-2 lg:!w-8 lg:!h-8" />
      <AtomsIcon name="GenFolderIcon" class="group-hover:!hidden !block group-hover:text-icon-hover group-hover:lg:text-icon-invert !w-13 !h-13 lg:order-2 lg:!w-8 lg:!h-8" />
      <div>
        <p class="subtitle-2 lg:text-subtitle-sm text-start">
          {{ name }}
        </p>
        <p class="body-2 text-start">
          {{ $ts("atoms.folderButton.lastUpdate") }} {{ lastUpdate }}
        </p>
      </div>
    </div>
    <p class="hidden lg:block text-start">
      {{ text }}
    </p>
  </button>
</template>
