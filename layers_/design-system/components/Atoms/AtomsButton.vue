/** * https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=94-9&m=dev */

<script setup lang="ts" generic="T">
import type { AtomsButtonProps } from './AtomsButton.props'
import AtomsLoader from './AtomsLoader.vue'

const props = withDefaults(defineProps<AtomsButtonProps>(), {
  anatomy: 'primary',
  size: 'l',
  state: 'default',
  as: 'button',
})

const button = tv({
  slots: {
    base: 'rounded-full inline-grid relative place-content-center leading-tight *:align-middle transition-colors duration-200 cursor-pointer focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-4 focus-visible:ring-(--color-text-info)',
    icon: 'inline-flex justify-center items-center',
    label: 'inline-block *:align-middle',
    loadingIndicator: 'absolute inset-4 h-auto',
    runner: 'absolute block rounded-full overflow-hidden bg-current animate-run w-4 h-full',
  },
  variants: {
    anatomy: {
      primary: 'bg-accent text-invert hover:bg-hover',
      secondary: 'bg-brand text-accent hover:text-hover',
      tertiary: 'bg-transparent text-accent hover:text-hover',
    },
    size: {
      m: {
        base: 'cta-2 py-padding-xxs min-h-9 h-fit px-padding-sm gap-xxs',
        icon: '*:text-[1.5rem]',
        loadingIndicator: 'inset-3.5',
      },
      l: {
        base: 'cta-1 py-padding-xxs min-h-12 h-fit px-padding-xl gap-xs',
        icon: '*:text-[2rem]',
        loadingIndicator: 'inset-4.5',
      },
      icon: {
        base: 'h-8 w-8',
        icon: '*:text-[1.5rem]',
      },
    },
    state: {
      default: '',
      disabled: 'bg-disabled text-disabled cursor-not-allowed hover:bg-disabled hover:text-disabled',
      loading: {
        label: 'opacity-0 visually-hidden',
      },
    },
  },
  defaultVariants: {
    anatomy: 'primary',
    size: 'l',
    state: 'default',
  },
  compoundVariants: [
    {
      anatomy: 'tertiary',
      state: 'disabled',
      class: 'bg-transparent text-disabled hover:bg-transparent',
    },
  ],
})

const buttonClasses = computed(() => button({ ...props }))

const as = computed(() => {
  if (props.as === 'link') {
    return resolveComponent('NuxtLink')
  }
  return 'button'
})

const to = computed(() => {
  if (props.as === 'link' && props.to) {
    return props.to
  }
  return undefined
})

const ariaLabel = computed(() => {
  if (props.size === 'icon' && !props.ariaLabel) {
    console.warn('AtomsButton: aria-label is required for icon-only buttons')
  }

  if (props.ariaLabel) {
    return props.ariaLabel
  }
  return undefined
})
</script>

<template>
  <component
    :is="as"
    :class="buttonClasses.base({ class: props.class })"
    :disabled="state === 'disabled' || state === 'loading'"
    :type="as === 'button' ? type : undefined"
    :to="to"
    :aria-label="ariaLabel"
    :aria-description="ariaDescription"
    :aria-busy="state === 'loading'"
    @click="onClick"
    @submit="onSubmit"
    @reset="onReset"
    @keydown="onKeyDown"
  >
    <AtomsLoader
      v-show="state === 'loading'"
      :variant="anatomy === 'primary' ? 'inverted' : 'default'"
      :class="buttonClasses.loadingIndicator()"
    />

    <span :class="buttonClasses.label()">
      <span
        v-if="$slots.iconLeft"
        :class="buttonClasses.icon()"
      >
        <slot name="iconLeft" />
      </span>
      <span v-if="$slots.default">
        <slot />
      </span>
      <span
        v-if="$slots.icon"
        :class="buttonClasses.icon()"
      >
        <slot
          name="icon"
          size="2rem"
        />
      </span>
    </span>
  </component>
</template>
