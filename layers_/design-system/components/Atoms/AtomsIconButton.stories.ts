import type { Meta, StoryObj } from '@storybook/vue3'
import AtomsIconButton from './AtomsIconButton.vue'
import AtomsIcon from './AtomsIcon.vue'

// Define metadata for the component
const meta = {
  title: 'Atoms/IconButton',
  component: AtomsIconButton,
  tags: ['autodocs'],
  argTypes: {
    anatomy: {
      control: 'select',
      options: ['primary', 'secondary', 'tertiary'],
      description: 'The visual style of the icon button',
    },
    state: {
      control: 'select',
      options: ['default', 'selected', 'disabled'],
      description: 'The state of the icon button',
    },
    as: {
      control: 'select',
      options: ['button'],
      description: 'The HTML element to render',
    },
    onClick: { action: 'clicked' },
  },
  args: {
    // Default args for all stories
    anatomy: 'secondary',
    state: 'default',
    as: 'button',
  },
} satisfies Meta<typeof AtomsIconButton>

export default meta
type Story = StoryObj<typeof meta>

// Base stories
export const Default: Story = {
  args: {
    anatomy: 'secondary',
    state: 'default',
  },
  render: args => ({
    components: { AtomsIconButton, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
      <AtomsIconButton v-bind="args">
        <template #icon>
          <AtomsIcon name="NavFilter" />
        </template>
      </AtomsIconButton>
    `,
  }),
}

export const Primary: Story = {
  args: {
    anatomy: 'primary',
  },
  render: args => ({
    components: { AtomsIconButton, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
      <AtomsIconButton v-bind="args">
        <template #icon>
          <AtomsIcon name="NavFilter" />
        </template>
      </AtomsIconButton>
    `,
  }),
}

export const Secondary: Story = {
  args: {
    anatomy: 'secondary',
  },
  render: args => ({
    components: { AtomsIconButton, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
      <AtomsIconButton v-bind="args">
        <template #icon>
          <AtomsIcon name="NavFilter" />    
        </template>
      </AtomsIconButton>
    `,
  }),
}

export const Tertiary: Story = {
  args: {
    anatomy: 'tertiary',
  },
  render: args => ({
    components: { AtomsIconButton, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
      <AtomsIconButton v-bind="args">
        <template #icon>
          <AtomsIcon name="NavFilter" />
        </template>
      </AtomsIconButton>
    `,
  }),
}

// State variants
export const Selected: Story = {
  args: {
    state: 'selected',
  },
  render: args => ({
    components: { AtomsIconButton, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
      <AtomsIconButton v-bind="args">
        <template #icon>
          <AtomsIcon name="NavFilter" />
        </template>
      </AtomsIconButton>
    `,
  }),
}

export const Disabled: Story = {
  args: {
    state: 'disabled',
  },
  render: args => ({
    components: { AtomsIconButton, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
      <AtomsIconButton v-bind="args">
        <template #icon>
          <AtomsIcon name="NavFilter" />
        </template>
      </AtomsIconButton>
    `,
  }),
}

// Link button
export const AsLink: Story = {
  args: {
    as: 'link',
  },
  render: args => ({
    components: { AtomsIconButton, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
      <AtomsIconButton v-bind="args">
        <template #icon>
          <AtomsIcon name="NavFilter" />
        </template>
      </AtomsIconButton>
    `,
  }),
}

// Comprehensive showcase
export const AllVariants: Story = {
  render: () => ({
    components: { AtomsIconButton, AtomsIcon },
    setup() {
      return {}
    },
    template: `
      <div class="grid grid-cols-3 gap-4">
        <!-- Primary buttons -->
        <div>
          <AtomsIconButton anatomy="primary">
            <template #icon>
              <AtomsIcon name="NavFilter" />
            </template>
          </AtomsIconButton>
        </div>
        <div>
          <AtomsIconButton anatomy="primary" state="selected">
            <template #icon>
              <AtomsIcon name="NavFilter" />
            </template>
          </AtomsIconButton>
        </div>
        <div>
          <AtomsIconButton anatomy="primary" state="disabled">
            <template #icon>
              <AtomsIcon name="NavFilter" />
            </template>
          </AtomsIconButton>
        </div>

        <!-- Secondary buttons -->
        <div>
          <AtomsIconButton anatomy="secondary">
            <template #icon>
              <AtomsIcon name="NavFilter" />
            </template>
          </AtomsIconButton>
        </div>
        <div>
          <AtomsIconButton anatomy="secondary" state="selected">
            <template #icon>
              <AtomsIcon name="NavFilter" />
            </template>
          </AtomsIconButton>
        </div>
        <div>
          <AtomsIconButton anatomy="secondary" state="disabled">
            <template #icon>
              <AtomsIcon name="NavFilter" />
            </template>
          </AtomsIconButton>
        </div>

        <!-- Tertiary buttons -->
        <div>
          <AtomsIconButton anatomy="tertiary">
            <template #icon>
              <AtomsIcon name="NavFilter" />
            </template>
          </AtomsIconButton>
        </div>
        <div>
          <AtomsIconButton anatomy="tertiary" state="selected">
            <template #icon>
              <AtomsIcon name="NavFilter" />  
            </template>
          </AtomsIconButton>
        </div>
        <div>
          <AtomsIconButton anatomy="tertiary" state="disabled">
            <template #icon>
              <AtomsIcon name="NavFilter" />
            </template>
          </AtomsIconButton>
        </div>
      </div>
    `,
  }),
}
