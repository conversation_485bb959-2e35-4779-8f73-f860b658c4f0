<script setup lang='ts'>
import type { AtomsCounterProps } from './AtomsCounter.props'

const props = withDefaults(defineProps<AtomsCounterProps>(), {
  minValue: 0,
  disabled: false,
  validation: '',
  validationVisibility: 'blur',
})

const value = defineModel<number>('value', {
  default: 0,
})

const handleSubtract = () => {
  if (!isSubtractDisabled.value)
    value.value--
}

const handleAdd = () => {
  if (!isAddDisabled.value)
    value.value++
}

const isSubtractDisabled = computed(() => props.disabled || value.value <= props.minValue)

const isAddDisabled = computed(() => props.disabled || (!!props.maxValue && value.value >= props.maxValue))

const fullValidation = computed(() => [
  `min:${props.minValue}`,
  props.maxValue ? `max:${props.maxValue}` : '',
  props.validation ?? '',
].join('|'))
</script>

<template>
  <FormKit
    :id="name"
    v-model="value"
    type="number"
    number="integer"
    :name="name"
    :min="minValue"
    :max="maxValue"
    :disabled="disabled"
    :validation="fullValidation"
    :validation-rules="validationRules"
    :validation-messages="validationMessages"
    :errors="errors"
    :validation-visibility="validationVisibility"
    :classes="{
      outer: 'flex flex-col gap-y-xxs',
      inner: '$reset flex gap-x-md',
      input: {
        '$reset appearance-none': true,
        '[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none': true,
        'body-2 text-accent hover:text-hover text-center': true,
        'max-w-16 px-padding-xs py-1': true,
        'bg-neutral border border-neutral hover:border-hover rounded-sm': true,
        'hover:shadow-[0_0_4px_0] hover:shadow-(color:--color-states-azure-1000)': true,
        'disabled:text-disabled disabled:bg-disabled disabled:border-disabled': true,
        'focus-visible:outline-2 outline-offset-4 outline-(--color-stroke-focus)': true,
      },
      messages: 'space-y-1',
    }"
  >
    <template #prefixIcon>
      <AtomsButton
        anatomy="tertiary"
        size="icon"
        :state="isSubtractDisabled ? 'disabled' : 'default'"
        :on-click="handleSubtract"
        :aria-label="`decrease counter ${name}`"
        type="button"
      >
        <template #icon>
          <AtomsIcon name="NavMinusIcon" />
        </template>
      </AtomsButton>
    </template>

    <template #suffixIcon>
      <AtomsButton
        anatomy="tertiary"
        size="icon"
        :state="isAddDisabled ? 'disabled' : 'default'"
        :on-click="handleAdd"
        type="button"
        :aria-label="`increase counter ${name}`"
      >
        <template #icon>
          <AtomsIcon name="NavPlusIcon" />
        </template>
      </AtomsButton>
    </template>
  </FormKit>
</template>
