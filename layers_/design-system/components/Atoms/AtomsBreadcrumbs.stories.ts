import type { Meta, StoryObj } from '@storybook/vue3'

import AtomsBreadcrumbs from './AtomsBreadcrumbs.vue'

const meta: Meta<typeof AtomsBreadcrumbs> = {
  title: 'Atoms/Breadcrumbs',
  component: AtomsBreadcrumbs,
}
export default meta

type Story = StoryObj<typeof AtomsBreadcrumbs>

export const Default: Story = {
  render: args => ({
    components: { AtomsBreadcrumbs },
    setup() {
      return { args }
    },
    template: `<AtomsBreadcrumbs v-bind="args"/>`,
  }),
  args: {
    items: [
      { label: 'First' },
      { label: 'Second' },
      { label: 'Third' },
      { label: 'Last one' },
    ],
  },
}

export const Overflowing: Story = {
  render: args => ({
    components: { AtomsBreadcrumbs },
    setup() {
      return { args }
    },
    template: `<div class="w-[300px] bg-brand p-2"><AtomsBreadcrumbs v-bind="args"/></div>`,
  }),
  args: {
    items: [
      { label: 'First' },
      { label: 'Second' },
      { label: 'Third' },
      { label: 'Fourth' },
      { label: 'Fifth' },
      { label: 'Last one' },
    ],
  },
}
