<script setup lang="ts">
import type { AtomsChipProps } from './AtomsChip.props'

const props = defineProps<AtomsChipProps>()

defineEmits<{
  (e: 'remove'): void
}>()

const chipTw = tv({
  slots: {
    containerTv: [
      'group w-fit pl-padding-xs pr-padding-sm py-padding-xxs',
      'flex flex-nowrap justify-center items-center gap-x-gap-xxs',
      'bg-light border-accent rounded-max border',
    ],
    iconTv: '*:!w-6 *:!h-6 h-6 text-accent m-auto',
    labelTv: 'text-cta-md font-bold text-accent text-nowrap overflow-hidden',
  },
  variants: {
    disabled: {
      true: {
        containerTv: 'bg-disabled border-transparent !pl-padding-sm',
        labelTv: 'text-disabled',
      },
      false: {
        containerTv: 'hover:border-hover',
        iconTv: 'group-hover:text-hover',
        labelTv: 'group-hover:text-hover',
      },
    },
  },
  defaultVariants: {
    disabled: false,
  },
})

const { containerTv, iconTv, labelTv } = chipTw({ ...props })
</script>

<template>
  <div
    :class="containerTv({ disabled })"
    :disabled="disabled"
  >
    <button v-if="!disabled" :class="iconTv({ disabled })" @click="$emit('remove')">
      <AtomsIcon name="NavCloseIcon" />
    </button>

    <span :class="labelTv({ disabled })">{{ label }}</span>
  </div>
</template>
