type LinkProps = { as: 'link' } & { to: string, type?: never }
type ButtonProps = { as?: 'button' } & { to?: never, type?: 'submit' | 'reset' | 'button' }

export type AtomsButtonProps = {
  anatomy?: 'primary' | 'secondary' | 'tertiary'
  size?: 'm' | 'l' | 'icon'
  state?: 'default' | 'disabled' | 'loading'
  ariaLabel?: string
  ariaDescription?: string
  class?: string
  onClick?: () => void
  onKeyDown?: (event: KeyboardEvent) => void
  onSubmit?: () => void
  onReset?: () => void
} & (ButtonProps | LinkProps)
