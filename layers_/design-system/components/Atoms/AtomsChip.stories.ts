import type { Meta, StoryObj } from '@storybook/vue3'
import AtomsChip from './AtomsChip.vue'

const meta: Meta<typeof AtomsChip> = {
  title: 'Atoms/Chips',
  component: AtomsChip,
  tags: ['autodoc'],
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=2146-1031',
      },
    ],
  },
  argTypes: {
    label: {
      control: 'text',
      description: 'The label inside the chip',
    },
    disabled: {
      control: 'boolean',
      description: 'Disable the button',
    },
  },
  args: {
    label: 'Chip',
    disabled: false,
  },
}

export default meta
type Story = StoryObj<typeof AtomsChip>

export const Default: Story = {
  args: {
    label: 'Chip',
    disabled: false,
  },
  render: args => ({
    components: { AtomsChip },
    setup() {
      return { args }
    },
    template: `
    <div class="flex flex-col gap-4" >
      <AtomsChip v-bind=args />
    </div>
    `,
  }),
}
