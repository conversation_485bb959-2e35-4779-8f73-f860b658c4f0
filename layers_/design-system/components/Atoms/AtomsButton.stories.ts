import type { StoryObj } from '@storybook/vue3'
import AtomsButton from './AtomsButton.vue'
import AtomsIcon from './AtomsIcon.vue'

// Define metadata for the component
const meta = {
  title: 'Atoms/Button',
  component: AtomsButton,
  tags: ['autodocs'],
  argTypes: {
    anatomy: {
      control: 'select',
      options: ['primary', 'secondary', 'tertiary'],
      description: 'The visual style of the button',
    },
    size: {
      control: 'select',
      options: ['m', 'l', 'icon'],
      description: 'The size of the button',
    },
    state: {
      control: 'select',
      options: ['default', 'disabled', 'loading'],
      description: 'The state of the button',
    },
    as: {
      control: 'select',
      options: ['button', 'link'],
      description: 'The HTML element to render',
    },
    to: {
      control: 'text',
      description: 'URL for link buttons',
      if: { arg: 'as', eq: 'link' },
    },
    type: {
      control: 'select',
      options: ['submit', 'reset'],
      description: 'The type of the button',
      if: { arg: 'as', eq: 'button' },
    },
    onClick: { action: 'clicked' },
    onSubmit: { action: 'submitted' },
    onReset: { action: 'reset' },
  },
  args: {
    anatomy: 'primary',
    size: 'l',
    state: 'default',
  },
}

export default meta
type Story = StoryObj<typeof meta>

// Base stories
export const Primary: Story = {
  args: {
    anatomy: 'primary',
  },
  render: args => ({
    components: { AtomsButton },
    setup() {
      return { args }
    },
    template: '<AtomsButton v-bind="args">Button</AtomsButton>',
  }),
}

export const Secondary: Story = {
  args: {
    anatomy: 'secondary',
  },
  render: args => ({
    components: { AtomsButton },
    setup() {
      return { args }
    },
    template: '<AtomsButton v-bind="args">Button</AtomsButton>',
  }),
}

export const Tertiary: Story = {
  args: {
    anatomy: 'tertiary',
  },
  render: args => ({
    components: { AtomsButton },
    setup() {
      return { args }
    },
    template: '<AtomsButton v-bind="args">Button</AtomsButton>',
  }),
}

// Size variants
export const MediumSize: Story = {
  args: {
    size: 'm',
  },
  render: args => ({
    components: { AtomsButton },
    setup() {
      return { args }
    },
    template: '<AtomsButton v-bind="args">Button</AtomsButton>',
  }),
}

export const LargeSize: Story = {
  args: {
    size: 'l',
  },
  render: args => ({
    components: { AtomsButton },
    setup() {
      return { args }
    },
    template: '<AtomsButton v-bind="args">Button</AtomsButton>',
  }),
}

// Icon only button
export const IconOnly: Story = {
  args: {
    size: 'icon',
  },
  render: args => ({
    components: { AtomsButton, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
      <AtomsButton v-bind="args">
        <template #icon>
          <AtomsIcon name="NavFilter" />
        </template>
      </AtomsButton>
    `,
  }),
}

// State variants
export const Disabled: Story = {
  args: {
    state: 'disabled',
  },
  render: args => ({
    components: { AtomsButton },
    setup() {
      return { args }
    },
    template: '<AtomsButton v-bind="args">Button</AtomsButton>',
  }),
}

// Adding a new story for the loading state below the Disabled story
export const Loading: Story = {
  args: {
    state: 'loading',
  },
  render: args => ({
    components: { AtomsButton },
    setup() {
      return { args }
    },
    template: '<AtomsButton v-bind="args">Loading Button</AtomsButton>',
  }),
}

// With icon
export const WithIcon: Story = {
  args: {
    anatomy: 'primary',
  },
  render: args => ({
    components: { AtomsButton, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
      <AtomsButton v-bind="args">
        Button with Icon
        <template #icon>
          <AtomsIcon name="NavFilter" />
        </template>
      </AtomsButton>
    `,
  }),
}

// Link button
export const AsLink: Story = {
  args: {
    as: 'link',
    to: 'https://www.example.com',
  },
  render: args => ({
    components: { AtomsButton, AtomsIcon },
    setup() {
      return { args }
    },
    template: '<AtomsButton v-bind="args">Link Button</AtomsButton>',
  }),
}

// Comprehensive showcase
export const AllVariants: Story = {
  render: () => ({
    components: { AtomsButton, AtomsIcon },
    setup() {
      return {}
    },
    template: `
      <div class="grid grid-cols-3 gap-4">
        <!-- Primary buttons -->
        <div>
          <AtomsButton anatomy="primary">Primary</AtomsButton>
        </div>
        <div>
          <AtomsButton anatomy="primary" size="m">Primary Medium</AtomsButton>
        </div>
        <div>
          <AtomsButton anatomy="primary" state="disabled">Primary Disabled</AtomsButton>
        </div>
        
        <!-- Secondary buttons -->
        <div>
          <AtomsButton anatomy="secondary">Secondary</AtomsButton>
        </div>
        <div>
          <AtomsButton anatomy="secondary" size="m">Secondary Medium</AtomsButton>
        </div>
        <div>
          <AtomsButton anatomy="secondary" state="disabled">Secondary Disabled</AtomsButton>
        </div>
        
        <!-- Tertiary buttons -->
        <div>
          <AtomsButton anatomy="tertiary">Tertiary</AtomsButton>
        </div>
        <div>
          <AtomsButton anatomy="tertiary" size="m">Tertiary Medium</AtomsButton>
        </div>
        <div>
          <AtomsButton anatomy="tertiary" state="disabled">Tertiary Disabled</AtomsButton>
        </div>
        
        <!-- With icons -->
        <div>
          <AtomsButton anatomy="primary">
            With Icon
            <template #icon>
              <AtomsIcon name="NavFilter" />
            </template>
          </AtomsButton>
        </div>
        <div>
          <AtomsButton anatomy="secondary">
            With Icon
            <template #icon>
              <AtomsIcon name="NavFilter" />
            </template>
          </AtomsButton>
        </div>
        <div>
          <AtomsButton anatomy="tertiary">
            With Icon
            <template #icon>
              <AtomsIcon name="NavFilter" />
            </template>
          </AtomsButton>
        </div>

        <!-- Loading state variants added -->
        <div>
          <AtomsButton anatomy="primary" state="loading">Primary Loading</AtomsButton>
        </div>
        <div>
          <AtomsButton anatomy="secondary" state="loading">Secondary Loading</AtomsButton>
        </div>
        <div>
          <AtomsButton anatomy="tertiary" state="loading">Tertiary Loading</AtomsButton>
        </div>
        
        <!-- Icon only buttons -->
        <div>
          <AtomsButton size="icon" anatomy="primary">
            <template #icon>
              <AtomsIcon name="NavFilter" />
            </template>
          </AtomsButton>
        </div>
        <div>
          <AtomsButton size="icon" anatomy="secondary">
            <template #icon>
              <AtomsIcon name="NavFilter" />
            </template>
          </AtomsButton>
        </div>
        <div>
          <AtomsButton size="icon" anatomy="tertiary">
            <template #icon>
              <AtomsIcon name="NavFilter" />
            </template>
          </AtomsButton>
        </div>

      </div>
    `,
  }),
}
