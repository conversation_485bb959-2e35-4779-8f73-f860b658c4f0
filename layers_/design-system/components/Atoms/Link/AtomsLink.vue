<script setup lang="ts">
import type { AtomsLinkProps } from './AtomsLink.props'

const props = withDefaults(defineProps<AtomsLinkProps>(), {
  hierarchy: 'primary',
  class: '',
})

const emit = defineEmits<{
  click: [e: Event]
}>()

const handleClick = (event: Event) => {
  emit('click', event)
}

const computedAriaLabel = computed(() => props.ariaLabel || undefined)

const link = tv({
  base: 'text-accent hover:text-hover focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent leading-none inline-flex items-center gap-1 underline-offset-2 underline [&>span.iconify]:shrink-0 text-start',
  variants: {
    hierarchy: {
      primary: 'link-1 [&>span.iconify]:text-[1.5rem]',
      secondary: 'link-2 [&>span.iconify]:text-[1.25rem]',
    },
    inverted: {
      true: 'text-white hover:text-white',
      false: '',
    },
    disabled: {
      true: 'text-disabled cursor-not-allowed hover:text-disabled',
      false: 'cursor-pointer',
    },
  },
})
</script>

<template>
  <NuxtLink
    v-if="!disabled && to"
    :to="to"
    :class="link({ ...props })"
    :aria-label="computedAriaLabel"
    :target="target ?? undefined"
    :external="external ?? undefined"
  >
    <slot>{{ label }}</slot>
  </NuxtLink>

  <button
    v-else-if="!disabled && ['onClick']"
    :class="link({ ...props })"
    @click.prevent="handleClick"
  >
    <slot>{{ label }}</slot>
  </button>

  <span
    v-else
    :class="link({ ...props, disabled: true })"
    aria-disabled="true"
  >
    <slot>{{ label }}</slot>
  </span>
</template>
