<script lang="ts" setup>
import type { AtomsLinkMenuProps } from './AtomsLinkMenu.props'

const props = defineProps<AtomsLinkMenuProps>()

const link = tv({
  base: 'text-neutral w-full flex gap-2 min-h px-padding-md py-padding-xs items-center justify-between no-underline body-2 md:body-1 rounded-full min-h-11',
  variants: {
    selected: {
      true: 'text-accent font-bold hover:text-accent',
    },
    disabled: {
      false: 'md:hover:bg-hover md:hover:text-white',
      true: 'text-disabled',
    },
  },
})
</script>

<template>
  <AtomsLink
    :class="link({ selected, disabled })"
    v-bind="props"
  >
    <slot>Default Link Text</slot>
  </AtomsLink>
</template>
