<script setup lang="ts">
import type { AtomsLinkHeaderProps } from './AtomsLinkHeader.props'

const props = defineProps<AtomsLinkHeaderProps>()

const link = tv({
  base: 'no-underline hover:underline text-neutral h-10 flex items-center [&>span.iconify]:text-[1.25rem]',
  variants: {
    selected: {
      true: 'font-bold relative hover:after:bg-hover  hover:no-underline after:content-[""] after:block after:w-full after:h-[2px] after:bg-accent after:absolute after:bottom-0 after:left-0',
      false: '',
    },
    disabled: {
      true: 'text-disabled! after:hidden hover:text-disabled pointer-events-none hover:no-underline',
      false: '',
    },
    inverted: {
      true: 'text-white hover:text-white after:bg-white hover:after:bg-white',
      false: '',
    },
  },
  compoundVariants: [
    {
      selected: true,
      inverted: false,
      class: 'text-accent hover:text-hover',
    },
    {
      selected: true,
      inverted: true,
      class: 'text-white hover:text-white after:bg-white hover:after:bg-white',
    },
  ],
  defaultVariants: {
    selected: false,
    disabled: false,
  },
})
</script>

<template>
  <AtomsLink
    v-bind="props"
    :class="link({ ...props })"
  >
    <slot>Default Link Text</slot>
  </AtomsLink>
</template>
