import type { Meta, StoryObj } from '@storybook/vue3'
import AtomsLinkHeader from './AtomsLinkHeader.vue'

const meta = {
  title: 'Atoms/Link/Header',
  component: AtomsLinkHeader,
  tags: ['autodocs'],
  argTypes: {
    to: {
      control: 'text',
      description: 'The URL or route to link to',
    },
    external: {
      control: 'boolean',
      description: 'Whether the link opens in a new tab',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the link is disabled',
    },
    selected: {
      control: 'boolean',
      description: 'Whether the header link is currently selected',
    },
    inverted: {
      control: 'boolean',
      description: 'Whether the link should use inverted colors for dark backgrounds',
    },
  },
} satisfies Meta<typeof AtomsLinkHeader>

export default meta
type Story = StoryObj<typeof meta>

// Standard Header Link
export const Standard: Story = {
  args: {
    to: '/header-link',
    selected: false,
  },
  render: args => ({
    components: { AtomsLinkHeader },
    setup() {
      return { args }
    },
    template: `
      <AtomsLinkHeader v-bind="args">
        Header Link
      </AtomsLinkHeader>
    `,
  }),
}

// Selected Header Link
export const Selected: Story = {
  args: {
    to: '/header-link',
    selected: true,
  },
  render: args => ({
    components: { AtomsLinkHeader },
    setup() {
      return { args }
    },
    template: `
      <AtomsLinkHeader v-bind="args">
        Selected Header Link
      </AtomsLinkHeader>
    `,
  }),
}

// External Header Link
export const External: Story = {
  args: {
    to: 'https://example.com',
    external: true,
    selected: false,
  },
  render: args => ({
    components: { AtomsLinkHeader },
    setup() {
      return { args }
    },
    template: `
      <AtomsLinkHeader v-bind="args">
        External Header Link
      </AtomsLinkHeader>
    `,
  }),
}

// Disabled Header Link
export const Disabled: Story = {
  args: {
    to: '/header-link',
    disabled: true,
    selected: false,
  },
  render: args => ({
    components: { AtomsLinkHeader },
    setup() {
      return { args }
    },
    template: `
      <AtomsLinkHeader v-bind="args">
        Disabled Header Link
      </AtomsLinkHeader>
    `,
  }),
}

// Inverted Header Links
export const InvertedLinks: Story = {
  render: () => ({
    components: { AtomsLinkHeader },
    template: `
      <div class="bg-hover p-2 py-4 rounded-md flex gap-2">
        <AtomsLinkHeader inverted to="/">
          Inverted Link
        </AtomsLinkHeader>
        <AtomsLinkHeader inverted selected to="/">
          Selected Inverted
        </AtomsLinkHeader>
      </div>
    `,
  }),
}
