<script lang="ts" setup>
import type { AtomsLinkFooterProps } from './AtomsLinkFooter.props'

const props = defineProps<AtomsLinkFooterProps>()

const link = tv({
  base: 'no-underline hover:underline group',
  variants: {
    disabled: {
      true: 'hover:no-underline',
      false: '',
    },
  },
})
</script>

<template>
  <AtomsLink
    :class="link({ ...props })"
    v-bind="props"
    inverted
    :disabled="disabled"
  >
    <AtomsIcon
      v-if="theme === 'skyfold'"
      name="NavChevronRight"
      class="group-hover:!hidden"
    />
    <AtomsIcon
      v-if="theme === 'skyfold'"
      name="NavArrowRight"
      class="group-hover:!inline-block !hidden"
    />

    <slot>{{ label }}</slot>
  </AtomsLink>
</template>
