import type { Meta, StoryObj } from '@storybook/vue3'
import AtomsIcon from '@design-system/components/Atoms/AtomsIcon.vue'
import AtomsLinkMenu from './AtomsLinkMenu.vue'

const meta = {
  title: 'Atoms/Link/Menu',
  component: AtomsLinkMenu,
  tags: ['autodocs'],
  argTypes: {
    to: {
      control: 'text',
      description: 'The URL or route to link to',
    },
    external: {
      control: 'boolean',
      description: 'Whether the link opens in a new tab',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the link is disabled',
    },
    selected: {
      control: 'boolean',
      description: 'Whether the menu item is currently selected',
    },
  },
} satisfies Meta<typeof AtomsLinkMenu>

export default meta
type Story = StoryObj<typeof meta>

// Standard Menu Link
export const Standard: Story = {
  args: {
    to: '/menu-item',
    selected: false,
  },
  render: args => ({
    components: { AtomsLinkMenu },
    setup() {
      return { args }
    },
    template: `
      <AtomsLinkMenu v-bind="args">
        Menu Item
      </AtomsLinkMenu>
    `,
  }),
}

// Standard Menu Link with Icon
export const StandardWithIcon: Story = {
  args: {
    to: '/menu-item',
    selected: false,
  },
  render: args => ({
    components: { AtomsLinkMenu, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
      <AtomsLinkMenu v-bind="args">
        Menu Item
        <AtomsIcon name="NavChevronRight" />
      </AtomsLinkMenu>
    `,
  }),
}

// Selected Menu Link
export const Selected: Story = {
  args: {
    to: '/menu-item',
    selected: true,
  },
  render: args => ({
    components: { AtomsLinkMenu },
    setup() {
      return { args }
    },
    template: `
      <AtomsLinkMenu v-bind="args">
        Selected Menu Item
      </AtomsLinkMenu>
    `,
  }),
}

// External Menu Link
export const External: Story = {
  args: {
    to: 'https://example.com',
    external: true,
    selected: false,
  },
  render: args => ({
    components: { AtomsLinkMenu },
    setup() {
      return { args }
    },
    template: `
      <AtomsLinkMenu v-bind="args">
        External Menu Item
      </AtomsLinkMenu>
    `,
  }),
}

// Disabled Menu Link
export const Disabled: Story = {
  args: {
    to: '/menu-item',
    disabled: true,
    selected: false,
  },
  render: args => ({
    components: { AtomsLinkMenu },
    setup() {
      return { args }
    },
    template: `
      <AtomsLinkMenu v-bind="args">
        Disabled Menu Item
      </AtomsLinkMenu>
    `,
  }),
}
