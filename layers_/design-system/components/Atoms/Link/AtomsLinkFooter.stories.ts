import type { Meta, StoryObj } from '@storybook/vue3'
import AtomsLinkFooter from './AtomsLinkFooter.vue'

const meta = {
  title: 'Atoms/Link/Footer',
  component: AtomsLinkFooter,
  tags: ['autodocs'],
  decorators: [() => ({ template: '<div class="bg-accent p-4 rounded-md"><story/></div>' })],
  argTypes: {
    to: {
      control: 'text',
      description: 'The URL or route to link to',
    },
    external: {
      control: 'boolean',
      description: 'Whether the link opens in a new tab',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the link is disabled',
    },
    theme: {
      control: 'select',
      options: ['modernfold', 'skyfold'],
      description: 'The theme of the footer link (MF or SF)',
    },
  },
} satisfies Meta<typeof AtomsLinkFooter>

export default meta
type Story = StoryObj<typeof meta>

// MF Footer Link
export const MFFooter: Story = {
  args: {
    to: '/main-footer',
    theme: 'modernfold',
  },
  render: args => ({
    components: { AtomsLinkFooter },
    setup() {
      return { args }
    },
    template: `
      <AtomsLinkFooter v-bind="args">
        MF Footer Link
      </AtomsLinkFooter>
    `,
  }),
}

// SF Footer Link
export const SFFooter: Story = {
  args: {
    to: '/secondary-footer',
    theme: 'modernfold',
  },
  render: args => ({
    components: { AtomsLinkFooter },
    setup() {
      return { args }
    },
    template: `
      <AtomsLinkFooter v-bind="args">
        SF Footer Link
      </AtomsLinkFooter>
    `,
  }),
}

// External Footer Link
export const ExternalFooter: Story = {
  args: {
    to: 'https://example.com',
    external: true,
    theme: 'modernfold',
  },
  render: args => ({
    components: { AtomsLinkFooter },
    setup() {
      return { args }
    },
    template: `
      <AtomsLinkFooter v-bind="args">
        External Footer Link
      </AtomsLinkFooter>
    `,
  }),
}
