import type { Meta, StoryObj } from '@storybook/vue3'
import AtomsIcon from '@design-system/components/Atoms/AtomsIcon.vue'
import AtomsLink from './AtomsLink.vue'

const meta = {
  title: 'Atoms/Link',
  component: AtomsLink,
  tags: ['autodocs'],
  argTypes: {
    to: {
      control: 'text',
      description: 'The URL or route to link to',
    },
    external: {
      control: 'boolean',
      description: 'Whether the link opens in a new tab',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the link is disabled',
    },
    hierarchy: {
      control: 'select',
      options: ['primary', 'secondary'],
      description: 'The visual hierarchy of the link',
    },
    inverted: {
      control: 'boolean',
      description: 'Whether the link should use inverted colors for dark backgrounds',
    },
    ariaLabel: {
      control: 'text',
      description: 'Custom ARIA label for accessibility',
    },
    class: {
      control: 'text',
      description: 'Additional CSS classes to apply',
    },
  },
} satisfies Meta<typeof AtomsLink>

export default meta
type Story = StoryObj<typeof meta>

// Basic Links
export const StandardInternal: Story = {
  args: {
    to: '/about',
  },
  render: args => ({
    components: { AtomsLink },
    setup() {
      return { args }
    },
    template: `
            <AtomsLink v-bind="args">
                Standard Internal Link
            </AtomsLink>
        `,
  }),
}

export const External: Story = {
  args: {
    to: 'https://example.com',
    external: true,
  },
  render: args => ({
    components: { AtomsLink, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
            <AtomsLink v-bind="args">
                External Link
            </AtomsLink>
        `,
  }),
}

export const Disabled: Story = {
  args: {
    to: '/disabled',
    disabled: true,
  },
  render: args => ({
    components: { AtomsLink },
    setup() {
      return { args }
    },
    template: `
            <AtomsLink v-bind="args">
                Disabled Link
            </AtomsLink>
        `,
  }),
}

// Secondary Hierarchy Links
export const SecondaryInternal: Story = {
  args: {
    to: '/secondary-internal',
    hierarchy: 'secondary',
  },
  render: args => ({
    components: { AtomsLink },
    setup() {
      return { args }
    },
    template: `
            <AtomsLink v-bind="args">
                Secondary Internal Link
            </AtomsLink>
        `,
  }),
}

export const SecondaryExternal: Story = {
  args: {
    to: 'https://example.com',
    external: true,
    hierarchy: 'secondary',
  },
  render: args => ({
    components: { AtomsLink, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
            <AtomsLink v-bind="args">
                Secondary External Link
            </AtomsLink>
        `,
  }),
}

export const SecondaryDisabled: Story = {
  args: {
    to: '/secondary-disabled',
    disabled: true,
    hierarchy: 'secondary',
  },
  render: args => ({
    components: { AtomsLink },
    setup() {
      return { args }
    },
    template: `
            <AtomsLink v-bind="args">
                Secondary Disabled Link
            </AtomsLink>
        `,
  }),
}

// Links with Icons
export const LeftIcon: Story = {
  args: {
    to: '/left-icon',
  },
  render: args => ({
    components: { AtomsLink, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
            <AtomsLink v-bind="args">
                <AtomsIcon name="NavArrowLeft" />
                Link with Left Icon
            </AtomsLink>
        `,
  }),
}

export const RightIcon: Story = {
  args: {
    to: '/right-icon',
  },
  render: args => ({
    components: { AtomsLink, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
            <AtomsLink v-bind="args">
                Link with Right Icon
                <AtomsIcon name="NavArrowRight" />
            </AtomsLink>
        `,
  }),
}

export const BothIcons: Story = {
  args: {
    to: '/both-icons',
  },
  render: args => ({
    components: { AtomsLink, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
            <AtomsLink v-bind="args">
                <AtomsIcon name="NavArrowLeft" />
                Link with Both Icons
                <AtomsIcon name="NavArrowRight" />
            </AtomsLink>
        `,
  }),
}

// Links with Custom Styling
export const CustomStyled: Story = {
  args: {
    to: '/custom-class',
    class: 'text-blue-600 font-bold',
  },
  render: args => ({
    components: { AtomsLink, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
            <AtomsLink v-bind="args">
                Custom Styled Link
            </AtomsLink>
        `,
  }),
}

export const WithAriaLabel: Story = {
  args: {
    to: '/accessibility',
    ariaLabel: 'Link with custom accessibility label',
  },
  render: args => ({
    components: { AtomsLink, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
            <AtomsLink v-bind="args">
                Link with Custom ARIA Label
            </AtomsLink>
        `,
  }),
}

export const CompleteExample: Story = {
  args: {
    to: '/complete-example',
    class: 'text-green-600 font-medium',
    ariaLabel: 'Complete example with all features',
  },
  render: args => ({
    components: { AtomsLink, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
            <AtomsLink v-bind="args">
                Complete Example
            </AtomsLink>
        `,
  }),
}

// External Links
export const GitHubLink: Story = {
  args: {
    to: 'https://github.com',
    external: true,
  },
  render: args => ({
    components: { AtomsLink, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
            <AtomsLink v-bind="args">
                GitHub
            </AtomsLink>
        `,
  }),
}

export const DisabledExternal: Story = {
  args: {
    to: 'https://example.com',
    external: true,
    disabled: true,
  },
  render: args => ({
    components: { AtomsLink, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
            <AtomsLink v-bind="args">
                Disabled External Link
            </AtomsLink>
        `,
  }),
}

// Inverted Links
export const InvertedLink: Story = {
  render: () => ({
    components: { AtomsLink, AtomsIcon },
    template: `
      <div class="bg-hover p-2 py-4 rounded-md flex gap-2">
        <AtomsLink inverted to="/">
          Inverted Link
          <AtomsIcon name="NavChevronRight" />
        </AtomsLink>
      </div>
    `,
  }),
}

// Link arrow
export const LinkArrow: Story = {
  args: {
    to: '/right-icon',
  },
  render: args => ({
    components: { AtomsLink, AtomsIcon },
    setup() {
      return { args }
    },
    template: `
            <AtomsLink v-bind="args">
                Link Arrow
                <AtomsIcon name="NavChevronRight" class="ml-20" />
            </AtomsLink>
        `,
  }),
}
