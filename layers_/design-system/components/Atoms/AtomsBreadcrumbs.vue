<script setup lang="ts">
import type { AtomsBreadcrumbsProps } from './AtomsBreadcrumbs.props'

defineProps<AtomsBreadcrumbsProps>()
</script>

<template>
  <div class="breadcrumbs max-w-full">
    <nav
      class="flex items-center justify-start"
      aria-label="breadcrumbs"
    >
      <ol class="flex p-padding-xxs body-2 whitespace-nowrap overflow-hidden *:not-last:overflow-hidden">
        <li
          v-for="(item, index) in items"
          :key="item.url ?? `page-${item.label}`"
          class="*:not-last:text-ellipsis *:not-last:overflow-hidden"
          :class="[{
            'text-placeholder': index === items.length - 1,
          }, 'flex items-center text-neutral']"
        >
          <template v-if="index < items.length - 1">
            <NuxtLink v-if="item.url" :to="item.url" dir="rtl">
              {{ item.label }}
            </NuxtLink>
            <span v-else>
              {{ item.label }}
            </span>
            <AtomsIcon name="NavChevronRight" class="!h-6 !w-6 !shrink-0" aria-hidden="true" />
          </template>
          <template v-else>
            <span> {{ item.label }} </span>
          </template>
        </li>
      </ol>
    </nav>
  </div>
</template>
