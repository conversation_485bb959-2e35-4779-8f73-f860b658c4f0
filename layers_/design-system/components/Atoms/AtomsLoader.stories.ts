import type { Meta, StoryObj } from '@storybook/vue3'
import AtomsLoader from './AtomsLoader.vue'

const meta = {
  title: 'Atoms/AtomsLoader',
  component: AtomsLoader,
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'inverted'],
      description: 'The visual style variant of the loader',
      defaultValue: 'default',
    },
    class: {
      control: 'text',
      description: 'Additional CSS classes to apply to the loader',
    },
  },
} satisfies Meta<typeof AtomsLoader>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    variant: 'default',
  },
}

export const Inverted: Story = {
  args: {
    variant: 'inverted',
  },
  parameters: {
    backgrounds: {
      default: 'dark',
      values: [
        { name: 'dark', value: '#1a1a1a' },
        { name: 'light', value: '#ffffff' },
      ],
    },
  },
}

export const CustomWidth: Story = {
  args: {
    variant: 'default',
    class: 'w-64',
  },
}
