<script setup lang="ts">
import type { SidebarButtonProps } from './AtomsSidebarButton.props'

defineProps<SidebarButtonProps>()
</script>

<template>
  <button
    :class="[
      'group w-full bg-transparent text-accent hover:text-hover hover:bg-light py-padding-xs cursor-pointer',
      { 'border-b border-neutral rounded-r-extra-small text-neutral': isExpanded },
    ]"
    :aria-expanded="isExpanded"
    :aria-label="title"
  >
    <div class="flex items-center gap-xxs">
      <AtomsIcon :name="isExpanded ? 'GenFolderOpened' : 'GenFolderIcon'" class="group-hover:text-icon-hover text-[2rem] shrink-0" aria-hidden="true" />
      <div class="grow subtitle-3 text-start *:line-clamp-1">
        <p>
          {{ title }}
        </p>
        <p v-if="subtitle">
          {{ subtitle }}
        </p>
      </div>
      <AtomsIcon
        v-if="hasChildren"
        :name="isExpanded ? 'NavChevronDown' : 'NavChevronUp'"
        class="group-hover:text-icon-hover text-[2rem] shrink-0"
        aria-hidden="true"
      />
    </div>
  </button>
</template>
