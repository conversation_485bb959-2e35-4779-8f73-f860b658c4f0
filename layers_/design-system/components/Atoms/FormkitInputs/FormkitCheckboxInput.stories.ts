import type { <PERSON>a, StoryObj } from '@storybook/vue3'
import { FormKit } from '@formkit/vue'

const meta: Meta<typeof FormKit> = {
  title: 'Formkit/Checkbox',
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=2152-8834&t=qbaPW3ITWW8oxkvS-0',
      },
    ],
  },
  decorators: [
    () => ({
      template: '<div class="flex flex-col gap-8 max-w-[400px] p-10"><story/></div>',
    }),
  ],
}

export default meta
type Story = StoryObj<typeof FormKit>

export const Default: Story = {
  render: args => ({
    setup() {
      const selectedFormValue = ref({
        singleCheckbox: true,
      })
      const disabledFormValue = ref({
        singleDisabledCheckbox: false,
      })
      const multiple = ref<string[]>(['option-3'])
      return { args, selectedFormValue, disabledFormValue, multiple }
    },
    components: { FormKit },
    template: `
      <div>
        <p>Default</p>
        <FormKit
          type="checkbox"
          decorator-icon="myCheckbox"
          :options="[{ value: false, label: 'Lorem', help: 'Lorem ipsum dolor sit amet' }]"
        />
      </div>

      <div>
        <p>Selected</p>
        <FormKit v-model="selectedFormValue" type="form" :actions="false">
        <FormKit
          type="checkbox"
          name="singleCheckbox"
          label="Lorem"
          help="Lorem ipsum dolor sit amet"
          decorator-icon="myCheckbox"
        />
      </FormKit> 
      </div>
      
      <div>
        <p>Disabled</p>
         <FormKit v-model="disabledFormValue" type="form" :actions="false">
        <FormKit
          type="checkbox"
          name="singleDisabledCheckbox"
          label="Lorem"
          help="Lorem ipsum dolor sit amet"
          :disabled="true"
          decorator-icon="myCheckbox"
        />
      </FormKit> 
      </div>
      
      <div>
        <p>Inactive</p>
        <FormKit
        type="checkbox"
        name="disabled"
        label="Label"
        help="Lorem ipsum dolor sit amet"
        decorator-icon="myCheckbox"
        :disabled="true"
        :value="true"
        />
      </div>
        
      <div>
       <p>Multiple</p>
        <FormKit
          v-model="multiple"
          type="checkbox"
          decorator-icon="myCheckbox"
          :options="[
            {
              value: 'option-1',
              label: 'Lorem 1',
            },
            {
              value: 'option-2',
              label: 'Lorem 2',
              attrs: { disabled: true },
              },
              {
                value: 'option-3',
                label: 'Lorem 3',
                attrs: { disabled: true },
            },
            {
              value: 'option-4',
              label: 'Lorem 4',
            },
          ]"
        />
       <pre wrap>{{ multiple }}</pre>
      </div>
    `,
  }),
}
