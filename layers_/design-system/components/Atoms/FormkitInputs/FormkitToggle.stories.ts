/**
 * This file documents the Toggle component styled in the current PR.
 * It demonstrates various states of the toggle component using FormKit.
 * The styling updates include modifications to track width, thumb dimensions, and color schemes as per the diff in layers_/forms/formkit.theme.ts.
 *
 * Usage:
 * - Default state: toggling on/off computed via v-model.
 * - Disabled state: toggle component disabled.
 * - WithHelp: toggle component with help text.
 */

import type { Meta, StoryObj } from '@storybook/vue3'
import { FormKit } from '@formkit/vue'
import { ref } from 'vue'

const meta: Meta<typeof FormKit> = {
  title: 'Formkit/Toggle',
  parameters: {
    design: [
      {
        name: 'Toggle',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=4495-2128&m=dev',
      },
    ],
  },
  decorators: [
    () => ({
      template: '<div class="flex flex-col gap-8 max-w-[400px] p-10"><story/></div>',
    }),
  ],
}

export default meta

type Story = StoryObj<typeof FormKit>

export const Default: Story = {
  render: args => ({
    components: { FormKit },
    setup() {
      const toggle = ref(false)
      return { args, toggle }
    },
    template: `
      <div>
        <FormKit
          type="toggle"
          name="defaultToggle"
          v-model="toggle"
          label="Airplane mode"
        />
        <pre>{{ toggle }}</pre>
      </div>
    `,
  }),
}

export const Disabled: Story = {
  render: args => ({
    components: { FormKit },
    setup() {
      const toggle = ref(false)
      return { args, toggle }
    },
    template: `
      <div>
        <FormKit
          type="toggle"
          name="disabledToggle"
          v-model="toggle"
          label="Airplane mode"
          :disabled="true"
        />
        <pre>{{ toggle }}</pre>
      </div>
    `,
  }),
}
