import type { Meta, StoryObj } from '@storybook/vue3'
import { FormKit } from '@formkit/vue'
import { ref } from 'vue'

const meta: Meta<typeof FormKit> = {
  title: 'Formkit/Select',
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=2152-11466&m=dev',
      },
    ],
  },
  decorators: [
    () => ({
      template: '<div class="flex flex-col gap-8 max-w-[400px] p-10"><story/></div>',
    }),
  ],
}

export default meta

type Story = StoryObj<typeof FormKit>

export const Default: Story = {
  render: args => ({
    components: { FormKit },
    setup() {
      const select = ref('')
      const options = [
        { label: 'React', value: 'react' },
        { label: 'Vue', value: 'vue' },
        { label: 'Angular', value: 'angular' },
        { label: 'Svelte', value: 'svelte' },
      ]
      return { args, select, options }
    },
    template: `
      <div>
        <FormKit
          type="dropdown"
          name="defaultSelect"
          v-model="select"
          label="Select"
          placeholder="Choose an option"
          :options="options"
          select-icon="NavChevronDown"
          selected-icon="NavCheckIcon"
          help="This is a default select component"
        />
        <pre>{{ select }}</pre>
      </div>
    `,
  }),
}

export const WithSelection: Story = {
  render: args => ({
    components: { FormKit },
    setup() {
      const select = ref('vue')
      const options = [
        { label: 'React', value: 'react' },
        { label: 'Vue', value: 'vue' },
        { label: 'Angular', value: 'angular' },
        { label: 'Svelte', value: 'svelte' },
      ]
      return { args, select, options }
    },
    template: `
      <div>
        <FormKit
          type="dropdown"
          name="selectedSelect"
          v-model="select"
          label="Select"
          placeholder="Choose an option"
          :options="options"
          select-icon="NavChevronDown"
          selected-icon="NavCheckIcon"
          help="This select has a pre-selected value"
        />
        <pre>{{ select }}</pre>
      </div>
    `,
  }),
}

export const Disabled: Story = {
  render: args => ({
    components: { FormKit },
    setup() {
      const select = ref('')
      const options = [
        { label: 'React', value: 'react' },
        { label: 'Vue', value: 'vue' },
        { label: 'Angular', value: 'angular' },
        { label: 'Svelte', value: 'svelte' },
      ]
      return { args, select, options }
    },
    template: `
      <div>
        <FormKit
          type="dropdown"
          name="disabledSelect"
          v-model="select"
          label="Select"
          placeholder="Choose an option"
          :options="options"
          select-icon="NavChevronDown"
          selected-icon="NavCheckIcon"
          help="This select is disabled"
          :disabled="true"
        />
        <pre>{{ select }}</pre>
      </div>
    `,
  }),
}

export const Error: Story = {
  render: args => ({
    components: { FormKit },
    setup() {
      const select = ref('')
      const options = [
        { label: 'React', value: 'react' },
        { label: 'Vue', value: 'vue' },
        { label: 'Angular', value: 'angular' },
        { label: 'Svelte', value: 'svelte' },
      ]
      return { args, select, options }
    },
    template: `
      <div>
        <FormKit
          type="dropdown"
          name="errorSelect"
          v-model="select"
          label="Select"
          placeholder="Choose an option"
          :options="options"
          select-icon="NavChevronDown"
          selected-icon="NavCheckIcon"
          :errors="['This is an error']"
        />
        <pre>{{ select }}</pre>
      </div>
    `,
  }),
}
