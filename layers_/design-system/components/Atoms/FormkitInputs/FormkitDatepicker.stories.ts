/**
 * This file documents the Datepicker component added in the current PR.
 * The PR updated the FormKit configuration (in layers_/forms/formkit.config.ts) to include the datepicker plugin,
 * enabling the datepicker component with overlay, popover, and custom formatting options (e.g., "DD/MM/YYYY").
 * This story demonstrates the default usage of the datepicker within a FormKit form.
 */

import type { Meta, StoryObj } from '@storybook/vue3'
import { FormKit } from '@formkit/vue'

const meta: Meta<typeof FormKit> = {
  title: 'Formkit/Datepicker',
  parameters: {
    design: [
      {
        name: 'Datepicker',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=4080-5733&m=dev',
      },
    ],
  },
  decorators: [
    () => ({
      template: '<div class="flex flex-col gap-8 max-w-[400px] p-10"><story/></div>',
    }),
  ],
}

export default meta

type Story = StoryObj<typeof FormKit>

export const Default: Story = {
  render: args => ({
    components: { FormKit },
    setup() {
      // The form wrapper will collect values from the datepicker component
      return { args }
    },
    template: `
      <FormKit v-slot="{ value }" type="form" :actions="false">
        <FormKit
          type="datepicker"
          name="date"
          label="Appointment date"
          help="Select a date to book your appointment."
          overlay
          popover
          format="DD/MM/YYYY"
        />
        <pre>{{ value }}</pre>
      </FormKit>
    `,
  }),
}
