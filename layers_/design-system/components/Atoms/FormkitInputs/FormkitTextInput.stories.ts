import type { Meta, StoryObj } from '@storybook/vue3'
import { FormKit } from '@formkit/vue'

const meta: Meta<typeof FormKit> = {
  title: 'Formkit/Text Input',
  tags: ['autodoc'],
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=2121-56',
      },
    ],
  },
  argTypes: {
    label: {
      control: 'text',
    },
    placeholder: {
      control: 'text',
    },
    help: {
      control: 'text',
    },
    disabled: {
      control: 'boolean',
    },
    validation: {
      control: 'select',
      options: ['required', 'alpha_spaces'],
    },
    showValidationIcon: {
      control: 'boolean',
      description: 'Show the suffix icon with validation status',
    },
  },
  decorators: [
    () => ({
      template: '<div class="flex flex-col gap-8 max-w-[400px]"><story/></div>',
    }),
  ],
}

export default meta
type Story = StoryObj<typeof FormKit>

export const Default: Story = {
  render: args => ({
    setup() {
      const text = ref('')

      return { args, text }
    },
    components: { FormKit },
    template: `
      <div>
        <p>Default</p>
        <FormKit
          v-model="text"
          v-bind="args"
          type="text"
          name="default"
        /> 
        <pre>{{ text }}</pre>
      </div>
    `,
  }),
  args: {
    label: 'Value',
    placeholder: 'Placeholder',
    help: 'Not mandatory helper text',
    disabled: false,
    validation: 'required',
    showValidationIcon: true,
  },
}

export const Disabled: Story = {
  render: args => ({
    setup() {
      const text = ref('')

      return { args, text }
    },
    components: { FormKit },
    template: `
      <div>
        <p>Disabled</p>
        <FormKit
          v-model="text"
          v-bind="args"
          type="text"
          name="default"
        /> 
        <pre>{{ text }}</pre>
      </div>
    `,
  }),
  args: {
    label: 'Value',
    placeholder: 'Placeholder',
    disabled: true,
  },
}
