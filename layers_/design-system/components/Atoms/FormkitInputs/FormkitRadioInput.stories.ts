import type { Meta, StoryObj } from '@storybook/vue3'
import { FormKit } from '@formkit/vue'

const meta: Meta<typeof FormKit> = {
  title: 'Formkit/Radio',
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=4028-1779&m=dev',
      },
    ],
  },
  decorators: [
    () => ({
      template: '<div class="flex flex-col gap-8 max-w-[400px] p-10"><story/></div>',
    }),
  ],
}

export default meta
type Story = StoryObj<typeof FormKit>

export const Default: Story = {
  render: args => ({
    setup() {
      const defaultdRadio = ref('not-selected')
      const selectedRadio = ref('selected')
      const inactiveRadio = ref('inactive')
      const radio = ref('option1')
      return { args, radio, inactiveRadio, selectedRadio, defaultdRadio }
    },
    components: { FormKit },
    template: `
      <div>
        <p>Default</p>
        <FormKit
          type="radio"
          name="default"
          v-model="defaultRadio"
         :options="[
          { label: 'Lorem', help: 'Lorem ipsum dolor sit amet', value: 'default'},]"
        /> 
      </div>

      <div>
        <p>Selected</p>
        <FormKit
          type="radio"
          name="selected"
          v-model="selectedRadio"
          :options="[
          { label: 'Lorem', help: 'Lorem ipsum dolor sit amet', value: 'selected'},]"
        />  
      </div>
      
      <div>
        <p>Disabled</p>
        <FormKit
          type="radio"
          name="disabled"
          label="Label"
          help="Lorem ipsum dolor sit amet"
          :value="false"
          :disabled="true"
          />
      </div>
      
      <div>
        <p>Inactive</p>
        <FormKit
          type="radio"
          name="disabled"
          v-model="inactiveRadio"
          :disabled="true"
          :options="[
          { label: 'Lorem', help: 'Lorem ipsum dolor sit amet', value: 'inactive', attrs: { disabled: true } },]"
          />
      </div>
      <div>
        <p>Options</p>
        <FormKit
        v-model="radio"
          type="radio"
           :options="[
          { label: 'Lorem 1', help: 'Lorem ipsum dolor sit amet', value: 'option1', attrs: { disabled: true } },
          { label: 'Lorem 2', help: 'Lorem ipsum dolor sit amet', value: 'option2' },
          { label: 'Lorem 3', help: 'Lorem ipsum dolor sit amet', value: 'option3' },
          ]"
          />
          <pre>{{ radio }}</pre>
      </div>
    `,
  }),
}
