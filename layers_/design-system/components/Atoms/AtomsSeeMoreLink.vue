<script setup lang='ts'>
defineProps<{ totalData?: number }>()

const nItemsToShow = defineModel<number>('nItemsToShow', {
  default: 10,
})
</script>

<template>
  <div v-if="totalData && totalData > 10">
    <AtomsLink v-if="nItemsToShow < totalData" @click.prevent="nItemsToShow += 10">
      {{ $t('atoms.seeMoreLink.seeMoreItems', { totalItems: totalData }) }}
    </AtomsLink>
    <AtomsLink v-else @click.prevent="nItemsToShow = 10">
      {{ $t('atoms.seeMoreLink.seeLess') }}
    </AtomsLink>
  </div>
</template>
