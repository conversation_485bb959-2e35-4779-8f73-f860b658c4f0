<script setup lang='ts'>
import type { AtomsButtonProps } from './AtomsButton.props'
import type { AtomsFooterButtonProps } from './AtomsFooterButton.props'

const props = defineProps<AtomsFooterButtonProps>()

const footerButtonProos = computed<AtomsButtonProps>(() => ({
  anatomy: 'secondary',
  size: 'l',
  as: 'link',
  to: props.to,
  state: props.state,
  ariaLabel: props.ariaLabel,
  ariaDescription: props.ariaDescription,
}))
</script>

<template>
  <AtomsButton
    v-bind="footerButtonProos"
    class="!rounded-extra-small !p-padding-xs min-h-auto section-1 uppercase text-nowrap hover:not-disabled:bg-hover hover:not-disabled:text-invert"
  >
    <slot>
      {{ props.label }}
    </slot>
  </AtomsButton>
</template>
