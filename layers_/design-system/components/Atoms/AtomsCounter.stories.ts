import type { Meta, StoryObj } from '@storybook/vue3'
import AtomsCounter from './AtomsCounter.vue'

const meta: Meta<typeof AtomsCounter> = {
  title: 'Atoms/Counter',
  component: AtomsCounter,
  tags: ['autodoc'],
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=2152-294',
      },
    ],
  },
  argTypes: {
    name: {
      control: 'text',
    },
    minValue: {
      control: 'number',
    },
    maxValue: {
      control: 'number',
    },
    disabled: {
      control: 'boolean',
    },
    validation: {
      control: 'text',
    },
    validationRules: {
      control: 'text',
    },
    validationMessages: {
      control: 'text',
    },
    validationVisibility: {
      control: 'select',
      options: ['blur', 'live', 'dirty', 'submit'],
    },
    errors: {
      control: 'text',
    },
  },
  args: {
    name: 'default',
    maxValue: 8,
    disabled: false,
    validation: 'required',
    validationVisibility: 'blur',
  },
  decorators: [
    () => ({
      template: '<div class="flex flex-col gap-8 max-w-[400px]"><story/></div>',
    }),
  ],
}

export default meta
type Story = StoryObj<typeof AtomsCounter>

export const Default: Story = {
  render: args => ({
    components: { AtomsCounter },
    setup() {
      const value = ref()

      return { args, value }
    },
    template: `
      <AtomsCounter v-model:value="value" v-bind="args" />
      <pre>{{value}}</pre>
    `,
  }),
}

export const Disabled: Story = {
  render: args => ({
    components: { AtomsCounter },
    setup() {
      const value = ref()

      return { args, value }
    },
    template: `
      <AtomsCounter v-model:value="value" v-bind="args" />
      <pre>{{value}}</pre>
    `,
  }),
  args: {
    disabled: true,
  },
}
