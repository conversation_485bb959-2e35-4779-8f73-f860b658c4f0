<script setup lang="ts">
import type { AtomsFiltersButtonProps } from './AtomsFiltersButton.props'

const props = defineProps<AtomsFiltersButtonProps>()

defineEmits<{
  (e: 'onClick'): void
}>()

const chipTw = tv({
  slots: {
    containerTv: [
      'group w-fit pl-padding-xs pr-padding-sm py-padding-xxs',
      'flex flex-nowrap justify-center items-center gap-x-gap-xxs',
      'bg-light border-accent rounded-sm',
      'focus-visible:outline-2 outline-offset-4 outline-(--color-stroke-focus)',
    ],
    iconTv: '!w-6 !h-6 !flex !items-center text-accent',
    labelTv: 'text-cta-md font-bold text-accent text-nowrap overflow-hidden',
    suffixCounterTv: 'text-caption-sm text-invert bg-accent w-5 h-5 rounded-max flex items-center justify-center',
  },
  variants: {
    disabled: {
      true: {
        containerTv: 'bg-disabled border-transparent focus:outline-none',
        labelTv: 'text-disabled',
        suffixCounterTv: 'hidden',
      },
      false: {
        iconTv: 'group-hover:text-hover',
        labelTv: 'group-hover:text-hover',
        suffixCounterTv: 'group-hover:bg-hover',
      },
    },
  },
  defaultVariants: {
    disabled: false,
  },
})

const { containerTv, iconTv, labelTv, suffixCounterTv } = chipTw({ ...props })
</script>

<template>
  <button
    :class="containerTv({ disabled })"
    :disabled="disabled"
    @click="$emit('onClick')"
  >
    <AtomsIcon name="NavFilter" :class="iconTv()" />

    <span :class="labelTv({ disabled })">{{ label }}</span>

    <span
      v-if="suffixCounter"
      :class="suffixCounterTv({ disabled })"
    >
      {{ suffixCounter }}
    </span>
  </button>
</template>
