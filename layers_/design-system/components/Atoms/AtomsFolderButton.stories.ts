import type { Meta, StoryObj } from '@storybook/vue3'
import AtomsFolderButton from './AtomsFolderButton.vue'

const meta: Meta<typeof AtomsFolderButton> = {
  title: 'Atoms/FolderButton',
  component: AtomsFolderButton,
  parameters: {
    viewport: { defaultViewport: 'desktop' },
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=6089-51569&p=f&t=GsqCuX3PNxbRCXBB-0',
      },
    ],
  },
}

export default meta
type Story = StoryObj<typeof AtomsFolderButton>

export const Mobile: Story = {
  parameters: {
    viewport: { defaultViewport: 'iphone14' },
  },
  render: args => ({
    components: { AtomsFolderButton },
    setup() {
      return { args }
    },
    template: `
    <div class="bg-brand">
      <AtomsFolderButton v-bind="args" @on-click="console.log('Hello World')"/>
    </div>
    `,
  }),
  args: {
    name: 'Folder Name',
    lastUpdate: '2025/01/01',
    text: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do.',
  },
}

export const Desktop: Story = {
  render: args => ({
    components: { AtomsFolderButton },
    setup() {
      return { args }
    },
    template: `
    <div class="p-10 w-[500px]" >
      <AtomsFolderButton v-bind="args" @on-click="console.log('Hello World')"/>
    </div>
    `,
  }),
  args: {
    name: 'Folder Name',
    lastUpdate: '2025/01/01',
    text: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do.',
  },
}
