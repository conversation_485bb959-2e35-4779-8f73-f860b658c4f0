import type { Meta, StoryObj } from '@storybook/vue3'
import AtomsFooterButton from './AtomsFooterButton.vue'

// Define metadata for the component
const meta = {
  title: 'Atoms/FooterButton',
  component: AtomsFooterButton,
  tags: ['autodocs'],
  argTypes: {
    state: {
      control: 'select',
      options: ['default', 'disabled'],
      description: 'The state of the footer button',
    },
    onClick: { action: 'clicked' },
  },
  args: {
    state: 'default',
    to: '/',
  },
} satisfies Meta<typeof AtomsFooterButton>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    state: 'default',
  },
  render: args => ({
    components: { AtomsFooterButton },
    setup() {
      return { args }
    },
    template: `
      <AtomsFooterButton v-bind="args">
        Programm Completion
      </AtomsFooterButton>
    `,
  }),
}

export const Disabled: Story = {
  args: {
    state: 'disabled',
  },
  render: args => ({
    components: { AtomsFooterButton },
    setup() {
      return { args }
    },
    template: `
      <AtomsFooterButton v-bind="args">
        Programm Completion
      </AtomsFooterButton>
    `,
  }),
}
