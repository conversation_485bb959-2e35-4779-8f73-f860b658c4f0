<script setup lang="ts">
import { fileExtentionsList, iconMap, type AtomsFileButtonProps, type FileType } from './AtomsFileButton.props'

const props = withDefaults(defineProps<AtomsFileButtonProps>(), {
  status: 'none',
  showFavorite: false,
  isFavorite: false,
})

defineEmits<(e: 'onClick' | 'onFavoriteClick') => void>()

const fileTitle = props.src?.split('/')?.at(-1) ?? props.id

const isMappedFileType = (fileName: string): fileName is FileType => fileExtentionsList.includes(fileName)

const extention = props.src?.split('.')?.at(-1) ?? ''
const fileType = isMappedFileType(extention) ? extention : 'pdf'

const iconName = computed(() => iconMap[fileType] ?? iconMap['pdf'])
</script>

<template>
  <NuxtLink
    v-if="src"
    class="block group w-full bg-neutral text-accent hover:text-invert hover:bg-hover px-padding-md py-padding-xs lg:hover:bg-hover lg:text-neutral lg:hover:text-invert lg:p-padding-sm lg:shadow-100 lg:rounded-sm  cursor-pointer"
    :to="src"
    target="_blank"
    :download="fileType !== 'pdf'"
    :aria-label="`Download file ${fileTitle}`"
  >
    <div class="flex items-center gap-xxs">
      <div class="grow flex lg:flex-wrap gap-xxs items-center">
        <AtomsIcon :name="iconName" class="shrink-0 group-hover:!text-icon-invert text-icon-accent lg:text-icon-neutral !w-13 !h-13" />
        <div class="flex flex-col gap-xxs lg:contents">
          <p class="grow subtitle-2 lg:text-subtitle-sm text-start max-w-none lg:max-w-[calc(100%-56px)] break-all leading-normal">
            {{ fileTitle }}
          </p>
          <p class="w-full body-2 text-start leading-normal">
            Last update: {{ lastUpdate }}
          </p>
          <p v-if="status !== 'none' && statusDate" :class="[status === 'effective' ? 'text-success' : 'text-alert', 'w-full leading-normal group-hover:!text-invert text-start body-2 font-bold']">
            {{ status === 'effective' ? 'Effective from' : 'Expired since' }} {{ statusDate }}
          </p>
        </div>
      </div>
      <div v-if="showFavorite">
        <AtomsIcon :name="isFavorite ? 'GenFavAdded' : 'GenFavIcon'" class="group-hover:!text-icon-invert text-icon-warning !w-8 !h-8" @click.stop="$emit('onFavoriteClick')" />
      </div>
    </div>
  </NuxtLink>
</template>
