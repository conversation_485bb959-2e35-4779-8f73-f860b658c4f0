import type { Meta, StoryObj } from '@storybook/vue3'
import AtomsFiltersButton from './AtomsFiltersButton.vue'

const meta: Meta<typeof AtomsFiltersButton> = {
  title: 'Atoms/FiltersButton',
  component: AtomsFiltersButton,
  tags: ['autodoc'],
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=2146-1031',
      },
    ],
  },
  argTypes: {
    label: {
      control: 'text',
      description: 'The label inside the button',
    },
    suffixCounter: {
      control: 'number',
      description: 'number of active filters',
    },
    disabled: {
      control: 'boolean',
      description: 'Disable the button',
    },
  },
  args: {
    label: 'Filters',
    suffixCounter: 3,
    disabled: false,
  },
}

export default meta
type Story = StoryObj<typeof AtomsFiltersButton>

export const Default: Story = {
  args: {
    label: 'Filters',
    suffixCounter: 3,
    disabled: false,
  },
  render: args => ({
    components: { AtomsFiltersButton },
    setup() {
      return { args }
    },
    template: `
    <div class="flex flex-col gap-4" >
      <AtomsFiltersButton v-bind=args />
    </div>
    `,
  }),
}
