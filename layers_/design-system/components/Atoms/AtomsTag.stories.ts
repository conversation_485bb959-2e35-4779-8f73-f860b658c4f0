import type { Meta, StoryObj } from '@storybook/vue3'
import AtomsTag from './AtomsTag.vue'

const meta: Meta<typeof AtomsTag> = {
  title: 'Atoms/Tag',
  component: AtomsTag,
  tags: ['autodoc'],
  parameters: {
    design: [
      {
        name: 'All',
        type: 'figma',
        url: 'https://www.figma.com/design/QFQQRcaMBBXyaz8ZjyjeaY/Dealer-Portal---Design-system?node-id=4576-1350',
      },
    ],
  },
}

export default meta
type Story = StoryObj<typeof AtomsTag>

export const Default: Story = {
  render: args => ({
    components: { AtomsTag },
    setup() {
      return { args }
    },
    template: `
    <div class="flex flex-col gap-4" >
      <AtomsTag label="Tag" />
    </div>
    `,
  }),
}
