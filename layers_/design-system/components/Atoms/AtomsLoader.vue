<script setup lang="ts">
import type { AtomsLoaderProps } from './AtomsLoader.props'

const props = withDefaults(defineProps<AtomsLoaderProps>(), {
  variant: 'default',
})

const loader = tv({
  slots: {
    base: 'rounded-full block h-2 overflow-hidden relative',
    runner: 'rounded-full animate-run h-full absolute bg-accent',
  },
  variants: {
    variant: {
      default: {
        runner: 'bg-accent',
      },
      inverted: {
        runner: 'bg-neutral',
      },
    },
  },
})

const { base, runner } = loader({ ...props })
</script>

<template>
  <div :class="base({ variant, class: props.class })">
    <div :class="runner({ variant })" />
    <span class="sr-only">Loading, please wait...</span>
  </div>
</template>
