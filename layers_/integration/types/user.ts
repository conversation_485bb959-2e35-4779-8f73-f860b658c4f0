import * as v from 'valibot'

const AddressSchema = v.object({
  city: v.string(),
  country_code: v.string(),
  line_1: v.string(),
  line_2: v.optional(v.string()),
  state_code: v.string(),
  zip_code: v.string(),
})

const UserMetadataSchema = v.object({
  address: AddressSchema,
  phone_number: v.string(),
  salesperson_id: v.string(),
})

const RolesSchema = v.array(v.string())

export const UserSchema = v.object({
  'email': v.string(),
  'email_verified': v.boolean(),
  'name': v.string(),
  'nickname': v.string(),
  'picture': v.string(),
  'roles': RolesSchema,
  'sub': v.string(),
  'updated_at': v.string(),
  'https://dealer-portals.com/user_metadata': UserMetadataSchema,
  'https://modernfold.com/roles': RolesSchema,
})

export type UserType = v.InferOutput<typeof UserSchema>
export type UserMetadataType = v.InferOutput<typeof UserMetadataSchema>
