import * as v from 'valibot'

export const changePasswordSchema = v.object({
  password: v.pipe(
    v.string(),
    v.minLength(8, 'Your password must contain at least 8 characters.'),
    v.regex(/[a-z]/, 'Your password must contain a lowercase letter.'),
    v.regex(/[A-Z]/, 'Your password must contain an uppercase letter.'),
    v.regex(/[0-9]/, 'Your password must contain a number.'),
  ),
})

export const passwordFormSchema = v.object({
  password: v.string(),
  password_confirm: v.string(),
})
