import type { MultilinkStoryblok } from '../storyblok/data/components'

type Folder = {
  label: Record<string, string>
  slug: string
}

type Story = {
  label: Record<string, string>
  slug: string
  path: string
  uuid: string
}

export type Breadcrumb = {
  label: Record<string, string>
  link?: MultilinkStoryblok
}

export type BreadcrumbsItem = {
  label: string
  url?: string
}

export type BreadcrumbsProps = {
  breadcrumbs: Array<BreadcrumbsItem>
}

export type FolderMap = Map<string, Folder>
export type StoryMap = Map<string, Story>
export type Breadcrumbs = Record<string, Breadcrumb[]>
