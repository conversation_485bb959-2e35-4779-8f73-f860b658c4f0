export type TUser = {
  sub: string
  given_name: string
  family_name: string
  nickname: string
  name: string
  picture: string
  updated_at: string
  email: string
  email_verified: boolean
  user_metadata: { phone_number: string } | Record<string, unknwon>
  roles: string[]
}

declare module '#auth-utils' {
  interface UserSession {
    // Add your own fields
    user: TUser
    tokens: {
      access_token: string
      refresh_token: string
      // removed to reduce session size
      // id_token: string
      scope: string
      expires_in: number
      token_type: 'Bearer'
    }
  }
}

export {}
