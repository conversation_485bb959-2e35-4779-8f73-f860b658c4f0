import * as v from 'valibot'
import { UserSchema, type UserMetadataType, type UserType } from '../types/user'

export const useAuth0User = () => {
  const { user } = useUserSession()

  const init = async () => {
    const userValidation = v.safeParse(UserSchema, user.value)

    if (!userValidation.success) {
      console.error('Failed to validate user data', userValidation.issues)
    }

    const validatedUserData = userValidation.output as UserType

    return {
      data: {
        email: validatedUserData.email,
        name: validatedUserData.name,
        nickname: validatedUserData.nickname,
      },
      metadata: validatedUserData['https://dealer-portals.com/user_metadata'],
    }
  }

  const transform = ({ data, metadata }: Awaited<ReturnType<typeof init>>) => ({
    data: {
      email: data.email,
      name: data.name,
    },
    metadata: metadata ?? {},
  })

  const data = useState<ReturnType<typeof transform> | null>('userSettings', () => null)

  const initialize = async () => {
    data.value = transform(await init())
  }

  const userData = computed(() => data.value?.data)
  const userMetadata = computed<UserMetadataType | Record<string, never>>(() => data.value?.metadata ?? {})

  return {
    userData,
    userMetadata,
    initialize,
  }
}
