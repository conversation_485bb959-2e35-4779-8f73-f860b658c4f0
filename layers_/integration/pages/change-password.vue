<script setup lang="ts">
import * as v from 'valibot'
import { passwordFormSchema } from '../types/changePassword'
import type { SiteType } from '~~/env'

definePageMeta({
  layout: false,
})

const config = useRuntimeConfig()
const site = config.public.site as SiteType

const passwordChanged = ref(false)

const changePassword = async (data: unknown) => {
  const { password } = v.parse(passwordFormSchema, data)

  await $fetch(`/api/auth0/change-password`, {
    method: 'PATCH',
    body: {
      password,
    },
  })

  passwordChanged.value = true
  setTimeout(() => {
    // Redirect to login after 5 seconds
    return navigateTo('/auth0/logout', { external: true })
  }, 5000)
}

// password visibility control
const showPassword = ref(false)
const showPasswordConfirm = ref(false)
</script>

<template>
  <div class="bg-brand">
    <div class="container  min-h-screen flex flex-col justify-start items-center lg:justify-center pt-10 lg:pt-0">
      <div class="p-padding-md pt-padding-xxl md:px-padding-xl md:pb-padding-mb lg:p-padding-xxl space-y-padding-xxl bg-white w-full">
        <AtomsIcon :name="site === 'modernfold' ? 'BrandModernFoldColored': 'BrandSkyFoldColored'" class="h-full w-full md:w-1/2 px-padding-md md:px-0" mode="svg" />
        <div>
          <h2 class="headline-1 pb-padding-xxs">
            {{ $ts('changePassword.title') }}
          </h2>
          <p class="body-1">
            {{ $ts('changePassword.description') }}
          </p>
        </div>
        <FormKit
          v-slot="{ state: { valid } }"
          type="form"
          :actions="false"
          @submit="changePassword"
        >
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-padding-xxl mb-padding-xxl">
            <FormKit
              :type="showPassword ? 'text' : 'password'"
              name="password"
              :label="$ts('changePassword.newPassword')"
              :placeholder="$ts('changePassword.newPasswordPlaceholder')"
              :help="$ts('changePassword.helpText')"
              validation="required|length:8|contains_uppercase|contains_lowercase|contains_numeric"
              validation-visibility="blur"
              :validation-messages="{
                required: $ts('changePassword.validationMessages.required'),
                length: $ts('changePassword.validationMessages.length'),
                contains_uppercase: $ts('changePassword.validationMessages.containsUppercase'),
                contains_lowercase: $ts('changePassword.validationMessages.containsLowercase'),
                contains_numeric: $ts('changePassword.validationMessages.containsNumeric'),
              }"
              :suffix-icon="showPassword ? 'eye' : 'eyeClosed' "
              suffix-icon-class="text-icon-accent hover:text-icon-hover cursor-pointer"
              @suffix-icon-click="showPassword = !showPassword"
            />
            <FormKit
              :type="showPasswordConfirm ? 'text' : 'password'"
              name="password_confirm"
              :label="$ts('changePassword.confirmNewPassword')"
              :placeholder="$ts('changePassword.confirmNewPasswordPlaceholder')"
              validation="required|confirm"
              validation-visibility="blur"
              :validation-messages="{
                required: $ts('changePassword.validationMessages.required'),
                confirm: $ts('changePassword.validationMessages.confirm'),
              }"
              :suffix-icon="showPasswordConfirm ? 'eye' : 'eyeClosed' "
              suffix-icon-class="text-icon-accent hover:text-icon-hover cursor-pointer"
              @suffix-icon-click="showPasswordConfirm = !showPasswordConfirm"
            />
          </div>
          <AtomsButton class="w-full md:w-auto min-w-[184px] float-right" :state="valid ? 'default' : 'disabled'" type="submit">
            {{ $ts('changePassword.confirm') }}
          </AtomsButton>
        </FormKit>
      </div>
    </div>
    <MoleculesDialog
      v-model:open="passwordChanged"
      icon="NavCheckIcon"
      content-class="max-w-88 w-[90dvw]"
      :title="$ts('changePassword.successTitle')"
    >
      <p>{{ $ts('changePassword.successMessage') }}</p>
    </MoleculesDialog>
  </div>
</template>
