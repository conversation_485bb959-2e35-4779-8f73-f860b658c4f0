<script lang="ts" setup>
import type { MappedData } from '@integration/server/api/[locale]/storyblok/page/[...path].get'
import type { PagesHomepageProps } from '@integration/components/Pages/PagesHomepage.props'

const { $getLocale } = useI18n()

const { data, error } = await useFetch<MappedData>(`/api/${$getLocale()}/storyblok/page//`, {
  key: `storyblok-homepage-${$getLocale()}`,
})

if (error.value || !data.value) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Page Not Found',
    fatal: true,
  })
}

// Type assertion since we know this is the homepage route
const homepageData = data.value as { component: 'PagesHomepage' } & PagesHomepageProps

definePageMeta({
  layout: 'homepage',
})
</script>

<template>
  <LazyPagesHomepage v-bind="homepageData" />
</template>
