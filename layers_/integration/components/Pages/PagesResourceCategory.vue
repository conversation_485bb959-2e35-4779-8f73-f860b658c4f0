<script setup lang="ts">
import { withoutTrailingSlash } from 'ufo'
import type { PagesResourceCategoryProps } from './PagesResourceCategory.props'

type HierarchyTree = {
  id: string
  title: string
  subtitle: string
  folders: HierarchyTree[]
}

const props = defineProps<PagesResourceCategoryProps>()
const { currentPathWithoutLocale } = useLanguage()
const { localePath, getLocale } = useI18n()

const parentFullSlug = ref(props.fullSlug)
const query = ref('')
const page = ref(1)

const { data: hierarchy } = await useFetch(`/api/${getLocale()}/storyblok/resource-hierarchy/${currentPathWithoutLocale.value}`,
  {
    transform: (data) => {
      const mapFolder = (item: typeof data): HierarchyTree => ({
        id: item.full_slug,
        title: item.content.name ?? '',
        subtitle: item.content.description ?? '',
        folders: item.folders?.map(mapFolder),
      })

      return mapFolder(data)
    },
  })

const { data: resources, status } = useResources(parentFullSlug, query, page)

const handleCurrentFolderUpdate = async (id: string) => {
  // <site>/pages/
  console.log(id)
  // parentFullSlug.value = id
  await navigateTo(localePath('/' + (id).split('/').slice(3).join('/')))
}

const { currentFolder } = useCurrentPageFolder()

const getCurrentFolder = (): string[] => {
  if (!hierarchy.value?.id) return []
  const categorySegments = withoutTrailingSlash(hierarchy.value?.id)?.split('/')?.slice(3)
  const folderSegments = currentPathWithoutLocale.value.split('/').filter(item => item).slice(categorySegments.length)
  const currentFolder = scan(folderSegments, (folderId, segment) => `${folderId}${segment}/`, hierarchy.value.id)
  return currentFolder
}

currentFolder.value = getCurrentFolder()

const previousFolder = computed(() => currentFolder.value?.at(-2) ?? hierarchy.value?.id ?? '')
</script>

<template>
  <div class="bg-neutral min-h-full w-full flex">
    <div class="container grid-standard-full grid-rows-[min-content] lg:grid-rows-none relative">
      <div
        class="col-span-full lg:col-span-4 space-y-3 py-4 bg-brand -mx-(--grid-system-spacing-x) px-(--grid-system-spacing-x) lg:-mr-(--grid-system-gutter-x) lg:pr-(--grid-system-gutter-x) sidebar-after"
      >
        <div
          v-if="currentFolder.length"
          attribute="parent_full_slug"
          class="lg:hidden"
        >
          <AtomsLink @click="handleCurrentFolderUpdate(previousFolder)">
            <AtomsIcon name="NavArrowLeft" />
            {{ $t('assets.backTo') }} {{ previousFolder?.split('/')?.at(previousFolder.endsWith('/') ? -2 : -1) }}
          </AtomsLink>
        </div>

        <h1 :class="{ 'headline-1': true, 'hidden lg:block': currentFolder.length }">
          {{ hierarchy?.title }}
        </h1>
        <h3 :class="{ 'headline-3 hidden': true, '!block lg:!hidden': currentFolder.length }">
          {{ name }}
        </h3>

        <p :class="{ 'caption-1 hidden': true, '!block lg:!hidden': !currentFolder.length }">
          {{ hierarchy?.subtitle }}
        </p>

        <div>
          <OrganismsSearchBar v-model="query" variant="contextual" :placeholder="String($t('assets.searchPlaceholder'))" />
        </div>

        <div
          class="hidden lg:block"
        >
          <MoleculesSidebarFilesNavigation
            v-if="hierarchy"
            :folders="hierarchy.folders"
            @update:current-folder="handleCurrentFolderUpdate($event ?? hierarchy.id)"
          />
        </div>
      </div>

      <div class="col-span-full lg:col-span-8 lg:ml-0 -mx-(--grid-system-spacing-x) lg:mx-0">
        <Transition
          enter-active-class="transition duration-300"
          enter-from-class="opacity-0 transform translate-x-[1%]"
        >
          <div v-show="status === 'idle' || status === 'success'" class="lg:pl-(--spacing-margin) lg:py-padding-xl xl:pl-padding-xxl">
            <div>
              <div class="relative">
                <h4 v-if="name !== hierarchy?.title" class="hidden lg:block headline-4 col-span-full mb-padding-xs">
                  {{ name }}
                </h4>

                <MoleculesAssetsHits
                  :items="resources?.hits ?? []"
                  @update:current-folder="handleCurrentFolderUpdate($event)"
                />
              </div>
            </div>

            <div
              class="col-span-full mt-padding-xs"
            >
              <MoleculesPagination
                v-if="resources?.nbPages && resources.nbPages > 1"
                v-model="page"
                size="M"
                :total="resources.nbHits"
                :items-per-page="12"
              />
            </div>
          </div>
        </Transition>
      </div>
    </div>
  </div>
</template>

<style scoped>
.sidebar-after:after {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  left: 1px;
  width: calc(((100vw - var(--spacing-page-container))/2) + 2px);
  transform: translateX(-100%);
  min-height: 100%;
  background-color: var(--color-background-brand);
}
/* we will explain what these classes do next! */
</style>
