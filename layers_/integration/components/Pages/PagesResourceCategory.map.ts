import type { ISbStoryData } from 'storyblok-js-client'
import type { MappedContent } from '@integration/server/types/storyblok'
import type { ResourceCategoryStoryblok, ResourceFolderStoryblok } from '@integration/storyblok/data/components'
import type { WithRelationships } from '@integration/types/storyblok'
import type { PagesResourceCategoryProps } from './PagesResourceCategory.props.ts'

export const pagesResourceCategoryMap = (content: WithRelationships<ResourceCategoryStoryblok | ResourceFolderStoryblok>, story: ISbStoryData): MappedContent<'PagesResourceCategory', PagesResourceCategoryProps> => {
  return {
    component: 'PagesResourceCategory',
    name: content.name,
    fullSlug: story?.full_slug,
  }
}
