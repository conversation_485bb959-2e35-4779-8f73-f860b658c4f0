import type { HomepageStoryblok } from '@integration/storyblok/data/components'
import type { MappedContent } from '@integration/server/types/storyblok'
import type { WithRelationships } from '@integration/types/storyblok'
import type { PagesHomepageProps } from '@integration/components/Pages/PagesHomepage.props'

export const pagesHomepageMap = (content: WithRelationships<HomepageStoryblok>): MappedContent<'PagesHomepage', PagesHomepageProps> => {
  return {
    component: 'PagesHomepage',
    newsItems: whatIsNewItemsMap(content.what_is_new),
    seeAllNewsUrl: content.what_is_new_see_all?.cached_url || '#',
  }
}

const whatIsNewItemsMap = (items: WithRelationships<HomepageStoryblok>['what_is_new']) => {
  if (!items) return []

  return items.map((item) => {
    if (typeof item === 'string' || !('content' in item) || !item.content.asset) return null

    const asset = item.content.asset as { title?: string, filename: string }

    return ({
      title: asset.title || asset.filename,
      date: item.published_at || undefined,
      to: asset.filename,
    })
  }).filter(Boolean)
}
