<script lang="ts" setup>
import type { PagesHomepageProps } from './PagesHomepage.props'

defineProps<PagesHomepageProps>()
</script>

<template>
  <div class="container pb-10.5 pt-13 relative lg:min-h-120 lg:h-page h-full flex flex-col grow">
    <h1 class="p-4 sr-only">
      {{ $t('modernfold.index.title') }}
    </h1>
    <div class="lg:grid lg:grid-cols-12 lg:gapped max-h-full grow flex flex-col max-lg:relative">
      <div class="col-span-12 lg:col-span-8 xl:col-span-9 lg:relative grow">
        <OrganismsSearch variant="homepage" :placeholder="`${$ts('home.search.placeholder')}`" />
      </div>
      <div class="col-span-12 lg:col-span-4 xl:col-span-3 lg:max-xl:pl-padding-xxl pt-8 lg:max-h-full lg:overflow-y-auto *:w-full">
        <OrganismsCarouselCards :see-all-url="seeAllNewsUrl">
          <div
            v-for="(item, index) in newsItems"
            :key="`news-${index}`"
            class="max-lg:min-w-70 lg:w-full"
          >
            <OrganismsCardNews
              :title="item.title"
              :date="item.date"
              :to="item.to"
            />
          </div>
        </OrganismsCarouselCards>
      </div>
    </div>
  </div>
</template>
