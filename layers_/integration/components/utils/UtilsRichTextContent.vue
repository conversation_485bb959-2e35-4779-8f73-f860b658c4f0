<script setup lang="ts">
/**
 * @see https://www.storyblok.com/mp/announcing-official-storyblok-richtext-package
 * For advanced customization, consider using: https://www.storyblok.com/docs/guides/vue/
 */
import type { VNode } from 'vue'
import { createTextVNode, h } from 'vue'
import { MarkTypes, richTextResolver, type StoryblokRichTextNode, type StoryblokRichTextOptions } from '@storyblok/richtext'
import { cleanPageUrl } from '@integration/utils/cleanPageUrl'
import { AtomsLink } from '#components'

const props = defineProps<{
  content: StoryblokRichTextNode
  linkMeta?: {
    target?: '_blank' | '_self'
  }
}>()

const options: StoryblokRichTextOptions<VNode> = {
  renderFn: h,
  // @ts-expect-error - createTextVNode types has been recently changed. @see https://github.com/storyblok/storyblok-vue/blob/ac9e973a70346a00a70dfbebdf9f19d1e5ea682f/src/composables/useStoryblokRichText.ts#L28
  textFn: createTextVNode,
  keyedResolvers: true,
  resolvers: {
    [MarkTypes.LINK]: (node) => {
      let to = cleanPageUrl(node.attrs?.href ?? '')

      if (node.attrs?.linktype === 'email') {
        to = `mailto:${to}`
      }

      return h(AtomsLink, {
        to,
        target: props.linkMeta?.target ?? '_self',
        class: 'inline-block',
      }, node.text ?? '')
    },
  },
}
const root = () => richTextResolver<VNode>(options).render(props.content as unknown as StoryblokRichTextNode<VNode>)
</script>

<template>
  <root />
</template>
