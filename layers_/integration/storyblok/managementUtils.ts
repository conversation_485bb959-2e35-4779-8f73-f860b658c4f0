import type { ISbContentMangmntAPI, ISbResponseData, ISbStoriesParams, ISbStoryData } from 'storyblok-js-client'
import type { createClient } from './client'

export default (client: ReturnType<typeof createClient>, spaceId: string) => {
  const storyblokClient = client

  if (!storyblokClient) {
    throw new Error('Missing Storyblok client')
  }

  const getStory = async (id: number): Promise<ISbStoryData> => {
    try {
      const response = await storyblokClient.get(`spaces/${spaceId}/stories/${id}`)

      return response.data.story
    }
    catch (err) {
      console.error(`Failed to fetch story ${id}:`, err)
      throw err
    }
  }

  const getAllStories = async ({ options, page = 1, per_page = 1000, acc = [], trowError = false }: { options?: ISbStoriesParams, page?: number, per_page?: number, acc?: ISbStoryData[], trowError?: boolean }): Promise<ISbStoryData[]> => {
    try {
      const response = await storyblokClient.get(`spaces/${spaceId}/stories`, { ...options, page, per_page })

      const currentStories = response.data.stories ?? []
      const allStories = acc.concat(currentStories)

      if (currentStories.length < per_page) {
        return allStories
      }

      return getAllStories({ options, page: page + 1, acc: allStories })
    }
    catch (err) {
      if (trowError) {
        throw err
      }
      return []
    }
  }

  const getLanguages = async (): Promise<{ code: string, name: string }[]> => {
    const content = await storyblokClient.get(`spaces/${spaceId}`)

    if (!content) {
      throw new Error('Failed to fetch languages')
    }

    return content.data.space.options?.languages.map((lang: { code: string, name: string }) => ({
      code: lang.code,
      name: lang.name,
    })) ?? []
  }

  const createStory = async (content: ISbContentMangmntAPI): Promise<{ data: ISbResponseData }> => {
    try {
      return await storyblokClient.post(`spaces/${spaceId}/stories`, content) as unknown as { data: ISbResponseData }
    }
    catch (err) {
      console.error(`Failed to create story "${content.story?.name}":`, err)
      throw err
    }
  }

  const updateStory = async (id: number, content: ISbContentMangmntAPI): Promise<ISbResponseData> => {
    try {
      return await storyblokClient.put(`spaces/${spaceId}/stories/${id}`, content)
    }
    catch (err) {
      console.error(`Failed to update story "${content.story?.name}":`, err)
      throw err
    }
  }

  const deleteStories = async (ids: number[]) => {
    const res = await Promise.allSettled(ids.map(id => storyblokClient.delete(`spaces/${spaceId}/stories/${id}`, { })))

    const rejected = res.filter(r => r.status === 'rejected')

    if (rejected.length > 0) {
      console.error('Failed to delete stories', rejected)
    }

    return res.filter(r => r.status === 'fulfilled')
  }

  return {
    getStory,
    getAllStories,
    getLanguages,
    createStory,
    updateStory,
    deleteStories,
  }
}
