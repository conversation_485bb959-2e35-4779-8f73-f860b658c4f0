// This file was generated by the storyblok CLI.
// DO NOT MODIFY THIS FILE BY HAND.
import type { ISbStoryData } from 'storyblok'

export interface AccountManagementInvoicesStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'account_management_invoices'
  _uid: string
  [k: string]: unknown
}

export interface AccountManagementProfileStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'account_management_profile'
  _uid: string
  [k: string]: unknown
}

export interface AccountManagementShipmentsStoryblok {
  page_fields?: PageFieldsStoryblok[]
  table_description?: string
  component: 'account_management_shipments'
  _uid: string
  [k: string]: unknown
}

export interface AccountManagementStatementsStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'account_management_statements'
  _uid: string
  [k: string]: unknown
}

export interface AccountManagementUsersStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'account_management_users'
  _uid: string
  [k: string]: unknown
}

export interface RichtextStoryblok {
  type: string
  content?: RichtextStoryblok[]
  marks?: RichtextStoryblok[]
  attrs?: unknown
  text?: string
  [k: string]: unknown
}

export interface AiaFormProgramCompletionStoryblok {
  page_fields?: PageFieldsStoryblok[]
  participants_description?: RichtextStoryblok
  program_provided_description?: RichtextStoryblok
  program_options?: OptionStoryblok[]
  program_provided_documents?: ProgramCompletionDocumentStoryblok[]
  component: 'aia_form_program_completion'
  _uid: string
  [k: string]: unknown
}

export interface BreadcrumbsStoryblok {
  items?: (
    | AccountManagementInvoicesStoryblok
    | AccountManagementProfileStoryblok
    | AccountManagementShipmentsStoryblok
    | AccountManagementStatementsStoryblok
    | AccountManagementUsersStoryblok
    | AiaFormProgramCompletionStoryblok
    | BreadcrumbsStoryblok
    | BreadcrumbsItemStoryblok
    | CountryStoryblok
    | CustomerSupportSampleOrderingStoryblok
    | CustomerSupportTroubleBackchargeStoryblok
    | CustomerSupportTroubleBackchargesStoryblok
    | CustomerSupportTroubleReportStoryblok
    | CustomerSupportTroubleReportsStoryblok
    | CustomerSupportWarrantyStoryblok
    | DealerStoryblok
    | HomepageStoryblok
    | LinkStoryblok
    | MenuStoryblok
    | MenuItemStoryblok
    | MenuItemSampleOrderingStoryblok
    | MessageStoryblok
    | OptionStoryblok
    | PageFieldsStoryblok
    | PortfolioStoryblok
    | PortfolioListingPageStoryblok
    | ProductStoryblok
    | ProgramCompletionDocumentStoryblok
    | ProjectManagementComStoryblok
    | ProjectManagementComAcoustiSealCalculatorStoryblok
    | ProjectManagementComCalculatorStoryblok
    | ProjectManagementComNewStoryblok
    | ProjectManagementComPreapprovedStoryblok
    | ProjectManagementComRequestStoryblok
    | ProjectManagementComRequestsStoryblok
    | ProjectManagementOrderStoryblok
    | ProjectManagementOrderDetailStoryblok
    | ProjectManagementOrderInformationStoryblok
    | ProjectManagementOrderItemStoryblok
    | ProjectManagementOrdersStoryblok
    | ProjectManagementOrdersAcknowledgmentsStoryblok
    | ProjectManagementShippingScheduleStoryblok
    | ProjectOfTheMonthStoryblok
    | ProjectTypeStoryblok
    | ProjectTypeSubcategoriesStoryblok
    | ResourceStoryblok
    | ResourceCategoryStoryblok
    | ResourceFolderStoryblok
    | SpecialFinishStoryblok
    | WallTypesStoryblok
    | WarrantyStoryblok
    | WhatIsNewStoryblok
    | WhatIsNewListingStoryblok
  )[]
  component: 'breadcrumbs'
  _uid: string
  [k: string]: unknown
}

export type MultilinkStoryblok =
  | {
    fieldtype: 'multilink'
    id: string
    url: string
    cached_url: string
    target?: '_blank' | '_self'
    anchor?: string
    rel?: string
    title?: string
    prep?: string
    linktype: 'story'
    story?: {
      name: string
      created_at?: string
      published_at?: string
      id: number
      uuid: string
      content?: {
        [k: string]: unknown
      }
      slug: string
      full_slug: string
      sort_by_date?: null | string
      position?: number
      tag_list?: string[]
      is_startpage?: boolean
      parent_id?: null | number
      meta_data?: null | {
        [k: string]: unknown
      }
      group_id?: string
      first_published_at?: string
      release_id?: null | number
      lang?: string
      path?: null | string
      alternates?: unknown[]
      default_full_slug?: null | string
      translated_slugs?: null | unknown[]
      [k: string]: unknown
    }
    [k: string]: unknown
  }
  | {
    fieldtype: 'multilink'
    id: string
    url: string
    cached_url: string
    target?: '_blank' | '_self'
    linktype: 'url'
    rel?: string
    title?: string
    [k: string]: unknown
  }
  | {
    fieldtype: 'multilink'
    id: string
    url: string
    cached_url: string
    target?: '_blank' | '_self'
    email?: string
    linktype: 'email'
    [k: string]: unknown
  }
  | {
    fieldtype: 'multilink'
    id: string
    url: string
    cached_url: string
    target?: '_blank' | '_self'
    linktype: 'asset'
    [k: string]: unknown
  }

export interface BreadcrumbsItemStoryblok {
  label: string
  link?: Exclude<MultilinkStoryblok, { linktype?: 'email' } | { linktype?: 'asset' }>
  component: 'breadcrumbs_item'
  _uid: string
  [k: string]: unknown
}

export interface CountryStoryblok {
  id?: string
  code?: string
  name?: string
  component: 'country'
  _uid: string
  [k: string]: unknown
}

export interface CustomerSupportSampleOrderingStoryblok {
  name?: string
  id?: string
  component: 'customer_support_sample_ordering'
  _uid: string
  [k: string]: unknown
}

export interface CustomerSupportTroubleBackchargeStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'customer_support_trouble_backcharge'
  _uid: string
  [k: string]: unknown
}

export interface CustomerSupportTroubleBackchargesStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'customer_support_trouble_backcharges'
  _uid: string
  [k: string]: unknown
}

export interface CustomerSupportTroubleReportStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'customer_support_trouble_report'
  _uid: string
  [k: string]: unknown
}

export interface CustomerSupportTroubleReportsStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'customer_support_trouble_reports'
  _uid: string
  [k: string]: unknown
}

export interface CustomerSupportWarrantyStoryblok {
  page_fields?: PageFieldsStoryblok[]
  links?: LinkStoryblok[]
  component: 'customer_support_warranty'
  _uid: string
  [k: string]: unknown
}

export interface DealerStoryblok {
  name?: string
  component: 'dealer'
  _uid: string
  [k: string]: unknown
}

export interface HomepageStoryblok {
  what_is_new?: (
    | ISbStoryData<ResourceStoryblok>
    | ISbStoryData<ResourceCategoryStoryblok>
    | ISbStoryData<ResourceFolderStoryblok>
    | string
  )[]
  what_is_new_see_all?: Exclude<MultilinkStoryblok, { linktype?: 'email' } | { linktype?: 'asset' }>
  component: 'homepage'
  _uid: string
  [k: string]: unknown
}

export interface LinkStoryblok {
  title?: string
  link?: Exclude<MultilinkStoryblok, { linktype?: 'email' } | { linktype?: 'asset' }>
  component: 'link'
  _uid: string
  [k: string]: unknown
}

export interface MenuStoryblok {
  items?: MenuItemStoryblok[]
  component: 'menu'
  _uid: string
  [k: string]: unknown
}

export interface MenuItemStoryblok {
  title: string
  description?: string
  link?: Exclude<MultilinkStoryblok, { linktype?: 'email' } | { linktype?: 'asset' }>
  items?: (MenuItemStoryblok | MenuItemSampleOrderingStoryblok)[]
  component: 'menu_item'
  _uid: string
  [k: string]: unknown
}

export interface MenuItemSampleOrderingStoryblok {
  title: string
  items?: MenuItemStoryblok[]
  component: 'menu_item_sample_ordering'
  _uid: string
  [k: string]: unknown
}

export interface MessageStoryblok {
  title: string
  description?: string
  status?: '' | 'info' | 'success' | 'alert' | 'error'
  size?: '' | 's' | 'l'
  component: 'message'
  _uid: string
  [k: string]: unknown
}

export interface OptionStoryblok {
  label?: string
  value?: string
  component: 'option'
  _uid: string
  [k: string]: unknown
}

export type MultiassetStoryblok = {
  alt: string | null
  copyright?: string | null
  fieldtype: 'asset'
  id: number
  filename: string | null
  name: string
  title: string | null
  focus: string | null
  meta_data?: {
    [k: string]: unknown
  }
  source?: string | null
  is_external_url?: boolean
  is_private?: boolean
  src?: string
  updated_at?: string
  width?: number | null
  height?: number | null
  aspect_ratio?: number | null
  public_id?: string | null
  content_type?: string
  [k: string]: unknown
}[]

export interface PageFieldsStoryblok {
  title?: string
  description?: RichtextStoryblok
  assets?: MultiassetStoryblok
  component: 'page_fields'
  _uid: string
  [k: string]: unknown
}

export interface AssetStoryblok {
  alt: string | null
  copyright?: string | null
  fieldtype: 'asset'
  id: number
  filename: string | null
  name: string
  title: string | null
  focus: string | null
  meta_data?: {
    [k: string]: unknown
  }
  source?: string | null
  is_external_url?: boolean
  is_private?: boolean
  src?: string
  updated_at?: string
  width?: number | null
  height?: number | null
  aspect_ratio?: number | null
  public_id?: string | null
  content_type?: string
  [k: string]: unknown
}

export interface PortfolioStoryblok {
  project_id?: string
  product?: ISbStoryData<ProductStoryblok> | string
  project_name: string
  project_type?: ISbStoryData<ProjectTypeStoryblok> | string
  project_type_sub_categories?: ISbStoryData<ProjectTypeSubcategoriesStoryblok> | string
  client_name?: string
  search_keyword?: string
  country?: ISbStoryData<CountryStoryblok> | string
  state?: string
  location?: string
  installation_date?: string
  description?: string
  pdf?: AssetStoryblok
  architect_design?: string
  general_contractor?: string
  newRetrofit?: '' | 'N' | 'R'
  number_of_walls?: string
  project_size?: string
  largest_size_w?: string
  largest_size_h?: string
  wall_types?: ISbStoryData<WallTypesStoryblok> | string
  panel_finish?: string
  dealer?: string
  video?: AssetStoryblok
  award_winning_text?: string
  award_winning?: string
  slide_show?: AssetStoryblok
  photos?: MultiassetStoryblok
  industry_list?: string
  skyfold_number?: string
  finish_type?: string
  brand?: string
  owner?: string
  flag?: string
  special_finish?: string
  special_finish_new?: (ISbStoryData<SpecialFinishStoryblok> | string)[]
  date_image?: string
  project_of_the_month?: ISbStoryData<ProjectOfTheMonthStoryblok> | string
  component: 'portfolio'
  _uid: string
  [k: string]: unknown
}

export interface PortfolioListingPageStoryblok {
  component: 'portfolio_listing_page'
  _uid: string
  [k: string]: unknown
}

export interface ProductStoryblok {
  product_id?: string
  name?: string
  photo?: AssetStoryblok
  website?: string
  component: 'product'
  _uid: string
  [k: string]: unknown
}

export interface ProgramCompletionDocumentStoryblok {
  title: string
  date: string
  asset: AssetStoryblok
  component: 'program_completion_document'
  _uid: string
  [k: string]: unknown
}

export interface ProjectManagementComStoryblok {
  page_fields?: PageFieldsStoryblok[]
  links?: LinkStoryblok[]
  description?: RichtextStoryblok
  component: 'project_management_com'
  _uid: string
  [k: string]: unknown
}

export interface ProjectManagementComAcoustiSealCalculatorStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'project_management_com_acousti_seal_calculator'
  _uid: string
  [k: string]: unknown
}

export interface ProjectManagementComCalculatorStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'project_management_com_calculator'
  _uid: string
  [k: string]: unknown
}

export interface ProjectManagementComNewStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'project_management_com_new'
  _uid: string
  [k: string]: unknown
}

export interface ProjectManagementComPreapprovedStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'project_management_com_preapproved'
  _uid: string
  [k: string]: unknown
}

export interface ProjectManagementComRequestStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'project_management_com_request'
  _uid: string
  [k: string]: unknown
}

export interface ProjectManagementComRequestsStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'project_management_com_requests'
  _uid: string
  [k: string]: unknown
}

export interface ProjectManagementOrderStoryblok {
  component: 'project_management_order'
  _uid: string
  [k: string]: unknown
}

export interface ProjectManagementOrderDetailStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'project_management_order_detail'
  _uid: string
  [k: string]: unknown
}

export interface ProjectManagementOrderInformationStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'project_management_order_information'
  _uid: string
  [k: string]: unknown
}

export interface ProjectManagementOrderItemStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'project_management_order_item'
  _uid: string
  [k: string]: unknown
}

export interface ProjectManagementOrdersStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'project_management_orders'
  _uid: string
  [k: string]: unknown
}

export interface ProjectManagementOrdersAcknowledgmentsStoryblok {
  page_fields?: PageFieldsStoryblok[]
  component: 'project_management_orders_acknowledgments'
  _uid: string
  [k: string]: unknown
}

export interface ProjectManagementShippingScheduleStoryblok {
  page_fields?: PageFieldsStoryblok[]
  subtitle?: string
  note?: RichtextStoryblok
  messages?: MessageStoryblok[]
  component: 'project_management_shipping_schedule'
  _uid: string
  [k: string]: unknown
}

export interface ProjectOfTheMonthStoryblok {
  project?: string
  scope?: string
  architect?: string
  specifying_dealer?: string
  dealer?: ISbStoryData<DealerStoryblok> | string
  image?: AssetStoryblok
  component: 'project_of_the_month'
  _uid: string
  [k: string]: unknown
}

export interface ProjectTypeStoryblok {
  id?: string
  name?: string
  component: 'project_type'
  _uid: string
  [k: string]: unknown
}

export interface ProjectTypeSubcategoriesStoryblok {
  id?: string
  name?: string
  project_type?: ISbStoryData<ProjectTypeStoryblok> | string
  component: 'project_type_subcategories'
  _uid: string
  [k: string]: unknown
}

export interface ResourceStoryblok {
  asset: AssetStoryblok
  expire_date?: string
  component: 'resource'
  _uid: string
  [k: string]: unknown
}

export interface ResourceCategoryStoryblok {
  name: string
  description?: string
  component: 'resource_category'
  _uid: string
  [k: string]: unknown
}

export interface ResourceFolderStoryblok {
  name: string
  description?: string
  component: 'resource_folder'
  _uid: string
  [k: string]: unknown
}

export interface SpecialFinishStoryblok {
  id?: string
  name: string
  component: 'special_finish'
  _uid: string
  [k: string]: unknown
}

export interface WallTypesStoryblok {
  id?: string
  name?: string
  component: 'wall_types'
  _uid: string
  [k: string]: unknown
}

export interface WarrantyStoryblok {
  Title?: string
  Link?: Exclude<MultilinkStoryblok, { linktype?: 'email' } | { linktype?: 'asset' }>
  component: 'Warranty'
  _uid: string
  [k: string]: unknown
}

export interface WhatIsNewStoryblok {
  component: 'what-is-new'
  _uid: string
  [k: string]: unknown
}

export interface WhatIsNewListingStoryblok {
  component: 'what-is-new-listing'
  _uid: string
  [k: string]: unknown
}
