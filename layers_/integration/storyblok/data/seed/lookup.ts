import { writeFile } from 'node:fs/promises'
import type { CountryStoryblok, ProductStoryblok, ProjectTypeStoryblok, ProjectTypeSubcategoriesStoryblok, WallTypesStoryblok } from '@integration/storyblok/data/components'
import { createClient } from '@integration/storyblok/client'

type ItemsMap = Record<
  string,
  string
>

// script
const lookup = async () => {
  // fetch all data
  const accessToken = process.env.STORYBLOK_ACCESS_TOKEN!
  const client = createClient({ type: 'content', accessToken })

  const country = await client.getAll<CountryStoryblok>('cdn/stories', { content_type: 'country' })
  const product = await client.getAll<ProductStoryblok>('cdn/stories', { content_type: 'product' })
  const project_type = await client.getAll<ProjectTypeStoryblok>('cdn/stories', { content_type: 'project_type' })
  const project_type_subcategories = await client.getAll<ProjectTypeSubcategoriesStoryblok>('cdn/stories', { content_type: 'project_type_subcategories' })
  const wall_types = await client.getAll<WallTypesStoryblok>('cdn/stories', { content_type: 'wall_types' })

  // map data
  const itemsById = Object.fromEntries(Object.entries({ country, product, project_type, project_type_subcategories, wall_types }).map(([key, results]) => [
    key,
    results.reduce<
      ItemsMap
    >(
      (items, item) => ({
        ...items,
        [`${item.content.id ?? item.content.product_id}`]: item.uuid,
      }),
      {},
    ),
  ]))

  await writeFile('lookup.json', JSON.stringify(itemsById, null, 2))
}

lookup()
