import items from './portfolios.json'
import lookup from './lookup.json'

type Item = typeof items[number]

const STORYBLOK_TOKEN = process.env.STORYBLOK_TOKEN
if (!STORYBLOK_TOKEN) throw new Error('Missing STORYBLOK_TOKEN')

const space = '326620'
const parentFolderId = 662578219

const storyblokApiUrl = `https://mapi.storyblok.com/v1/spaces/${space}/stories`

const t = (value: string) => (value === 'NULL' ? undefined : value)
const mapToStoryblokPortfolio = (
  item: Item,
  lookup: {
    product: Record<string, string>
    project_type: Record<string, string>
    project_type_subcategories: Record<string, string>
    country: Record<string, string>
    wall_types: Record<string, string>
  },
) => {
  return {
    component: 'portfolio',
    project_id: item.projectID,
    product: lookup.product[item.productId] || undefined,
    project_name: t(item.projectNameEn),
    project_name__i18n__fr: t(item.projectNameFr),
    project_name__i18n__es: t(item.projectNameSp),
    project_type: lookup.project_type[item.projectType] || undefined,
    project_type_sub_categories: lookup.project_type_subcategories[item.projectTypeSubCategorie] || undefined,
    client_name: t(item.clientName),
    search_keyword: t(item.searchKeyword),
    country: lookup.country[item.country] || undefined,
    state: t(item.state),
    location: t(item.location),
    installation_date: t(item.installationDate),
    description: t(item.descriptionEn),
    description__i18n__fr: t(item.descriptionFr),
    description__i18n__es: t(item.descriptionSp),
    description__i18n__de: t(item.descriptionDe),
    description__i18n__pt: t(item.descriptionPt),
    architect_design: t(item.architectDesign),
    general_contractor: t(item.generalContractor),
    newRetrofit: item.newRetrofit,
    number_of_walls: Number(item.numberWalls),
    project_size: t(item.projectSize),
    largest_size_w: Number(item.largestSizeW),
    largest_size_h: Number(item.largestSizeH),
    wall_types: lookup.wall_types[item.wallTypes] || undefined,
    panel_finish: t(item.panelFinishEn),
    panel_finish__i18n__fr: t(item.panelFinishFr),
    panel_finish__i18n__es: t(item.panelFinishSp),
    panel_finish__i18n__de: t(item.panelFinishDe),
    panel_finish__i18n__pt: t(item.panelFinishPt),
    dealer: t(item.dealer),
    video: t(item.video) ? [item.video] : [],
    award_winning_text: t(item.awardWinningText),
    award_winning: item.awardWinning,
    slide_show: [], // based on SlideExists === "1"
    photos: [], // based on PhotoExists === "1"
    industry_list: t(item.Industry_List),
    skyfold_number: t(item.Skyfold_Number),
    finish_type: t(item.Finish_Type),
    brand: t(item.Brand),
    owner: t(item.Owner),
    flag: t(item.Flag),
    special_finish: t(item.Special_Finish),
    date_image: t(item.Date_Image),
  }
}

const createSlug = (str: string) => str
  .toLowerCase()
  .normalize('NFD') // Split accented characters into base + diacritics
  .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
  .replace(/[^a-z0-9\s-]/g, '') // Remove invalid chars
  .trim() // Trim whitespace
  .replace(/\s+/g, '-') // Replace spaces with -
  .replace(/-+/g, '-') // Collapse multiple hyphens

async function importItem(item: Item): Promise<void> {
  const name = item.projectNameEn + ' ' + item.Skyfold_Number
  const slug = createSlug(name)

  const payload = {
    publish: '1',
    story: {
      name,
      slug,
      is_folder: false,
      parent_id: parentFolderId,
      content: mapToStoryblokPortfolio(item, lookup),
    },
  }

  const response = await fetch(storyblokApiUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': STORYBLOK_TOKEN!,
    },
    body: JSON.stringify(payload),
  })

  if (!response.ok) {
    const errorText = await response.text()
    throw new Error(`HTTP ${response.status} - ${errorText}`)
  }

  console.log(`✅ Imported: ${name}`)
}

async function main() {
  for (const item of items) {
    try {
      await importItem(item)
    }
    catch (error) {
      console.error(`❌ Failed to import ${item.projectNameEn}:`, error)
    }
  }
}

main()
