import StoryblokClient from 'storyblok-js-client'

type ClientOptions = {
  type: 'content'
  accessToken: string
} | {
  type: 'management'
  oauthToken: string
}

export const createClient = (options: ClientOptions) => {
  if (options.type === 'content') {
    if (!options.accessToken) {
      throw new Error('Missing accessToken for content delivery')
    }
  }
  else if (options.type === 'management') {
    if (!options.oauthToken) {
      throw new Error('Missing oauthToken for management')
    }
  }

  return new StoryblokClient(options.type === 'content' ? { accessToken: options.accessToken } : { oauthToken: options.oauthToken })
}
