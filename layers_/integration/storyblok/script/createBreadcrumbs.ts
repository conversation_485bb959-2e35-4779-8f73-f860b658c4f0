import type { ISbContentMangmntAPI } from 'storyblok-js-client'
import type { Breadcrumb, Breadcrumbs, FolderMap, StoryMap } from '@integration/types/breadcrumbs'
import { sanitizeSlug, pathToSlug } from '@integration/utils/storyblok'
import { createClient } from '../client'
import managementUtils from '../managementUtils'

const normalizeBreadcrumbItem = ({ path, parent_id, crumbs }: { path: string, parent_id: number, crumbs: Breadcrumb[] }): ISbContentMangmntAPI['story'] => {
  let name = 'Breadcrumbs'

  const breadcrumbsItem = crumbs.map((item) => {
    const label = Object.entries(item.label).reduce((acc, [lang, label]) => {
      if (lang === 'en') {
        acc.label = label
        name = label
      }
      else if (label !== item.label.en) {
        acc[`label__i18n__${lang}`] = label
      }
      return acc
    }, {} as Record<string, string>)

    return ({
      ...label,
      link: item.link,
      component: 'breadcrumbs_item',
    })
  })

  return {
    name: name,
    parent_id: parent_id.toString(),
    content: {
      component: 'breadcrumbs',
      items: breadcrumbsItem,
    },
    slug: pathToSlug(path),
  }
}

export const createBreadcrumbs = async ({ deliveryAccessToken, managmentAccessToken, spaceId, site, languages }: { deliveryAccessToken: string, managmentAccessToken: string, spaceId: string, site: string, languages: string[] }) => {
  console.log('Starting create breadcrumbs...')
  console.time('createBreadcrumbs')

  const clientDelivery = createClient({ type: 'content', accessToken: deliveryAccessToken })
  const clientManagment = createClient({ type: 'management', oauthToken: managmentAccessToken })

  const { getAllStories, deleteStories, createStory, updateStory } = managementUtils(clientManagment, spaceId)

  try {
    // Fetch folders to identify paths corresponding to non-page story
    const allFolders = await getAllStories({ options: { folder_only: true } })
    const folders = allFolders.filter(folder => folder?.full_slug?.startsWith(`${site}/pages/`))
    const foldersMap: FolderMap = new Map()

    for (const folder of folders) {
      const fullSlug = sanitizeSlug(folder.full_slug, site)

      foldersMap.set(fullSlug, {
        label: {
          en: folder.name,
        },
        slug: folder.slug,
      })
    }

    // Fetch all stories for each language based on the site
    const siteStories: StoryMap = new Map()

    await Promise.all(
      languages.map(async (lang) => {
        try {
          const data = await clientDelivery.getAll('cdn/stories', {
            starts_with: `${site}/pages/`,
            language: lang,
          })

          for (const story of data) {
            const fullSlug = sanitizeSlug(story.full_slug, site, lang)
            const label = story.content?.title ?? story.content?.name ?? story.content?.page_fields?.title ?? story.name

            if (siteStories.has(fullSlug)) {
              // Handle multilanguage
              const existingStory = siteStories.get(fullSlug)
              if (existingStory) existingStory.label[lang] = label
            }
            else {
              siteStories.set(fullSlug, {
                label: {
                  [lang]: label,
                },
                slug: story.slug,
                path: fullSlug,
                uuid: story.uuid,
              })
            }
          }
        }
        catch {
          console.error('[createBreadcrumbs] Failed to fetch stories for', lang)
        }
      }),
    )

    // Generate breadcrumbs
    const breadcrumbs: Breadcrumbs = {}
    const _alreadyCreatedBreadcrumbs: Map<string, Breadcrumb> = new Map()

    const sortedStoriesByPathLength = Array.from(siteStories.keys()).sort((a, b) => a.length - b.length)

    for (const storyKey of sortedStoriesByPathLength) {
      const story = siteStories.get(storyKey)!

      const segments = story.path.split('/').filter(Boolean) ?? []
      segments.splice(0, 0, '/')

      let currentPath = ''
      const currentCrumbs: Breadcrumb[] = []

      for (const segment of segments) {
        if (segment !== '/') {
          currentPath += segment + '/'
        }

        if (_alreadyCreatedBreadcrumbs.has(currentPath)) {
          currentCrumbs.push(_alreadyCreatedBreadcrumbs.get(currentPath)!)
        }
        else {
          let breacrumb: Breadcrumb | null = null

          if (siteStories.has(currentPath)) {
            const storyPart = siteStories.get(currentPath)!
            breacrumb = {
              label: storyPart.label,
              link: {
                fieldtype: 'multilink',
                linktype: 'story',
                cached_url: storyPart.path,
                url: storyPart.path,
                id: storyPart.uuid,
              },
            }
          }

          if (!breacrumb) {
            if (foldersMap.has(currentPath)) {
              const folderPart = foldersMap.get(currentPath)!
              breacrumb = {
                label: folderPart.label,
              }
            }
          }

          if (breacrumb) {
            currentCrumbs.push(breacrumb)
            _alreadyCreatedBreadcrumbs.set(currentPath, breacrumb)
          }
        }
      }

      breadcrumbs[story.path] = currentCrumbs
    }

    if (!breadcrumbs || Object.keys(breadcrumbs).length === 0) throw new Error('[createBreadcrumbs] Failed to generate breadcrumbs')

    // Retrieve the breadcrumbs folder
    const currentBreadcrumbsFolder = allFolders.find(folder => folder?.full_slug === `${site}/breadcrumbs`)
    if (!currentBreadcrumbsFolder || !currentBreadcrumbsFolder.id || !currentBreadcrumbsFolder.parent_id) throw new Error('[createBreadcrumbs] Failed to find breadcrumbs folder')

    // Create new temp breadcrumbs folder
    const _tempBreadcrumbsFolder = await createStory(
      {
        story: {
          name: 'Breadcrumbs',
          content: { content_types: ['breadcrumbs'], lock_subfolders_content_types: false },
          parent_id: currentBreadcrumbsFolder.parent_id.toString(),
          is_folder: true,
          default_root: true,
          slug: 'breadcrumbs__new',
        },
      },
    )

    const _tempBreadcrumbsFolderId = _tempBreadcrumbsFolder.data.story?.id

    if (!_tempBreadcrumbsFolderId) throw new Error('[createBreadcrumbs] Failed to create temp breadcrumbs folder')

    // Create new breadcrumbs
    const newBreadcrumbsResponse = await Promise.allSettled(
      Object.entries(breadcrumbs).map(async ([path, crumbs]) => {
        const story = normalizeBreadcrumbItem({
          parent_id: _tempBreadcrumbsFolderId,
          crumbs,
          path,
        })

        return await createStory({ story, publish: 1 })
      }),
    )

    // Delete old breadcrumbs folder and rename temp folder
    await deleteStories([currentBreadcrumbsFolder.id])
    await updateStory(_tempBreadcrumbsFolderId, {
      story: {
        name: 'Breadcrumbs',
        slug: 'breadcrumbs',
      },
      force_update: 1,
      publish: 1,
    })

    const successful = newBreadcrumbsResponse.filter(r => r.status === 'fulfilled')

    console.timeEnd('createBreadcrumbs')
    console.log(`Correctly created ${successful.length} breadcrumbs out of ${newBreadcrumbsResponse.length} for ${site}`)

    return successful.map(r => r.value.data.story?.uuid).filter(Boolean)
  }
  catch (error) {
    console.timeEnd('createBreadcrumbs')
    console.error(error)
    throw error
  }
}
