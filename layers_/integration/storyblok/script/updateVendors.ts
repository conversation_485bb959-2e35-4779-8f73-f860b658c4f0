import * as v from 'valibot'
import { SampleOrderingVendorSchema } from '@modernfold/server/types/sampleordering'
import { fetchVendors } from '@modernfold/server/api/samples/vendors'
import type { MenuItemSampleOrderingStoryblok, MenuItemStoryblok } from '../data/components'
import { createClient } from '../client'
import managementUtils from '../managementUtils'

const SAMPLE_ORDERING_STORY_ID = 657382004
const SAMPLE_ORDERING_COMPONENT_TYPE = 'customer_support_sample_ordering'
const HEADER_MENU_STORY_ID = 664580644
const SAMPLE_ORDERING_MENU_ITEM_COMPONENT_TYPE = 'menu_item_sample_ordering'

const createMenuItem = (title: string, id: string, slug: string) => ({
  component: 'menu_item',
  title,
  link: {
    fieldtype: 'multilink',
    linktype: 'story',
    cached_url: slug,
    id,
  },
})

export async function updateSampleOrderingVendors({ managmentAccessToken, spaceId }: { managmentAccessToken: string, spaceId: string }) {
  if (!managmentAccessToken || !spaceId) {
    return {
      success: false,
      message: 'Missing Storyblok configuration',
    }
  }

  const clientManagment = createClient({ type: 'management', oauthToken: managmentAccessToken })
  const { getStory, getAllStories, createStory, updateStory, deleteStories } = managementUtils(clientManagment, spaceId)

  try {
    // 1. Retrieve vendors from external API
    const vendorsRaw = await fetchVendors()
    const vendors = v.parse(v.array(SampleOrderingVendorSchema), vendorsRaw)
    const normalizedVendors = vendors.map(vendor => ({
      ...vendor,
      slug: vendor.VendorID.toString(),
      full_slug: `modernfold/pages/customer-support/sample-ordering/${vendor.VendorID}`,
    }))

    // 2. Delete existing stories
    const existingStories = await getAllStories({ options: { with_parent: SAMPLE_ORDERING_STORY_ID } })
    await deleteStories(existingStories.map(story => story.id))

    // 3. Create new stories
    const results = await Promise.allSettled(normalizedVendors.map(async (vendor) => {
      const story = {
        name: vendor.VendorName,
        parent_id: SAMPLE_ORDERING_STORY_ID.toString(),
        content: {
          component: SAMPLE_ORDERING_COMPONENT_TYPE,
          id: vendor.slug,
          name: vendor.VendorName,
        },
        slug: vendor.slug,
      }

      const result = await createStory({ story, publish: 1 })

      if (!result.data.story.uuid || !result.data.story.full_slug || !result.data.story.content.id) {
        throw new Error('Invalid response from Storyblok while creating vendor story')
      }

      const { name, uuid, full_slug, content } = result.data.story

      return {
        name,
        uuid,
        full_slug,
        id: content.id,
      }
    }))

    const fulfilled = results.filter(
      _result => _result.status === 'fulfilled',
    )

    // 4. Update header menu
    const headerMenuStory = await getStory(HEADER_MENU_STORY_ID)

    if (headerMenuStory) {
      const sampleOrderingMenuItems = fulfilled.map(el =>
        createMenuItem(el.value.name, el.value.uuid, el.value.full_slug),
      )

      const updatedHeader = {
        ...headerMenuStory,
        default_root: false,
        parent_id: headerMenuStory.parent_id ? headerMenuStory.parent_id.toString() : undefined,
        first_published_at: headerMenuStory.first_published_at || undefined,
        sort_by_date: headerMenuStory.sort_by_date || undefined,
        content: {
          ...headerMenuStory.content,
          items: headerMenuStory.content.items.map((item: MenuItemSampleOrderingStoryblok | MenuItemStoryblok) => {
            if (item.component === SAMPLE_ORDERING_MENU_ITEM_COMPONENT_TYPE) {
              return { ...item, items: sampleOrderingMenuItems }
            }

            return {
              ...item,
              items: item.items?.map((sub: MenuItemSampleOrderingStoryblok | MenuItemStoryblok) =>
                sub.component === SAMPLE_ORDERING_MENU_ITEM_COMPONENT_TYPE
                  ? { ...sub, items: sampleOrderingMenuItems }
                  : sub,
              ),
            }
          }),
        },
      }

      await updateStory(HEADER_MENU_STORY_ID, {
        story: updatedHeader,
        force_update: 1,
        publish: 1,
      })

      console.log(`[UPDATE_MENU] ${sampleOrderingMenuItems.length} items updated`)

      return {
        success: true,
        created: fulfilled.length,
        failed: results.length - fulfilled.length,
      }
    }
  }
  catch (err) {
    console.error('[UPDATE_SAMPLE_ORDERING_VENDORS_ERROR]', err)

    return {
      success: false,
      message: err instanceof Error ? err.message : 'Unknown error',
    }
  }
}
