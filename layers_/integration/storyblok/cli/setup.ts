import { $ } from 'bun'

const space = '326620' // TODO: from env
const path = 'layers_/integration/storyblok/'
const componentsPath = `${path}data/`
const componentsFileName = `components.${space}.json`
const typesPath = `${componentsPath}components.ts`
const typesOptionsPath = `${path}cli/json-schema-to-ts-options.json`

// generate both schemas
await $`bun storyblok pull-components --space ${space} -p ${componentsPath}`
await $`bun storyblok generate-typescript-typedefs --source ${componentsPath}${componentsFileName} --target ${typesPath} --JSONSchemaToTSOptionsPath ${typesOptionsPath}`
await $`bun lint:fix`
