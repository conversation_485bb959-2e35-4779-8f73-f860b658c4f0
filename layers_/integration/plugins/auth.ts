export default defineNuxtPlugin({
  name: 'auth',
  async setup() {
    const { loggedIn, user } = useUserSession()
    const { initialize: initializeAuth0User } = useAuth0User()

    if (useRuntimeConfig().storybookBuild) return

    if (!loggedIn.value || !user.value || !Object.keys(user.value).length) {
      await navigateTo('/auth0/login', {
        external: true,
      })
      return
    }

    await callOnce('initializeUserSettings', initializeAuth0User)
  } })
