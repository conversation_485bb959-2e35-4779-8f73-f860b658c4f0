import { eventHandler, sendRedirect } from 'h3'
import { useRuntimeConfig } from '#imports'

export default eventHandler((event) => {
  const config = useRuntimeConfig().oauth?.auth0

  return sendRedirect(event, `https://${config.domain}/authorize?${new URLSearchParams({
    response_type: 'code',
    client_id: config.clientId,
    redirect_uri: config.redirectURL,
    scope:
      'offline_access openid profile email read:current_user update:current_user_metadata create:current_user_metadata delete:current_user_metadata',
    audience: config.audience,
  })}`)
})
