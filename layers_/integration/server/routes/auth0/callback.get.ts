import { jwtDecode } from 'jwt-decode'

export default defineOAuthAuth0EventHandler({
  config: {
    emailRequired: true,
  },
  async onSuccess(event, { user, tokens }) {
    const { id_token, ...tokensWithoutIdToken } = tokens
    const accessToken = jwtDecode(tokens.access_token)
    // @ts-expect-error custom namespace is not typed
    const roles = accessToken['https://modernfold.com/roles'] ?? []
    await replaceUserSession(event, {
      user: {
        ...user,
        roles,
      },
      tokens: tokensWithoutIdToken,
    })
    return sendRedirect(event, '/')
  },
  onError(event, error) {
    console.error('auth0 callback error:', error)
    return sendRedirect(event, '/auth0/login')
  },
})
