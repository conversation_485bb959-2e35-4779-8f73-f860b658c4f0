import { jwtDecode } from 'jwt-decode'
import type { H3Event } from 'h3'
import type { SetRequired } from 'type-fest'
import { refreshTokens } from '@integration/server/utils/auth0'
import type { UserSession } from '#auth-utils'

export const requireRefreshedUserSession = async (event: H3Event) => {
  const session = await getRefreshedUserSession(event)
  if (!session) {
    throw createError({ status: 401, message: 'User session expired' })
  }
  return session
}

export const getRefreshedUserSession = async (event: H3Event) => {
  const session = await getUserSession(event)
  if (!session.user || !Object.keys(session.user).length) return null
  try {
    const validSession = await getValidUserSession(
      session as SetRequired<UserSession, 'user'>,
      event,
    )
    return validSession
  }
  catch {
    throw createError({ status: 500, message: 'Error checking user session' })
  }
}

export const getValidUserSession = async <Session extends UserSession>(
  session: Session,
  event: H3Event,
) => {
  if (!session.user || !Object.keys(session.user).length) return session

  const accessToken = jwtDecode(session.tokens.access_token)
  if (!accessToken.exp) return session

  const expirationDate = accessToken.exp * 1000 // exp is in seconds
  const now = Date.now() - 5 * 60 * 1000 // Date.now() is in milliseconds
  if (expirationDate > now) return session

  return refreshUserSession(session, event)
}

export const refreshUserSession = async <Session extends UserSession>(
  session: Session,
  event: H3Event,
) => {
  const refresh_token = session.tokens.refresh_token
  if (!refresh_token) {
    await clearUserSession(event)
    throw new Error('Missing refresh token')
  }

  try {
    const newTokens = await refreshTokens(refresh_token)

    const { id_token, ...tokensWithoutIdToken } = newTokens
    const newSession = {
      ...session,
      tokens: {
        ...session.tokens,
        ...tokensWithoutIdToken,
      },
    }
    await replaceUserSession(event, newSession)
    return newSession
  }
  catch {
    await clearUserSession(event)
    throw new Error('Error refreshing token')
  }
}
