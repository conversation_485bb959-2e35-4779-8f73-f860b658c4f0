type Token = {
  access_token: string
  id_token: string
  scope: string
  expires_in: number
  token_type: 'Bearer'
}

export const refreshTokens = async (refresh_token: string) =>
  $fetch<Token>(`https://${process.env.NUXT_OAUTH_AUTH0_DOMAIN}/oauth/token`, {
    method: 'POST',
    body: {
      grant_type: 'refresh_token',
      client_id: process.env.NUXT_OAUTH_AUTH0_CLIENT_ID,
      client_secret: process.env.NUXT_OAUTH_AUTH0_CLIENT_SECRET!,
      refresh_token,
    },
  })
