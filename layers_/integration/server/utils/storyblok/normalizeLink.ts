import type { LinkStoryblok, MenuItemSampleOrderingStoryblok, MenuItemStoryblok } from '@integration/storyblok/data/components'
import { cleanPageUrl } from '@integration/utils/cleanPageUrl'
import { isLinkExternal } from '@integration/server/utils/storyblok/isLinkExternal'
import type { Link } from '~/types/link'

/**
 * resolveLinks is the equivalent storyblok api parameter
 * when set to story storyblok return '/locale/' as cached_url even if not set
 */
export const normalizeLink = (
  link: LinkStoryblok | MenuItemStoryblok | MenuItemSampleOrderingStoryblok,
  resolveLinks: 'link' | 'story' = 'link',
): Link => {
  if (link.component === 'menu_item_sample_ordering') {
    return {
      label: link.title,
    }
  }

  return ({
    label: link.title || '',
    to: !!link.link?.cached_url && (resolveLinks !== 'story' || !!link.link?.story)
      ? cleanPageUrl(link.link?.cached_url)
      : undefined,
    external: isLinkExternal(link.link),
  })
}
