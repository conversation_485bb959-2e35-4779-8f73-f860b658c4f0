type AuthTokenResponse = {
  access_token: string
  scope: string
  expires_in: number
  token_type: string
}

export const generateAuth0Token = async () => {
  const response = await $fetch<AuthTokenResponse>(
    `https://${process.env.AUTH0_DOMAIN}/oauth/token`,
    {
      method: 'POST',
      body: {
        grant_type: 'client_credentials',
        client_id: process.env.AUTH0_CLIENT_ID,
        client_secret: process.env.AUTH0_CLIENT_SECRET,
        audience: process.env.AUTH0_AUDIENCE,
      },
    },
  )

  return response
}
