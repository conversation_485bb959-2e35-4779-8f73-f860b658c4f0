import { getClient } from '@integration/server/utils/algolia'
import type { ResourceAlgolia, ResourceFolderAlgolia } from '@integration/algolia/indexes/resources'
import * as v from 'valibot'
import type { SearchResponse } from 'algoliasearch'

const paramsSchema = v.object({
  locale: v.string(),
})

const querySchema = v.object({
  parentFullSlug: v.string(),
  query: v.string(),
  page: v.optional(
    v.pipe(
      v.string(),
      v.transform(parseInt),
      v.number(),
    ),
  ),
})

export default defineEventHandler(async (event) => {
  const { locale } = await getValidatedRouterParams(event, params => v.parse(paramsSchema, params))
  const { parentFullSlug, query, page } = await getValidatedQuery(event, query => v.parse(querySchema, query))
  const site = useRuntimeConfig().public.site

  const client = getClient()
  const response = await client.search({
    requests: [
      {
        indexName: `${site}__resources__${locale}`,
        query,
        hitsPerPage: 12,
        page: page && page - 1,
        attributesToHighlight: [],
        facetFilters: `parent_full_slug:${parentFullSlug}`,
      },
    ],
  }) as {
    results: SearchResponse<ResourceAlgolia | ResourceFolderAlgolia>[]
  }

  return response
})
