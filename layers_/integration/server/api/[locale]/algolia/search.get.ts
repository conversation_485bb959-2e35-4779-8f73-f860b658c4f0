import { getClient } from '@integration/server/utils/algolia'
import type { PortfolioAlgolia } from '@integration/algolia/indexes/portfolios'
import type { ResourceAlgolia, ResourceFolderAlgolia, ResourceCategoryAlgolia } from '@integration/algolia/indexes/resources'
import * as v from 'valibot'
import type { SearchResponse } from 'algoliasearch'

const paramsSchema = v.object({
  locale: v.string(),
})

const querySchema = v.object({
  query: v.string(),
  page: v.optional(
    v.pipe(
      v.string(),
      v.transform(parseInt),
      v.number(),
    ),
  ),
})

export default defineEventHandler(async (event) => {
  const { locale } = await getValidatedRouterParams(event, params => v.parse(paramsSchema, params))
  const { query, page } = await getValidatedQuery(event, query => v.parse(querySchema, query))
  const site = useRuntimeConfig().public.site

  const client = getClient()
  const response = await client.search({
    requests: [
      (site === 'skyfold' && {
        indexName: `skyfold__portfolios__${locale}`,
        query,
        hitsPerPage: 3,
        attributesToRetrieve: ['content_type', 'objectID', 'project_name', 'project_type.name'],
        page: page && page - 1,
        attributesToHighlight: [],
      }),
      {
        indexName: `${site}__resources__${locale}`,
        query,
        hitsPerPage: site === 'skyfold' ? 3 : 6,
        attributesToRetrieve: ['content_type', 'objectID', 'name', 'description', 'category_name', 'asset.filename'],
        page: page && page - 1,
        attributesToHighlight: [],
      },
    ].filter(Boolean),
  }) as {
    results: SearchResponse<PortfolioAlgolia | ResourceAlgolia | ResourceFolderAlgolia | ResourceCategoryAlgolia>[]
  }

  return response
})
