import type { PortfolioStoryblok } from '@integration/storyblok/data/components'
import type { ISbStoriesParams } from 'storyblok-js-client'
import type StoryblokClient from 'storyblok-js-client'
import type { WithRelationships } from '@integration/types/storyblok'
import type { UiState } from 'instantsearch.js'
import { groupBy } from '@design-system/utils/object'

export default defineEventHandler(async (event) => {
  const { algoliaUIStateStringified } = getQuery<{ algoliaUIStateStringified: string }>(event)
  const algoliaUIState = JSON.parse(algoliaUIStateStringified) as UiState

  const portfolioRelationsFacets = [
    'product.name',
    'country.name',
    'project_type.name',
  ]
  const portfolioFieldsFacets: string[] = [
    'state',
    'location',
    'finish_type',
    'special_finish',
    'brand',
    'architect_design',
    'dealer',
  ]
  const cmsData = await fetchPortfolios(
    getStoryblokClient(),
    algoliaUIState,
    portfolioRelationsFacets,
  )

  const t = await useTranslationServerMiddleware(event)
  const appliedFilters = [...portfolioRelationsFacets, ...portfolioFieldsFacets]
  // @ts-expect-error "?." do not narrow types
    .filter(rel => rel === 'product.name' || algoliaUIState.menu?.[rel]?.length || algoliaUIState.refinementList?.[rel]?.length)
    .map((rel) => {
      // @ts-expect-error "?." do not narrow types
      const facetValue = algoliaUIState.menu?.[rel] ?? algoliaUIState.refinementList?.[rel]
      const value = Array.isArray(facetValue) ? facetValue.join(', ') : (facetValue ?? 'All')
      return {
        name: t(`portfolio.filters.labels.${rel.replace('.', '__')}`),
        value,
      }
    })
  return {
    appliedFilters, cmsData,
  }
})

const fetchPortfolios = async (client: StoryblokClient, algoliaUIState: UiState, portfolioRelationsFacets: string[]) => {
  type Filter = {
    type: 'relationship'
    rel: string
    field: string
    value: string | string[]
  } | {
    type: 'field'
    field: string
    value: string | string[]
  }
  const getFilter = ([facet, value]: [facet: string, value: string | string[]]): Filter => {
    const [rel, field] = facet.split('.')
    const isRelationship = rel && field && portfolioRelationsFacets.includes(facet)
    return isRelationship
      ? {
          type: 'relationship',
          rel,
          field,
          value,
        }
      : {
          type: 'field',
          field: facet,
          value,
        }
  }

  const filters
    = [
      algoliaUIState?.menu,
      algoliaUIState?.refinementList,
    ]
      .filter(Boolean)
      .flatMap(Object.entries)
      .map(getFilter)

  const filterQuery: ISbStoriesParams['filter_query'] = {}

  const valueString
    = (value: string | string[]) => typeof value === 'string'
      ? value
      : value.join(',')

  for (const filter of filters) {
    if (filter.type === 'field') {
      filterQuery[filter.field] = {
        in: valueString(filter.value),
      }
    }
    else if (filter.type === 'relationship') {
      const rels = await client.getAll(
        'cdn/stories',
        {
          content_type: filter.rel,
          filter_query: {
            [filter.field]: {
              in: valueString(filter.value),
            },
          },
        },
      )

      const relsByValue = groupBy(rels, rel => rel.content[filter.field])
      const getUUIDs = (stories: { uuid: string }[] | undefined) => stories?.map(({ uuid }) => uuid) ?? []

      filterQuery[filter.rel] = {
        in: valueString(
          typeof filter.value === 'string'
            ? getUUIDs(relsByValue[filter.value])
            : filter.value.flatMap(value => getUUIDs(relsByValue[value])),
        ),
      }
    }
  }

  const portfolios = await client.getAll<WithRelationships<PortfolioStoryblok>>(
    'cdn/stories',
    {
      content_type: 'portfolio',
      // resolve_relations: [
      //   'portfolio.country',
      //   'portfolio.dealer',
      //   'portfolio.product',
      //   'portfolio.project_type',
      //   'portfolio.wall_types',
      // ],
      filter_query: filterQuery,
    },
  )

  return portfolios
}
