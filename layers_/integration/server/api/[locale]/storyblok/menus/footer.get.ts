import { getStoryblokClient } from '@integration/server/utils/storyblok'
import * as v from 'valibot'
import type { MenuItemSampleOrderingStoryblok, MenuItemStoryblok, MenuStoryblok } from '@integration/storyblok/data/components'
import { menuParamsSchema } from '@integration/server/types/menuSchema'
import { normalizeLink } from '@integration/server/utils/storyblok/normalizeLink'
import { site } from '@integration/algolia/indexes/portfolios'
import { isAllowed } from '@integration/server/utils/storyblok/roles'
import type { FooterLink, FooterColumn } from '~/types/footer'

export default defineEventHandler(async (event) => {
  const { user } = await requireRefreshedUserSession(event)
  const { locale } = await getValidatedRouterParams(event, params => v.parse(menuParamsSchema, params))
  const site = useRuntimeConfig().public.site

  try {
    const storyblokClient = getStoryblokClient()
    const { data: menu } = await storyblokClient.getStory<MenuStoryblok>(`${site}/menus/footer`, { language: locale, resolve_links: 'story' })

    if (!menu.story.content.items) {
      throw new Error('Menu not found')
    }
    // @ts-expect-error TODO: nuxt-auth-utils not typed correctly
    return normalizeFooter(menu.story.content.items, user.roles)
  }
  catch (error) {
    throw createError({
      statusCode: 500,
      statusMessage: 'Error fetching menu',
      data: error,
    })
  }
})

function normalizeFooter(items: MenuItemStoryblok[] | MenuItemSampleOrderingStoryblok[], roles: string[]): FooterColumn[] {
  return items.map(item => ({
    links: [normalizeFooterLink(item, roles)],
    brand: site,
  }))
}

function normalizeFooterLink(item: MenuItemStoryblok | MenuItemSampleOrderingStoryblok, roles: string[]): FooterLink {
  if (item.component === 'menu_item_sample_ordering') {
    return {
      title: item.title,
      links: item.items?.map(item => normalizeFooterLink(item, roles)) || [],
      disabled: undefined, // !isAllowed(['sample_role], roles),
    }
  }

  return {
    title: item.link?.cached_url ? normalizeLink(item, 'story') : item.title,
    description: item.description,
    links: item.items?.map(item => normalizeFooterLink(item, roles)) || [],
    // @ts-expect-error optional chaining do not narrow types
    disabled: !isAllowed(item.link?.story?.tag_list, roles),
  }
}
