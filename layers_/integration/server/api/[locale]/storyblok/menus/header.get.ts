import { getStoryblokClient } from '@integration/server/utils/storyblok'
import * as v from 'valibot'
import type { MenuItemSampleOrderingStoryblok, MenuItemStoryblok, MenuStoryblok } from '@integration/storyblok/data/components'
import type { MenuItem } from '@design-system/components/Organisms/Menu/OrganismsMenuDropdown.props'
import { menuParamsSchema } from '@integration/server/types/menuSchema'
import { normalizeLink } from '@integration/server/utils/storyblok/normalizeLink'
import { isAllowed } from '@integration/server/utils/storyblok/roles'

export default defineEventHandler(async (event) => {
  const { user } = await requireRefreshedUserSession(event)
  const { locale } = await getValidatedRouterParams(event, params => v.parse(menuParamsSchema, params))
  const site = useRuntimeConfig().public.site

  try {
    const storyblokClient = getStoryblokClient()
    const { data: menu } = await storyblokClient.getStory<MenuStoryblok>(`${site}/menus/header`, { language: locale, resolve_links: 'story' })

    if (!menu.story.content.items) {
      throw new Error('Menu not found')
    }

    // @ts-expect-error TODO: nuxt-auth-utils not typed correctly
    return normalizeMenu(menu.story.content.items, user.roles)
  }
  catch (error) {
    console.error(error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Error fetching menu',
      data: error,
    })
  }
})

/**
 * Utility function to map API response to MenuItem[]
 */
function normalizeMenu(menu: (MenuItemStoryblok | MenuItemSampleOrderingStoryblok)[], roles: string[]): MenuItem[] {
  return menu.map((item) => {
    return {
      items: item.items?.length ? normalizeMenu(item.items, roles) : undefined,
      ...normalizeLink(item, 'story'),
      // @ts-expect-error optional chaining do not narrow types
      hidden: !isAllowed(item.link?.story?.tag_list, roles),
    }
  })
}
