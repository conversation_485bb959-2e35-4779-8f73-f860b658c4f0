import * as v from 'valibot'
import { getStoryblokClient } from '@integration/server/utils/storyblok'

// MARK: - Common Pages
import { pagesHomepageMap } from '@integration/components/Pages/PagesHomepage.map'
import { pagesResourceCategoryMap } from '@integration/components/Pages/PagesResourceCategory.map'

// MARK: - Modernfold Pages
import { sampleOrderingFormMap } from '@modernfold/components/Pages/PagesSampleOrderingForm.map'
import { customerSupportTroubleReportMap } from '@modernfold/components/Pages/PagesCustomerSupportTroubleReport.map'
import { troubleReportsMap } from '@modernfold/components/Pages/PagesTroubleReports.map'
import { pagesShippingScheduleMap } from '@modernfold/components/Pages/PagesShippingSchedule.map'
import { pagesCOMRequestMap } from '@modernfold/components/Pages/COM/PagesCOMRequest.map'
import { pagesWarrantyMap } from '@modernfold/components/Pages/Warranty/PagesWarranty.map'
import { pagesAccountManagementShipmentsMap } from '@modernfold/components/Pages/PagesAccountManagementShipments.map'
import { pagesAccountManagementInvoicesMap } from '@modernfold/components/Pages/PagesAccountManagementInvoices.map'
import { comRequestsMap } from '@modernfold/components/Pages/COM/PagesCOMRequests.map'
import { comAcoustiSealCalculatorMap } from '@modernfold/components/Pages/COM/PagesCOMAcoustiSealCalculator.map'
import { pagesCOMPreapprovedCoveringListMap } from '@modernfold/components/Pages/COM/PagesCOMPreapprovedCoveringList.map'
import { ordersAcknowledgmentsMap } from '@modernfold/components/Pages/PagesOrdersAcknowledgments.map'

// MARK: - Skyfold Pages
import { pagesComMap } from '@modernfold/components/Pages/COM/PagesCOM.map'
import { pagesComNewNumberMap } from '@modernfold/components/Pages/COM/PagesCOMNewNumber.map'
import { pagesPortfolioMap } from '@skyfold/components/Pages/PagesPortfolio.map'
import { pagesPortfolioListingMap } from '@skyfold/components/Pages/PagesPortfolioListingPage.map'
import { pagesAiaFormProgramCompletionMap } from '@skyfold/components/Pages/PagesAiaForm.map'

// MARK: - Types
import type { PageStoryblok } from '@integration/server/types/storyblok'
import type { ISbStory } from 'storyblok-js-client'

const paramsSchema = v.object({
  locale: v.string(),
  path: v.string(),
})

export default defineEventHandler(async (event) => {
  const { user } = await requireRefreshedUserSession(event)

  const { locale, path } = await getValidatedRouterParams(event, params => v.parse(paramsSchema, params))
  const site = useRuntimeConfig().public.site

  const relations = {
    portfolio: [
      'country',
      'dealer',
      'product',
      'project_type',
      'project_of_the_month',
      'project_of_the_month.content.dealer',
      'wall_types',
    ],
    homepage: [
      'what_is_new',
      'what_is_new_see_all',
    ],
  } as const

  const resolveRelations = Object.entries(relations)
    .flatMap(
      ([content, rels]) =>
        rels.map(rel =>
          `${content}.${rel}` as const,
        ),
    )

  const storyblokClient = getStoryblokClient()
  const { data: page } = await storyblokClient.getStory<PageStoryblok>(
    `${site}/pages/${path}`,
    {
      language: locale,
      resolve_relations: resolveRelations,
    },
  )

  // @ts-expect-error TODO: nuxt-auth-utils not typed correctly
  if (!isAllowed(page.story.tag_list, user.roles)) {
    throw createError({
      status: 403,
    })
  }

  const mappedData = mapData(page)

  if (!mappedData) {
    throw createError({
      status: 404,
      message: `component ${page.story.content.component} is not mapped`,
    })
  }

  return mappedData
})

export const mapData = (page: ISbStory<PageStoryblok>['data']) => {
  switch (page.story.content.component) {
    case 'customer_support_sample_ordering':
      return sampleOrderingFormMap(page.story.content)
    case 'customer_support_trouble_report':
      return customerSupportTroubleReportMap(page.story.content)
    case 'customer_support_trouble_reports':
      return troubleReportsMap(page.story.content)
    case 'customer_support_warranty':
      return pagesWarrantyMap(page.story.content)
    case 'project_management_com':
      return pagesComMap(page.story.content)
    case 'project_management_com_new':
      return pagesComNewNumberMap(page.story.content)
    case 'project_management_com_requests':
      return comRequestsMap(page.story.content)
    case 'project_management_com_request':
      return pagesCOMRequestMap(page.story.content)
    case 'account_management_shipments':
      return pagesAccountManagementShipmentsMap(page.story.content)
    case 'portfolio':
      return pagesPortfolioMap(page.story.content)
    case 'portfolio_listing_page':
      return pagesPortfolioListingMap(page.story.content)
    case 'homepage':
      return pagesHomepageMap(page.story.content)
    case 'resource_category':
    case 'resource_folder':
      return pagesResourceCategoryMap(page.story.content, page.story)
    case 'account_management_invoices':
      return pagesAccountManagementInvoicesMap(page.story.content)
    case 'project_management_com_acousti_seal_calculator':
      return comAcoustiSealCalculatorMap(page.story.content)
    case 'project_management_shipping_schedule':
      return pagesShippingScheduleMap(page.story.content)
    case 'project_management_com_preapproved':
      return pagesCOMPreapprovedCoveringListMap(page.story.content)
    case 'aia_form_program_completion':
      return pagesAiaFormProgramCompletionMap(page.story.content)
    case 'project_management_orders_acknowledgments':
      return ordersAcknowledgmentsMap(page.story.content)
  }
}

export type MappedData = NonNullable<ReturnType<typeof mapData>>
