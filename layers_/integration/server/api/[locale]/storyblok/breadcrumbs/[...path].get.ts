import * as v from 'valibot'
import { pathToSlug } from '@integration/utils/storyblok'
import type { BreadcrumbsItemStoryblok } from '@integration/storyblok/data/components'

const paramsSchema = v.object({
  locale: v.string(),
  path: v.string(),
})

export default defineEventHandler(async (event) => {
  const { locale, path } = await getValidatedRouterParams(event, params => v.parse(paramsSchema, params))
  const site = useRuntimeConfig().public.site

  const breadcrumbsSlug = pathToSlug(path)

  const storyblokClient = getStoryblokClient()
  const { data } = await storyblokClient.getStory(
    `${site}/breadcrumbs/${breadcrumbsSlug}`,
    {
      language: locale,
    },
  )

  return data.story.content.items.map((item: BreadcrumbsItemStoryblok) => ({
    label: item.label,
    url: item.link?.cached_url ?? '',
  }))
})
