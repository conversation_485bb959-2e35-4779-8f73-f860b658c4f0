import { getStoryblokClient } from '@integration/server/utils/storyblok'
import { removeLastSegment } from '@integration/algolia/indexes/resources'
import type { ISbStoryData } from 'storyblok-js-client'
import * as v from 'valibot'
import type { ResourceCategoryStoryblok, ResourceFolderStoryblok } from '@integration/storyblok/data/components'

const paramsSchema = v.object({
  locale: v.string(),
  path: v.string(),
})

export default defineEventHandler(async (event) => {
  const { locale, path } = await getValidatedRouterParams(event, params => v.parse(paramsSchema, params))
  const site = useRuntimeConfig().public.site

  const storyblokClient = getStoryblokClient()

  let rootCategory: ISbStoryData<ResourceCategoryStoryblok> | null = null
  let parentPath = path
  do {
    const { data } = await storyblokClient.getStory<ResourceCategoryStoryblok | ResourceFolderStoryblok>(`${site}/pages/${parentPath}`, { language: locale })
    if (data.story.content.component === 'resource_category') {
      rootCategory = data.story as ISbStoryData<ResourceCategoryStoryblok>
    }
    parentPath = removeLastSegment(parentPath)
  } while (!rootCategory)

  const folders = await storyblokClient.getAll<ResourceFolderStoryblok>('cdn/stories', { content_type: 'resource_folder', starts_with: rootCategory.full_slug.slice(3), language: locale })

  const foldersByParentSlug = folders.reduce<Record<string, ISbStoryData<ResourceFolderStoryblok>[]>>((foldersByParentSlug, folder) => {
    const parentSlug = removeLastSegment(folder.full_slug)
    const siblingsFolders = foldersByParentSlug[parentSlug] ?? []
    return {
      ...foldersByParentSlug,
      [parentSlug]: [...siblingsFolders, folder],
    }
  }, {})

  type WithSubfolders<Item> = Item & { folders: WithSubfolders<ISbStoryData<ResourceFolderStoryblok>>[] }
  const addSubFolders = <Item extends { full_slug: string }>(item: Item): WithSubfolders<Item> => ({
    ...item,
    folders: foldersByParentSlug[item.full_slug]?.map(addSubFolders) ?? [],
  })

  const hierarchy = addSubFolders(rootCategory)

  return hierarchy
})
