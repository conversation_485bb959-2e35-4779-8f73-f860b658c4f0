import { site, indexData } from '@integration/algolia/indexes/portfolios'

export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig(event)

  const currentSite = config.public.site

  if (site !== currentSite) {
    throw createError({
      status: 404,
    })
  }

  const languages = config.public.i18nConfig.locales
    // @ts-expect-error optional chaining is not allowed on unknown properties
    ?.map(locale => locale?.code)
    ?.filter(Boolean)

  if (!languages?.length) {
    throw createError({
      status: 500,
    })
  }

  try {
    const accessToken = config.storyblok.accessToken

    for (const locale of languages) {
      await indexData(locale, accessToken)
    }
  }
  catch (error) {
    console.error(error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Error indexing portfolios',
      data: error,
    })
  }
})
