import { createBreadcrumbs } from '@integration/storyblok/script/createBreadcrumbs'

export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig(event)

  const deliveryAccessToken = config.storyblok.accessToken
  const managmentAccessToken = config.storyblok.managementAccessToken
  const spaceId = config.storyblok.spaceId
  const site = config.public.site
  const languages: string[] = config.public.i18nConfig.locales.map((locale) => {
    if (typeof locale === 'object' && locale !== null && 'code' in locale) return locale.code
  }).filter(Boolean) ?? ['en']

  try {
    const result = await createBreadcrumbs({ deliveryAccessToken, managmentAccessToken, spaceId, site, languages })

    return result
  }
  catch (error) {
    console.error(error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Error creating breadcrumbs',
      data: error,
    })
  }
})
