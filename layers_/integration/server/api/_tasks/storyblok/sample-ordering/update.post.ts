import { updateSampleOrderingVendors } from '@integration/storyblok/script/updateVendors'

export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig(event)
  const managmentAccessToken = config.storyblok.managementAccessToken
  const spaceId = config.storyblok.spaceId

  try {
    const result = await updateSampleOrderingVendors({ managmentAccessToken, spaceId })

    if (!result?.success) {
      throw new Error(result?.message)
    }

    return result
  }

  catch (error) {
    if (error instanceof Error) {
      throw createError({
        statusCode: 500,
        statusMessage: `Unexpected server error`,
        data: error,
      })
    }
  }
})
