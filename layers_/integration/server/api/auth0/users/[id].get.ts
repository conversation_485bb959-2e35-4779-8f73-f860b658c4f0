import type { TUser } from '~~/layers_/integration/types/auth'

export default defineEventHandler(async (event) => {
  const userId = event.context.params?.id

  const token = await generateAuth0Token()

  try {
    const data = await $fetch(`${process.env.AUTH0_AUDIENCE}users/${userId}`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token.access_token}`,
      },
    })

    return data as TUser
  }
  catch (error) {
    throw createError({
      statusCode: 500,
      statusMessage: `Failed to change password: ${error}`,
    })
  }
})
