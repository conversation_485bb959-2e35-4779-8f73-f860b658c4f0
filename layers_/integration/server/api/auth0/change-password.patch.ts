import * as v from 'valibot'
import { changePasswordSchema } from '@integration/types/changePassword'
import { generateAuth0Token } from '@integration/server/utils/generateToken'

export default defineEventHandler(async (event) => {
  const session = await requireRefreshedUserSession(event)
  // @ts-expect-error TODO: nuxt-auth-utils not typed correctly
  const userId = session.user.sub

  const body = await readValidatedBody(event, body => v.parse(changePasswordSchema, body))

  const token = await generateAuth0Token()

  try {
    await $fetch(`${process.env.AUTH0_AUDIENCE}users/${userId}`, {
      method: 'PATCH',
      headers: {
        Authorization: `Bearer ${token.access_token}`,
      },
      body,
    })
  }
  catch (error) {
    throw createError({
      statusCode: 500,
      statusMessage: `Failed to change password: ${error}`,
    })
  }
})
