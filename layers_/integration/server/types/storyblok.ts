import type {
  CustomerSupportSampleOrderingStoryblok,
  CustomerSupportTroubleReportStoryblok,
  CustomerSupportTroubleReportsStoryblok,
  ResourceCategoryStoryblok,
  ResourceFolderStoryblok,
  PortfolioStoryblok,
  PortfolioListingPageStoryblok,
  HomepageStoryblok,
  ProjectManagementComStoryblok,
  ProjectManagementComNewStoryblok,
  ProjectManagementShippingScheduleStoryblok,
  ProjectManagementComRequestStoryblok,
  ProjectManagementComRequestsStoryblok,
  CustomerSupportWarrantyStoryblok,
  AccountManagementShipmentsStoryblok,
  AccountManagementInvoicesStoryblok,
  ProjectManagementComAcoustiSealCalculatorStoryblok,
  ProjectManagementComPreapprovedStoryblok,
  AiaFormProgramCompletionStoryblok,
  ProjectManagementOrderInformationStoryblok,
  ProjectManagementOrdersAcknowledgmentsStoryblok,
} from '@integration/storyblok/data/components'
import type { WithRelationships } from '@integration/types/storyblok'

export type PageStoryblok = WithRelationships<
  | CustomerSupportSampleOrderingStoryblok
  | CustomerSupportTroubleReportStoryblok
  | CustomerSupportTroubleReportsStoryblok
  | ProjectManagementComRequestsStoryblok
  | ResourceCategoryStoryblok
  | ResourceFolderStoryblok
  | PortfolioStoryblok
  | PortfolioListingPageStoryblok
  | HomepageStoryblok
  | ProjectManagementComStoryblok
  | ProjectManagementComNewStoryblok
  | ProjectManagementShippingScheduleStoryblok
  | ProjectManagementComRequestStoryblok
  | CustomerSupportWarrantyStoryblok
  | AccountManagementShipmentsStoryblok
  | AccountManagementInvoicesStoryblok
  | ProjectManagementComAcoustiSealCalculatorStoryblok
  | ProjectManagementComPreapprovedStoryblok
  | AiaFormProgramCompletionStoryblok
  | ProjectManagementOrderInformationStoryblok
  | ProjectManagementOrdersAcknowledgmentsStoryblok
>

export type MappedContent<Component extends string, Props> = {
  component: Component
} & Props
