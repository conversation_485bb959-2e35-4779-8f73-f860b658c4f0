// https://nuxt.com/docs/api/configuration/nuxt-config
import { fileURLToPath } from 'url'
import { env } from '../../env'

export default defineNuxtConfig({
  extends: ['../forms'],
  modules: ['nuxt-auth-utils'],
  runtimeConfig: {
    storyblok: {
      accessToken: env.STORYBLOK_ACCESS_TOKEN,
      managementAccessToken: env.STORYBLOK_MANAGMENT_API_ACCESS_TOKEN,
      spaceId: env.STORYBLOK_SPACE_ID,
    },
    oauth: {
      auth0: {
        clientId: env.AUTH0_CLIENT_ID,
        clientSecret: env.AUTH0_CLIENT_SECRET,
        domain: env.AUTH0_DOMAIN,
        audience: env.AUTH0_AUDIENCE,
        redirectURL: `${env.BASE_URL}/auth0/callback`,
        logoutURL: env.BASE_URL,
      },
    },
  },
  alias: {
    // TODO: fix dependency direction
    '@modernfold': fileURLToPath(
      new URL('../modernfold', import.meta.url),
    ),
    '@skyfold': fileURLToPath(
      new URL('../skyfold', import.meta.url),
    ),
    '@integration': fileURLToPath(new URL('./', import.meta.url)),
    '@forms': fileURLToPath(
      new URL('../forms', import.meta.url),
    ),
    '@design-system': fileURLToPath(
      new URL('../design-system', import.meta.url),
    ),
  },
})
