/**
 * Sanitizes a full slug by removing the language and site prefixes,
 * and ensures it ends with a single trailing slash
 */
export const sanitizeSlug = (slug: string, site: string, lang?: string): string => {
  const langPrefix = lang ? `${lang}/` : ''
  const prefixRegex = new RegExp(`^${langPrefix}${site}/pages/`)
  const withoutPrefix = slug.replace(prefixRegex, '')
  const cleaned = withoutPrefix.replace(/^\/+|\/+$/g, '')
  return cleaned ? `${cleaned}/` : ''
}

/**
 * Removes the language prefix from a full slug
 */
export const removeLangFromFullSlug = (slug: string): string => {
  return slug.replace(/^[a-z]{2}\//, '')
}

/**
 * Converts a URL path to a slug-safe format using double underscores.
 * The homepage path '/' is represented as '__'
 */
export const pathToSlug = (path: string): string => {
  const cleaned = path.replace(/^\/+|\/+$/g, '')
  return cleaned === '' ? '__' : cleaned.replace(/\//g, '__')
}

/**
 * Converts a slug-safe string back to a URL path.
 * The slug '__' is converted back to '/'
 */
export const slugToPath = (slug: string): string => {
  return slug === '__' ? '/' : `/${slug.replace(/__/g, '/')}`
}
