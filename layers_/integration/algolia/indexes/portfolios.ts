import * as v from 'valibot'
import type { ISbStoryData } from 'storyblok-js-client'
import type { CountryStoryblok, ProductStoryblok, ProjectTypeStoryblok, ProjectTypeSubcategoriesStoryblok, WallTypesStoryblok, PortfolioStoryblok } from '@integration/storyblok/data/components'
import { createClient } from '@integration/storyblok/client'
import { getClient as getAlgoliaClient } from '@integration/algolia/client'
import { tryOrNull } from '@integration/utils/try'

type ItemsMap = Record<
  string,
  CountryStoryblok
  | ProductStoryblok
  | ProjectTypeStoryblok
  | ProjectTypeSubcategoriesStoryblok
  | WallTypesStoryblok
>

// settings

export const site = 'skyfold'
export const indexBaseName = 'portfolios'

export const indexSettings = {
  searchableAttributes: [
    'project_name',
    'skyfold_number',
  ],
  attributesForFaceting: [
    'searchable(state)',
    'searchable(country.name)',
    'searchable(client_name)',
    'searchable(location)',
    'searchable(product.name)',
    'wall_types.name',
    'project_type_sub_categories.name',
    'finish_type',
    'newRetrofit',
    'award_winning',
    'searchable(installation_date)',
    'searchable(architect_design)',
    'searchable(general_contractor)',
    'project_type.name',
    'searchable(dealer)',
    'searchable(panel_finish)',
    'searchable(industry_list)',
    'special_finish',
  ],
}

// algolia schemas
const assetStoryblokSchema = v.object({
  alt: v.union([v.string(), v.null_()]),
  copyright: v.optional(v.union([v.string(), v.null_()])),
  fieldtype: v.literal('asset'),
  id: v.number(),
  filename: v.union([v.string(), v.null_()]),
  name: v.string(),
  title: v.union([v.string(), v.null_()]),
  focus: v.union([v.string(), v.null_()]),
  meta_data: v.optional(
    v.record(v.string(), v.unknown()),
  ),
  source: v.optional(v.union([v.string(), v.null_()])),
  is_external_url: v.optional(v.boolean()),
  is_private: v.optional(v.boolean()),
  src: v.optional(v.string()),
  updated_at: v.optional(v.string()),
  width: v.optional(v.union([v.number(), v.null_()])),
  height: v.optional(v.union([v.number(), v.null_()])),
  aspect_ratio: v.optional(v.union([v.number(), v.null_()])),
  public_id: v.optional(v.union([v.string(), v.null_()])),
  content_type: v.optional(v.string()),
})
export type AssetAlgolia = v.InferOutput<typeof assetStoryblokSchema>

export const portfolioSchema = v.object({
  objectID: v.string(),
  content_type: v.literal('portfolio'),
  component: v.literal('portfolio'),

  full_slug: v.string(),

  _uid: v.string(),
  state: v.string(),
  video: assetStoryblokSchema,
  dealer: v.string(),
  photos: v.array(assetStoryblokSchema),
  country: v.object({
    id: v.string(),
    _uid: v.string(),
    code: v.string(),
    name: v.string(),
    component: v.literal('country'),
  }),
  product: v.object({
    _uid: v.string(),
    name: v.string(),
    website: v.string(),
    component: v.literal('product'),
    product_id: v.string(),
  }),
  location: v.string(),
  project_id: v.string(),
  slide_show: assetStoryblokSchema,
  wall_types: v.object({
    id: v.string(),
    _uid: v.string(),
    name: v.string(),
    component: v.literal('wall_types'),
  }),
  finish_type: v.string(),
  newRetrofit: v.union([v.literal(''), v.literal('N'), v.literal('R')]),
  panel_finish: v.string(),
  project_name: v.string(),
  project_type: v.object({
    id: v.string(),
    _uid: v.string(),
    name: v.string(),
    component: v.literal('project_type'),
  }),
  project_type_sub_categories: v.object({
    id: v.string(),
    _uid: v.string(),
    name: v.string(),
    component: v.literal('project_type_subcategories'),
  }),
  award_winning: v.string(),
  industry_list: v.string(),
  largest_size_h: v.string(),
  largest_size_w: v.string(),
  skyfold_number: v.string(),
  number_of_walls: v.string(),
  architect_design: v.string(),
  installation_date: v.string(),
  general_contractor: v.string(),
})
export type PortfolioAlgolia = v.InferOutput<typeof portfolioSchema>

// script
export const indexData = async (language: string, accessToken: string) => {
  // fetch all data
  const client = createClient({ type: 'content', accessToken })
  const country = await client.getAll<CountryStoryblok>('cdn/stories', { content_type: 'country', language })
  const product = await client.getAll<ProductStoryblok>('cdn/stories', { content_type: 'product', language })
  const project_type = await client.getAll<ProjectTypeStoryblok>('cdn/stories', { content_type: 'project_type', language })
  const project_type_subcategories = await client.getAll<ProjectTypeSubcategoriesStoryblok>('cdn/stories', { content_type: 'project_type_subcategories', language })
  const wall_types = await client.getAll<WallTypesStoryblok>('cdn/stories', { content_type: 'wall_types', language })

  // map data
  const itemsByuuid = [...country, ...product, ...project_type, ...project_type_subcategories, ...wall_types].reduce<
    ItemsMap
  >(
    (items, item) => ({
      ...items,
      [item.uuid]: item.content,
    }),
    {},
  )

  const portfolios = await client.getAll<PortfolioStoryblok>('cdn/stories', { content_type: 'portfolio', language })

  const portfolioObjects = portfolios.map(tryOrNull(mapPortfolio(itemsByuuid))).filter(Boolean)

  const algoliaClient = getAlgoliaClient()
  const indexName = `${site}__${indexBaseName}__${language}`
  await algoliaClient.saveObjects({ indexName, objects: portfolioObjects })
  await algoliaClient.setSettings({ indexName, indexSettings })
}

// connector?
const getUnlocalizedFullSlug = (res: ISbStoryData) => res.lang === 'default' ? res.full_slug : res.full_slug.replace(`${res.lang}/`, '')

const mapPortfolio = (itemsByuuid: ItemsMap) => (portfolio: ISbStoryData<PortfolioStoryblok>): Partial<PortfolioAlgolia> => {
  const resolveRelationship = (key: string) => {
    const uuidOrData = portfolio.content[key]

    if (typeof uuidOrData === 'string')
      return itemsByuuid[uuidOrData]

    // @ts-expect-error is safe even with the type error
    return uuidOrData?.content
  }

  return {
    ...portfolio.content,

    objectID: `${portfolio.id}`,
    content_type: portfolio.content.component,

    full_slug: getUnlocalizedFullSlug(portfolio),

    ...portfolio.content,
    product: resolveRelationship('product'),
    project_type: resolveRelationship('project_type'),
    project_type_sub_categories: resolveRelationship('project_type_sub_categories'),
    country: resolveRelationship('country'),
    wall_types: resolveRelationship('wall_types'),
  }
}
