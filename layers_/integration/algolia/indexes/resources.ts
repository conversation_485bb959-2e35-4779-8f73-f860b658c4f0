import * as v from 'valibot'
import type { ISbStoryData } from 'storyblok-js-client'
import type { ResourceCategoryStoryblok, ResourceFolderStoryblok, ResourceStoryblok } from '@integration/storyblok/data/components'
import { createClient } from '@integration/storyblok/client'
import { getClient as getAlgoliaClient } from '@integration/algolia/client'
import { tryOrNull } from '@integration/utils/try'

type ItemsMap = Record<
  string,
  ISbStoryData<ResourceFolderStoryblok>
  | ISbStoryData<ResourceCategoryStoryblok>
>

// settings
export const sites = ['skyfold', 'modernfold'] as const

export const indexBaseName = 'resources'

const indexSettings = {
  searchableAttributes: [
    'name',
    'description',
    'breadcrumbs.name',
  ],
  attributesForFaceting: [
    'content_type',
    'parent_full_slug',
    'category_full_slug',
    'hierarchicalBreadcrumbs.lvl1',
    'hierarchicalBreadcrumbs.lvl2',
    'hierarchicalBreadcrumbs.lvl3',
    'hierarchicalBreadcrumbs.lvl4',
    'hierarchicalBreadcrumbs.lvl5',
  ],
}

// algolia schemas
const breadcrumbsSchema = v.array(v.object({
  name: v.string(),
  full_slug: v.string(),
}))
export type Breadcrumbs = v.InferOutput<typeof breadcrumbsSchema>

const hierarchicalBreadcrumbsSchema = v.object({
  lvl1: v.string(),
  lvl2: v.string(),
  lvl3: v.string(),
  lvl4: v.string(),
  lvl5: v.string(),
})
export type HierarchicalBreadcrumbs = v.InferOutput<typeof hierarchicalBreadcrumbsSchema>

export const objectResourceCategorySchema = v.object({
  objectID: v.string(),
  content_type: v.literal('resource_category'),

  full_slug: v.string(),
  name: v.string(),
  description: v.optional(v.string()),
})
export type ResourceCategoryAlgolia = v.InferOutput<typeof objectResourceCategorySchema>

export const objectResourceFolderSchema = v.object({
  objectID: v.string(),
  content_type: v.literal('resource_folder'),

  full_slug: v.string(), // not really used?
  name: v.string(), // display name
  parent_full_slug: v.string(), // used to filter by parent
  parent_name: v.string(), // not really used
  category_full_slug: v.string(), // used to group by category
  category_name: v.string(), // display name
  description: v.optional(v.string()),
  breadcrumbs: breadcrumbsSchema,
  hierarchicalBreadcrumbs: hierarchicalBreadcrumbsSchema,
})
export type ResourceFolderAlgolia = v.InferOutput<typeof objectResourceFolderSchema>

export const objectResourceSchema = v.object({
  objectID: v.string(),
  content_type: v.literal('resource'),

  full_slug: v.string(),
  name: v.string(),
  parent_full_slug: v.string(),
  parent_name: v.string(),
  category_full_slug: v.string(),
  category_name: v.string(),
  breadcrumbs: breadcrumbsSchema,
  hierarchicalBreadcrumbs: hierarchicalBreadcrumbsSchema,

  // Asset
  asset: v.object({
    alt: v.nullable(v.string()),
    copyright: v.optional(v.nullable(v.string())),
    id: v.number(),
    filename: v.nullable(v.string()),
    name: v.string(),
    title: v.nullable(v.string()),
    src: v.optional(v.string()),
    content_type: v.optional(v.string()),
  }),
})
export type ResourceAlgolia = v.InferOutput<typeof objectResourceSchema>

// script
export const indexData = async (site: string, language: string, accessToken: string) => {
  // fetch all data
  const client = createClient({ type: 'content', accessToken })
  const resources = await client.getAll<ResourceStoryblok>('cdn/stories', { content_type: 'resource', starts_with: `${site}/`, language })
  const folders = await client.getAll<ResourceFolderStoryblok>('cdn/stories', { content_type: 'resource_folder', starts_with: `${site}/`, language })
  const categories = await client.getAll<ResourceCategoryStoryblok>('cdn/stories', { content_type: 'resource_category', starts_with: `${site}/`, language })

  // map data
  const itemsByFullSlug = [...categories, ...folders].reduce<
    ItemsMap
  >(
    (items, item) => ({
      ...items,
      [item.full_slug]: item,
    }),
    {},
  )
  const resourcesObjects = resources.map(tryOrNull(mapResource(itemsByFullSlug))).filter(Boolean)
  const resourceFoldersObjects = folders.map(tryOrNull(mapFolder(itemsByFullSlug))).filter(Boolean)
  const resourcesCategoriesObjects = categories.map(tryOrNull(mapCategory)).filter(Boolean)

  const algoliaClient = getAlgoliaClient()
  const indexName = `${site}__${indexBaseName}__${language}`
  await algoliaClient.saveObjects({ indexName, objects: [...resourcesObjects, ...resourceFoldersObjects, ...resourcesCategoriesObjects] })
  await algoliaClient.setSettings({ indexName, indexSettings })
}

// connector?
export const removeLastSegment = (path: string) => path.split('/').slice(0, path.endsWith('/') ? -2 : -1).join('/').concat('/')

const mapCategory = (category: ISbStoryData<ResourceCategoryStoryblok>): ResourceCategoryAlgolia => ({
  objectID: `${category.id}`,
  content_type: category.content.component,

  full_slug: category.full_slug,
  name: category.content.name ?? '',
  description: category.content.description,
})

const mapFolder = (
  items: ItemsMap,
) => (folder: ISbStoryData<ResourceFolderStoryblok>): ResourceFolderAlgolia => {
  const { breadcrumbs, hierarchicalBreadcrumbs } = getBreadcrumbs(items, folder)

  return {
    objectID: `${folder.id}`,
    content_type: folder.content.component,

    full_slug: folder.full_slug,
    name: folder.content.name ?? '',
    parent_full_slug: breadcrumbs.at(-2)!.full_slug,
    parent_name: breadcrumbs.at(-2)!.name,
    category_full_slug: breadcrumbs.at(0)!.full_slug,
    category_name: breadcrumbs.at(0)!.name,
    description: folder.content.description,
    breadcrumbs,
    hierarchicalBreadcrumbs,
  }
}

const mapResource = (
  items: ItemsMap,
) => (resource: ISbStoryData<ResourceStoryblok>): ResourceAlgolia => {
  const { breadcrumbs, hierarchicalBreadcrumbs } = getBreadcrumbs(items, resource)

  return {
    objectID: `${resource.id}`,
    content_type: resource.content.component,

    full_slug: resource.full_slug,
    name: resource.content.asset?.filename ?? '', // TODO: which field?
    parent_full_slug: breadcrumbs.at(-2)!.full_slug,
    parent_name: breadcrumbs.at(-2)!.name,
    category_full_slug: breadcrumbs.at(0)!.full_slug,
    category_name: breadcrumbs.at(0)!.name,
    breadcrumbs,
    hierarchicalBreadcrumbs,

    asset: resource.content.asset,
  }
}

const getBreadcrumbs = (items: ItemsMap, item: ISbStoryData) => {
  const breadcrumbs: Breadcrumbs = [{
    name: item.content.name ?? item.content.asset?.filename ?? '', // TODO: which field?'',
    full_slug: item.full_slug,
  }]

  let parentFullSlug = item.full_slug
  let categoryReached = false

  do {
    parentFullSlug = removeLastSegment(parentFullSlug)
    const parentItem = items[parentFullSlug]

    if (!parentItem) {
      console.info(items)
      throw new Error(`Parent item not found for "${item.full_slug}": "${parentFullSlug}" not found`)
    }

    breadcrumbs.unshift({
      name: parentItem.content.name ?? '',
      full_slug: parentItem.full_slug,
    })

    categoryReached = parentItem.content.component === 'resource_category'
  } while (!categoryReached)

  const hierarchicalBreadcrumbs: HierarchicalBreadcrumbs = breadcrumbs
    .slice(1, item.content.component === 'resource' ? -1 : undefined)
    .reduce((hierarchicalBreadcrumbs, _, index, breadcrumbs) => ({
      ...hierarchicalBreadcrumbs,
      [`lvl${index + 1}`]: breadcrumbs.slice(0, index + 1).map(item => item.name).join(' > '),
    }), {} as HierarchicalBreadcrumbs)

  return { breadcrumbs, hierarchicalBreadcrumbs }
}
