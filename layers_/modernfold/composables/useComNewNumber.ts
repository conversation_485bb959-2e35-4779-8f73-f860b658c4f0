import { reset } from '@formkit/core'
import { useFormKitContextById } from '@formkit/vue'
import * as v from 'valibot'
import { COMNewNumberBodySchema } from '@modernfold/server/types/comNewNumber'

type FormState = {
  status: 'idle' | 'submitting' | 'success' | 'error'
  message: string
  showResultModal: boolean
}

export const useComNewNumber = async () => {
  const { selectedDistributor, selectedDistributorName, userSettings } = useUserSettings()
  const formId = 'form-com-new-number'
  const ctx = useFormKitContextById(formId)
  const { ts } = useI18n()

  const formState = ref<FormState>({
    status: 'idle',
    message: '',
    showResultModal: false,
  })

  const user = userSettings.value?.userSettings

  const getDefaultFormData = () => {
    return {
      RequestDate: new Date().toISOString(),
      Email: userSettings.value?.userSettings?.Email ?? '',
      UserId: userSettings.value?.userSettings?.MPMUserId ?? '',
      DistributorId: selectedDistributorName.value,
      SubmittedBy: `${user?.FirstName} ${user?.LastName}`,
      Phone: userSettings.value?.userSettings?.Phone ?? '',
      ApprovalNotes: null,
    }
  }

  const form = reactive<{ data: Record<string, unknown> }>({
    data: getDefaultFormData(),
  })

  const { data } = useAsyncData(
    `${formId}__panel-and-coverings`,
    async () => {
      const [panelSkinsRaw, coveringsRaw] = await Promise.all([
        $fetch('/api/com/newnumber/panelskin'),
        $fetch('/api/com/newnumber/covering'),
      ])
      return { panelSkins: panelSkinsRaw, coverings: coveringsRaw }
    },
    {
      transform: (raw: { panelSkins: { Type: number, Name: string }[], coverings: { Type: number, Name: string }[] }) => ({
        panelSkins: raw?.panelSkins.map(skin => ({
          label: skin.Name,
          value: skin.Type,
        })),
        coverings: raw?.coverings.map(covering => ({
          label: covering.Name,
          value: covering.Type,
        })),
      }),
    },
  )

  const panelSkins = computed(() => data.value?.panelSkins ?? [])
  const coverings = computed(() => data.value?.coverings ?? [])

  const { data: manufacturers } = useAsyncData(
    `${formId}__manufacturers`,
    async () => {
      if (!form.data.Covering) return []
      const manufacturersRaw = await $fetch<{ AppManfID: number, Manufacturer: string }[]>(
        `/api/com/newnumber/manufacturers/${form.data.Covering}`,
      )
      return manufacturersRaw
    },
    { watch: [() => form.data.Covering],
      transform: manufacturersRaw => manufacturersRaw?.map(m => ({
        label: m.Manufacturer,
        value: m.AppManfID,
      })),
    },
  )

  const { data: patterns } = useAsyncData(
    `${formId}__patterns`,
    async () => {
      if (!form.data?.Manufacturer) return []
      const patternsRaw = await $fetch<{ PatternName: string }[]>(
        `/api/com/newnumber/pattern/${form.data.Manufacturer}`,
      )
      return patternsRaw
    },
    {
      watch: [() => form.data.Manufacturer],
      transform: patternsRaw =>
        patternsRaw?.map((p: { PatternName: string }) => ({
          label: p.PatternName,
          value: p.PatternName,
        })),
    },
  )

  watch(
    [() => form.data.Covering, () => form.data.Manufacturer],
    ([newCovering, newManufacturer], [oldCovering, oldManufacturer]) => {
      if (oldCovering && newCovering !== oldCovering) {
        form.data.Manufacturer = ''
        form.data.Pattern = ''
      }
      else if (oldManufacturer && newManufacturer !== oldManufacturer) {
        form.data.Pattern = ''
      }
    },
  )

  watch(selectedDistributorName, (name) => {
    form.data.DistributorId = name
  },
  { immediate: true },
  )

  const selectedManufacturerLabel = computed(() =>
    // @ts-expect-error formkit types
    manufacturers.value?.find(m => m.__original === form.data.Manufacturer)?.label,
  )

  function refineform(formData: typeof form.data) {
    return {
      ...formData,
      DistributorId: selectedDistributor.value,
      Quantity: Number(formData.Quantity) || null,
      Manufacturer: selectedManufacturerLabel.value,
      Notes: formData.Notes || null,
    }
  }

  async function sendData() {
    const parsedForm = v.parse(COMNewNumberBodySchema, refineform(form.data))
    formState.value.status = 'submitting'
    try {
      const response = await $fetch('/api/com/newnumber/request', {
        method: 'POST',
        body: parsedForm,
      })
      if (!response || !response.success) throw new Error()
      formState.value.status = 'success'
      formState.value.message = ts('com.request.submit.successMessage', { comId: response.comId })
      setTimeout(() => {
        resetData()
      }, 5000) // Reset after 5 seconds
    }
    catch (error) {
      formState.value.status = 'error'
      formState.value.message = ts('com.request.submit.errorMessage')
      console.error('Error submitting COM new number form', error)
    }
    finally {
      formState.value.showResultModal = true
    }
  }

  watch(() => formState.value.showResultModal, (newVal) => {
    if (newVal === false
      && (formState.value.status === 'success' || formState.value.status === 'error')) {
      resetData()
    }
  })

  function submitHandler() {
    if (!ctx?.value) return
    if (formState.value.status === 'idle') {
      sendData()
    }
  }

  function resetFormState() {
    formState.value = {
      status: 'idle',
      message: '',
      showResultModal: false,
    }
  }

  function resetData() {
    reset(formId)
    form.data = getDefaultFormData()
    resetFormState()
  }

  return {
    formId,
    formState,
    form,
    panelSkins,
    coverings,
    manufacturers,
    patterns,
    submitHandler,
    resetData,
    resetFormState,
  }
}
