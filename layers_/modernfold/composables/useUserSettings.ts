// TODO: user settings are a modernfold thing
import type { UserSettings } from '@modernfold/server/api/users/settings.get'
import type { Distributor } from '@modernfold/server/api/users/distributors.get'

const useSwitchCodeState = () => {
  const { user } = useUserSession()
  // @ts-expect-error TODO: nuxt-auth-utils not typed correctly
  const username = user.value?.email
  return useCookie<number | undefined>(`${username}-switchCode`, { default: undefined })
}

export const useUserSettings = () => {
  const requestFetch = useRequestFetch()
  const selectedDistributor = useSwitchCodeState()

  const init = async () => {
    const settings = await requestFetch<UserSettings>(`/api/users/settings`)
    const distributors = await requestFetch<Distributor>(`/api/users/distributors`)
    return {
      settings,
      distributors,
    }
  }

  const transform = ({ settings, distributors }: Awaited<ReturnType<typeof init>>) => ({
    settings,
    distributors: distributors.distributors.map(option => ({
      label: option.CompanyName,
      value: option.Id,
    })),
  })

  const data = useState<ReturnType<typeof transform> | null>('userSettings', () => null)

  const initialize = async () => {
    data.value = transform(await init())
    // If i have just one distributor I set it as default
    // I don't do it if there's more so i can chose one when i get redirected to /multiple-distributors page
    if (!selectedDistributor.value && distributors.value?.length === 1) {
      // assuming is the same
      selectedDistributor.value = distributorId.value
    }
  }

  const userSettings = computed(() => data.value?.settings)
  const distributors = computed(() => data.value?.distributors)
  const distributorId = computed(() => userSettings.value?.userSettings?.DistributorId)
  const selectedDistributorName = computed(() => {
    const selected = distributors.value?.find(distributor => distributor.value === selectedDistributor.value)
    return selected ? `${selected.value} ${selected.label}` : ''
  })

  return {
    selectedDistributor,
    selectedDistributorName,
    distributors,
    userSettings,
    initialize,
  }
}
