import { useFormKitContextById } from '@formkit/vue'
import * as v from 'valibot'
import countries from '@forms/data/countries.json'
import type { FormItem } from '@forms/types/types'
import { reset } from '@formkit/core'
import type { SampleOrderingStepId, SampleOrderingFormData } from '@modernfold/types/sampleOrdering'
import { SampleOrderingBodySchema } from '@modernfold/server/types/sampleordering'

export const useSampleOrdering = async (vendorId = '') => {
  const { $ts } = useI18n()

  const { userSettings } = useUserSettings()

  const { confirmGuardNavigation } = useNavigationGuard()

  const state = reactive<Record<SampleOrderingStepId, FormItem<SampleOrderingStepId, SampleOrderingFormData>>>({
    'shipping-information': {
      id: 'shipping-information',
      title: 'Shipping Information',
      data: {},
      isCompleted: false,
      isValid: false,
    },
    'shipping-method': {
      id: 'shipping-method',
      title: 'Shipping Method',
      data: {},
      isCompleted: false,
      isValid: false,
    },
    'quantity': {
      id: 'quantity',
      title: 'Quantity',
      data: {},
      isCompleted: false,
      isValid: false,
    },
    'confirm': {
      id: 'confirm',
      title: 'Confirm',
      data: {},
      isValid: false,
    },
  })

  const orderSteps = computed(() => Object.values(state).filter(step => step.id !== 'confirm'))

  const getFormId = (stepId: SampleOrderingStepId) => vendorId ? `${vendorId}-${stepId}` : stepId

  const { data, error } = await useFetch(`/api/distributors/${userSettings?.value?.userSettings?.DistributorId}`, {
    transform: (_rawData) => {
      if (!_rawData || !_rawData?.length) return {}

      const {
        CompanyName,
        Address1,
        City,
        State,
        ZipCode,
        Country,
        Phone,
      } = _rawData[0] ?? {}

      const name = userSettings.value?.userSettings?.FirstName
      const lastName = userSettings.value?.userSettings?.LastName
      const email = userSettings.value?.userSettings?.Email

      const sanitizedCountry = sanitizeString(Country) ?? ''
      const country = sanitizedCountry ? countries.find(country => sanitizedCountry === country.name) : null

      return ({
        Name: name && lastName ? `${name} ${lastName}` : '',
        Email: email ?? '',
        CompanyName: sanitizeString(CompanyName) ?? '',
        Address: sanitizeString(Address1) ?? '',
        City: sanitizeString(City) ?? '',
        State: sanitizeString(State) ?? '',
        Zip: sanitizeString(ZipCode) ?? '',
        Country: country?.name ?? '',
        __phoneNumber: {
          prefix: country?.name?.toLowerCase() === 'usa' ? '1' : '',
          number: sanitizeString(Phone) ?? '',
        },
      })
    },
  })

  fillShippingInformationWithDefault()

  function fillShippingInformationWithDefault() {
    if (!error.value && data.value) {
      state['shipping-information'].data = {
        ...state['shipping-information'].data,
        ...data.value,
      }
    }
  }

  function getFormData() {
    if (state['shipping-information'].data.__phoneNumber) {
      const { prefix, number } = state['shipping-information'].data?.__phoneNumber ?? {}
      state['shipping-information'].data.Phone = `${prefix ? `+${prefix} ` : ''}${number}`
    }

    const formData = {
      Address2: '', // TODO: to ask (if we don't pass it, the API will throw an error)
      VendorID: Number(vendorId),
      DistributorID: userSettings?.value?.userSettings?.DistributorId,
      ...state['shipping-information'].data,
      ...state['shipping-method'].data,
      ...state['quantity'].data,
    }

    const result = v.safeParse(SampleOrderingBodySchema, formData)

    if (!result.success) {
      console.error('Invalid form data', result)
      throw new Error()
    }

    return result.output
  }

  async function sendData() {
    state.confirm.data.sending = true

    try {
      const data = await $fetch('/api/samples/orders', {
        method: 'POST',
        body: getFormData(),
      })

      if (!data || !data.success) throw new Error()

      state.confirm.data.status = 'success'
      state.confirm.data.message = $ts('forms.confirm.success.message')
      confirmGuardNavigation(true)
    }
    catch (error) {
      const maybeWithData = error as Error & { data?: { message?: string } }

      state.confirm.data.status = 'warning'
      state.confirm.data.message = maybeWithData?.data?.message || $ts('forms.confirm.warning.message')
    }
    finally {
      state.confirm.isCompleted = true
      state.confirm.data.sending = false
    }
  }

  function submitStep(stepId: SampleOrderingStepId) {
    state.confirm.isCompleted = false
    if (stepId === 'confirm' && state.confirm?.isValid) {
      sendData()
      return
    }

    const formId = getFormId(stepId)

    const formContext = useFormKitContextById(formId)

    if (!formContext || !formContext.value) return

    state[stepId].isCompleted = formContext.value.state.valid
    submitForm(formId)
  }

  function updateStepValidity(stepId: SampleOrderingStepId, valid: boolean) {
    state[stepId].isValid = valid

    if (!valid) {
      state[stepId].isCompleted = valid
      if (state.confirm) state.confirm.isCompleted = false
    }
    else if (state.confirm) {
      state.confirm.isValid = Object.values(state)
        .filter(step => step.id !== 'confirm')
        .every(step => step.isValid)
    }
  }

  function resetConfirmStatus() {
    state.confirm.data.status = ''
    state.confirm.data.message = ''
  }

  function resetConfirmStep(invalid?: boolean) {
    if (typeof invalid === 'boolean') state.confirm.isValid = !invalid
    state.confirm.isCompleted = false
    resetConfirmStatus()
  }

  function resetData() {
    for (const step of orderSteps.value) {
      const formId = getFormId(step.id)
      step.isCompleted = false
      step.isValid = false
      reset(formId)
    }

    resetConfirmStep(true)

    fillShippingInformationWithDefault()
  }

  watch(() => state.confirm.isCompleted, (value) => {
    if (!value) resetConfirmStatus()
  })

  return {
    state,
    orderSteps,
    sendData,
    submitStep,
    updateStepValidity,
    resetData,
    resetConfirmStep,
  }
}
