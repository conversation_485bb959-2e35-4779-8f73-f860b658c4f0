import type { InvoiceRow, InvoiceFilters } from '@modernfold/types/invoices'

export const useInvoiceFilters = (rawData: Ref<InvoiceRow[]>) => {
  const formId = 'invoiceFiltersForm'

  const initialFilterState: InvoiceFilters = {
    purchaseOrderNumber: '',
    beginDate: undefined,
    endDate: undefined,
    jobNumber: '',
    jobName: '',
    invoiceNumber: '',
  }

  const formData = ref<InvoiceFilters>({ ...initialFilterState })
  const appliedFilters = ref<InvoiceFilters>({ ...initialFilterState })
  const dayjs = useDayjs()

  const filteredData = computed(() => {
    if (!rawData.value) return []

    return rawData.value.filter((invoice) => {
      if (appliedFilters.value.purchaseOrderNumber
        && !invoice.poNumber.toLowerCase().includes(appliedFilters.value.purchaseOrderNumber.toLowerCase())) {
        return false
      }

      if (appliedFilters.value.beginDate) {
        const invoiceDate = dayjs(invoice.invoiceDate)
        const beginDate = dayjs(appliedFilters.value.beginDate)

        if (invoiceDate.isBefore(beginDate, 'day')) {
          return false
        }
      }

      if (appliedFilters.value.endDate) {
        const invoiceDate = dayjs(invoice.invoiceDate)
        const endDate = dayjs(appliedFilters.value.endDate)

        if (invoiceDate.isAfter(endDate, 'day')) {
          return false
        }
      }

      if (appliedFilters.value.jobNumber
        && !invoice.jobNumber.toLowerCase().includes(appliedFilters.value.jobNumber.toLowerCase())) {
        return false
      }

      if (appliedFilters.value.jobName
        && !invoice.jobName.toLowerCase().includes(appliedFilters.value.jobName.toLowerCase())) {
        return false
      }

      if (appliedFilters.value.invoiceNumber
        && !invoice.invoiceNumber.toLowerCase().includes(appliedFilters.value.invoiceNumber.toLowerCase())) {
        return false
      }

      return true
    })
  })

  const hasFormFilters = computed(() =>
    Object.values(formData.value).some(Boolean),
  )

  const hasAppliedFilters = computed(() =>
    Object.values(appliedFilters.value).some(Boolean),
  )

  const handleSearch = () => {
    appliedFilters.value = { ...formData.value }
  }

  const resetFilters = () => {
    reset(formId)
    appliedFilters.value = { ...initialFilterState }
  }

  return {
    formId,
    formData,
    appliedFilters,
    filteredData,
    hasFormFilters,
    hasAppliedFilters,
    handleSearch,
    resetFilters,
  }
}
