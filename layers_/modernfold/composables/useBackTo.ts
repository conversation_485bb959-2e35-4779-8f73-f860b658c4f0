import type { BreadcrumbsItem } from '@integration/types/breadcrumbs'

export const useBackTo = (breadcrumbs: Array<BreadcrumbsItem>, index = -2) => {
  const { ts } = useI18n()

  if (!breadcrumbs || !Array.isArray(breadcrumbs)) return {
    to: '/',
    label: ts('com.goBack'),
  }

  return {
    to: breadcrumbs?.at(index)?.url ?? '/',
    label: breadcrumbs?.at(index)?.label ? `${ts('com.backTo')} ${breadcrumbs?.at(index)?.label}` : ts('com.goBack'),
  }
}
