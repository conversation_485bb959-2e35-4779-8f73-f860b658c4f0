// TODO: user settings are a modernfold thing
export default defineNuxtPlugin({
  dependsOn: ['auth'],
  async setup() {
    if (useRuntimeConfig().storybookBuild) return

    // Initialize global settings
    const { initialize: initializeUserSettings, selectedDistributor } = useUserSettings()
    await callOnce('initializeUserSettings', initializeUserSettings)

    addRouteMiddleware('multiple-distributors', (to) => {
      const { localePath } = useI18n()
      const multipleDistributorsPath = localePath('/multiple-distributors')

      // handle navigation to multiple-distributors page
      if (to.path === multipleDistributorsPath) {
        // redirect to home if distributor is already selected, otherwise allow navigation
        if (selectedDistributor.value) return navigateTo(localePath('/'))
        // avoid infinite redirects
        return
      }

      // check if there is an already selected distributor
      if (selectedDistributor.value) return

      return navigateTo(multipleDistributorsPath)
    }, { global: true })
  } })
