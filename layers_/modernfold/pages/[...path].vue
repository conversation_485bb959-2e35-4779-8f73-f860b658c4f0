<script setup lang="ts">
import type { MappedData } from '@integration/server/api/[locale]/storyblok/page/[...path].get'
import type { Breadcrumb } from '@design-system/components/Atoms/AtomsBreadcrumbs.props'

const { currentPathWithoutLocale } = useLanguage()
const { $getLocale, localePath } = useI18n()
const requestFetch = useRequestFetch()

const { data, error } = await useAsyncData(
  `storyblok-page-${currentPathWithoutLocale.value}`,
  async () => {
    const [page, breadcrumbs] = await Promise.all([
      requestFetch<MappedData>(`/api/${$getLocale()}/storyblok/page${currentPathWithoutLocale.value}`),
      requestFetch<Breadcrumb[]>(
        `/api/${$getLocale()}/storyblok/breadcrumbs/${currentPathWithoutLocale.value}`,
      )
        .then(res => res ?? [] as Breadcrumb[])
        .catch(() => [] as <PERSON><PERSON><PERSON>rumb[]),
    ])
    return { page, breadcrumbs }
  },
)

if (error.value?.statusCode === 403) {
  await navigateTo(localePath('/'))
}

if (error.value || !data.value) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Page Not Found',
    fatal: true,
  })
}

const { page, breadcrumbs } = data.value

definePageMeta({
  middleware: 'custom-layout',
})
</script>

<template>
  <div class="contents">
    <LazyPagesSampleOrderingForm v-if="page?.component === 'PagesSampleOrderingForm'" v-bind="page" :breadcrumbs />
    <LazyPagesTroubleReports v-else-if="page?.component == 'PagesTroubleReports'" v-bind="page" :breadcrumbs />
    <LazyPagesCustomerSupportTroubleReport v-else-if="page?.component === 'PagesCustomerSupportTroubleReport'" v-bind="page" :breadcrumbs />
    <LazyPagesResourceCategory v-else-if="page?.component === 'PagesResourceCategory'" v-bind="page" :breadcrumbs />
    <LazyPagesCOM v-else-if="page?.component == 'PagesCOM'" v-bind="page" :breadcrumbs />
    <LazyPagesCOMPreapprovedCoveringList v-else-if="page?.component == 'PagesCOMPreapprovedCoveringList'" v-bind="page" :breadcrumbs />
    <LazyPagesCOMRequests v-else-if="page?.component === 'PagesCOMRequests'" v-bind="page" :breadcrumbs />
    <LazyPagesCOMRequest v-else-if="page?.component == 'PagesCOMRequest'" v-bind="page" :breadcrumbs />
    <LazyPagesWarranty v-else-if="page?.component == 'PagesWarranty'" v-bind="page" :breadcrumbs />
    <LazyPagesAccountManagementShipments v-else-if="page?.component == 'PagesAccountManagementShipments'" v-bind="data" :breadcrumbs />
    <LazyPagesCOMAcoustiSealCalculator v-else-if="page?.component === 'PagesCOMAcoustiSealCalculator'" v-bind="page" :breadcrumbs />
    <LazyPagesCOMNewNumber v-else-if="page?.component == 'PagesCOMNewNumber'" v-bind="page" :breadcrumbs />
    <LazyPagesShippingSchedule v-else-if="page?.component == 'PagesShippingSchedule'" v-bind="page" :breadcrumbs />
    <LazyPagesAccountManagementInvoices v-else-if="page?.component == 'PagesAccountManagementInvoices'" v-bind="page" :breadcrumbs />
    <LazyPagesOrdersAcknowledgments v-else-if="page?.component == 'PagesOrdersAcknowledgments'" v-bind="page" :breadcrumbs />
  </div>
</template>
