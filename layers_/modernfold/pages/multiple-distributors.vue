<script setup lang="ts">
definePageMeta({
  layout: false,
})

const { selectedDistributor, distributors } = useUserSettings()
</script>

<template>
  <div class="bg-brand">
    <div class="container  min-h-screen flex flex-col justify-start items-center lg:justify-center pt-10 lg:pt-0">
      <div class="p-padding-md pt-padding-xxl md:px-padding-xl md:pb-padding-mb lg:p-padding-xxl space-y-padding-xxl bg-white w-full">
        <AtomsIcon name="BrandModernFoldColored" class="h-full w-full md:w-1/2 px-padding-md md:px-0" mode="svg" />
        <div>
          <h2 class="headline-1 pb-padding-xxs">
            {{ $ts('modernfold.multipleDistributors.title') }}
          </h2>
          <p class="body-1">
            {{ $ts('modernfold.multipleDistributors.description') }}
          </p>
        </div>
        <div>
          <p class="body-2 font-bold pb-padding-xs">
            {{ $ts('modernfold.multipleDistributors.selectDistributor') }}
          </p>
          <MoleculesSwitchCode
            v-if="distributors?.length && distributors.length > 1"
            id="menu-switch-code"
            v-model="selectedDistributor"
            :options="distributors"
            variant="login"
          />
        </div>
        <AtomsButton
          class="w-full md:w-auto min-w-[184px] float-right"
          as="link"
          :to="$localePath('/')"
          :state="selectedDistributor ? 'default' : 'disabled'"
        >
          {{ $ts('modernfold.multipleDistributors.go') }}
        </AtomsButton>
      </div>
    </div>
  </div>
</template>
