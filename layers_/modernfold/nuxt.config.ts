// https://nuxt.com/docs/api/configuration/nuxt-config
import { fileURLToPath } from 'url'

export default defineNuxtConfig({
  extends: ['../integration'],
  alias: {
    '@modernfold': fileURLToPath(new URL('./', import.meta.url)),
    '@integration': fileURLToPath(
      new URL('../integration', import.meta.url),
    ),
    '@forms': fileURLToPath(
      new URL('../forms', import.meta.url),
    ),
    '@design-system': fileURLToPath(
      new URL('../design-system', import.meta.url),
    ),
  },
  nitro: {
    prerender: {
      ignore: [path => !path.startsWith('/_locales')],
    },
  },
})
