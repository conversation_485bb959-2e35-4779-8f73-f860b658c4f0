<script lang="ts" setup>
import type { TemplatesCOMPageWithBreadcrumbs } from './TemplatesCOMPage.props'

const props = defineProps<TemplatesCOMPageWithBreadcrumbs>()

const { to, label } = useBackTo(props.breadcrumbs)
</script>

<template>
  <div class="my-4 md:my-6 min-h-full flex flex-col">
    <div class="container mb-padding-xl">
      <AtomsLink
        :to="to"
      >
        <AtomsIcon
          name="NavArrowLeft"
        />
        {{ label }}
      </AtomsLink>

      <h1 v-if="title" class="headline-3">
        {{ title }}
      </h1>
    </div>
    <div v-if="$slots.body" class="bg-light w-full">
      <div class="container py-padding-xxl space-y-padding-xxl">
        <slot name="body" />
      </div>
    </div>
    <div v-if="$slots.footer" class="bg-light w-full mt-padding-xl">
      <div class="container py-padding-xxl space-y-padding-xxl">
        <slot name="footer" />
      </div>
    </div>
  </div>
</template>
