<script setup lang="ts">
import type { OrganismsHeaderProps } from '@design-system/components/Organisms/Header/OrganismsHeader.props.ts'

const props = defineProps<OrganismsHeaderProps>()

const { selectedDistributor, distributors } = useUserSettings()
</script>

<template>
  <OrganismsHeader v-bind="props">
    <template v-if="distributors?.length && distributors.length > 1 && !hideDistributorSwitch" #after-logo>
      <MoleculesSwitchCode
        id="header-switch-code"
        v-model="selectedDistributor"
        :class="[{ 'text-invert': props.anatomy === 'transparent' }, 'hidden lg:flex']"
        :options="distributors"
      />
    </template>
    <template #menu-mobile-before-bottom>
      <MoleculesSwitchCode
        v-if="distributors?.length && distributors.length > 1"
        id="menu-switch-code"
        v-model="selectedDistributor"
        :options="distributors"
        class="px-padding-md py-padding-xxl bg-neutral"
      />
    </template>
  </OrganismsHeader>
</template>
