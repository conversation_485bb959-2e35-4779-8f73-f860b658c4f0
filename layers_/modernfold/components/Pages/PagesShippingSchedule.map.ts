import type { MappedContent } from '@integration/server/types/storyblok'
import type { ProjectManagementShippingScheduleStoryblok } from '@integration/storyblok/data/components'
import type { WithRelationships } from '@integration/types/storyblok'
import type { StoryblokRichTextNode } from '@storyblok/richtext'
import type { PagesShippingSchedule } from './PagesShippingSchedule.props'

export const pagesShippingScheduleMap = (content: WithRelationships<ProjectManagementShippingScheduleStoryblok>): MappedContent<'PagesShippingSchedule', PagesShippingSchedule> => {
  return {
    component: 'PagesShippingSchedule',
    title: content.page_fields?.[0]?.title ?? '',
    description: content.page_fields?.[0]?.description as StoryblokRichTextNode,
    subtitle: content.subtitle ?? '',
    note: content.note as StoryblokRichTextNode,
    messages: content.messages?.map(message => ({
      title: message.title ?? '',
      description: message.description,
      status: message.status !== '' ? message.status : undefined,
      size: message.size !== '' ? message.size : undefined,
    })) ?? [],
  }
}
