<script lang="ts" setup>
import type { PagesSampleOrderingWithBreadcrumbs } from './PagesSampleOrdering.props'

const props = defineProps<PagesSampleOrderingWithBreadcrumbs>()

const vendorId = computed(() => {
  if (!props.id) return ''
  return typeof props.id === 'number' ? String(props.id) : props.id
})
</script>

<template>
  <div class="container min-h-screen flex flex-col">
    <AtomsBreadcrumbs v-if="breadcrumbs?.length" :items="breadcrumbs" />
    <h1 class="headline-1 mb-padding-xs">
      {{ name }}
    </h1>
    <div class="flex-1 flex flex-col">
      <FormsSampleOrdering v-if="id" :vendor-id="vendorId" />
    </div>
  </div>
</template>
