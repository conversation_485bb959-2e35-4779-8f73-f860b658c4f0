import type { MappedContent } from '@integration/server/types/storyblok'
import type { ProjectManagementOrdersAcknowledgmentsStoryblok } from '@integration/storyblok/data/components'
import type { WithRelationships } from '@integration/types/storyblok'
import mapStoryblokAssets from '@integration/utils/mapStoryblokAssets'
import type { PagesOrdersAcknowledgmentsProps } from './PagesOrdersAcknowledgments.props'

export const ordersAcknowledgmentsMap = (content: WithRelationships<ProjectManagementOrdersAcknowledgmentsStoryblok>): MappedContent<'PagesOrdersAcknowledgments', PagesOrdersAcknowledgmentsProps> => {
  return {
    component: 'PagesOrdersAcknowledgments',
    title: content.page_fields?.[0]?.title ?? '',
    assets: mapStoryblokAssets(content.page_fields?.[0]?.assets || []),
  }
}
