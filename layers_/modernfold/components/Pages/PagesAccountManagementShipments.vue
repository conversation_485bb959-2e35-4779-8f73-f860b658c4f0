<script setup lang='ts'>
import type { ColumnDef } from '@tanstack/vue-table'
import AtomsLink from '@design-system/components/Atoms/Link/AtomsLink.vue'
import AtomsIcon from '@design-system/components/Atoms/AtomsIcon.vue'
import type { DistributorShipmentsRow } from '@modernfold/server/api/distributors/[id]/shipments.get'
import type { PagesAccountManagementShipmentsWithBreadcrumbs } from './PagesAccountManagementShipments.props'

type DistributorShipmentsData = DistributorShipmentsRow & { id: string }

defineProps<PagesAccountManagementShipmentsWithBreadcrumbs>()

const { ts } = useI18n()
const dayjs = useDayjs()
const formatData = (value: string) => dayjs(value).format('MM/DD/YYYY')

const { selectedDistributor } = useUserSettings()
const { data: tableData } = await useFetch<DistributorShipmentsData[]>(`/api/distributors/${selectedDistributor.value}/shipments`, {
  default: () => [] as DistributorShipmentsData[],
  transform: (rawData: DistributorShipmentsData[]) => {
    if (!Array.isArray(rawData)) {
      return []
    }
    return rawData.map((item: DistributorShipmentsData) => ({
      ...item,
      id: String(item.OrderNumber),
    }))
  },
})

const currentSorting = ref<'asc' | 'desc'>('asc')
const handleSortingClick = () => currentSorting.value = currentSorting.value === 'asc' ? 'desc' : 'asc'
const items = computed(() => tableData.value?.toSorted((a, b) => currentSorting.value === 'asc' ? a.OrderNumber - b.OrderNumber : b.OrderNumber - a.OrderNumber))

const tableColumns: ColumnDef<DistributorShipmentsData>[] = [
  {
    header: ts('accountManager.shipments.tableHeader.shipDate'),
    accessorKey: 'Date',
    cell: cellData => formatData(String(cellData.getValue())),
  },
  {
    header: () => h('div', {
      class: 'flex justify-between',
    }, [
      h('p', {}, ts('accountManager.shipments.tableHeader.orderNumber')),
      h(AtomsLink, {
        onClick: handleSortingClick,
      }, [
        h(AtomsIcon, {
          name: currentSorting.value === 'asc' ? 'NavArrowDown' : 'NavArrowUp',
          class: 'text-invert',
        }),
      ]),
    ]),
    accessorKey: 'OrderNumber',
  },
  {
    header: ts('accountManager.shipments.tableHeader.jobNumber'),
    accessorKey: 'JobNumber',
  },
  {
    header: ts('accountManager.shipments.tableHeader.jobName'),
    accessorKey: 'JobName',
    cell: cellData => h('p', { class: 'whitespace-nowrap' }, (String(cellData.getValue()))),
  },
  {
    header: ts('accountManager.shipments.tableHeader.purchaseNumber'),
    accessorKey: 'PurchaseNumber',
    cell: cellData => h('p', { class: 'whitespace-nowrap' }, (String(cellData.getValue()))),
  },
  {
    header: ts('accountManager.shipments.tableHeader.carrier'),
    accessorKey: 'ShipVia',
    cell: cellData => h('p', { class: 'whitespace-nowrap' }, (String(cellData.getValue()))),
  },
  {
    header: ts('accountManager.shipments.tableHeader.tracking'),
    accessorKey: 'ProNumber',
    cell: cellData => h('p', { class: 'whitespace-nowrap' }, (String(cellData.getValue()))),
  },
  {
    header: ts('accountManager.shipments.tableHeader.weight'),
    accessorKey: 'Weight',
  },
  {
    header: ts('accountManager.shipments.tableHeader.pieces'),
    accessorKey: 'Pieces',
  },
  {
    header: ts('accountManager.shipments.tableHeader.shippingCharges'),
    accessorKey: 'Rate',
  },
]
</script>

<template>
  <div class="space-y-padding-sm py-6">
    <div class="container">
      <MoleculesHeadingPage :breadcrumbs="breadcrumbs" :title="title" :description="description" />
    </div>
    <div class="bg-light">
      <div class="container py-padding-xxl">
        <p class="body-1">
          {{ tableDescription }}
        </p>

        <MoleculesTable :columns="tableColumns" :items="items" paginated-table />
      </div>
    </div>
  </div>
</template>
