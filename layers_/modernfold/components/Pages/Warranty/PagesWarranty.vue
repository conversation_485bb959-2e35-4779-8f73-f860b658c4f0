<script lang="ts" setup>
import type { PagesWarrantyWithBreadcrumbs } from './PagesWarranty.props'

defineProps<PagesWarrantyWithBreadcrumbs>()
</script>

<template>
  <div class="container py-padding-xl">
    <MoleculesHeadingPage :breadcrumbs="breadcrumbs" :title="title" :description="subtitle" />
    <div class="grid grid-cols-2 md:grid-cols-4 gap-padding-md md:gap-padding-xl xl:gap-padding-xxl pt-padding-xl">
      <OrganismsCardCOM
        v-for="card in cards"
        :key="card.label"
        :label="card.label"
        :to="card.to"
        :external="card.external"
      />
    </div>
  </div>
</template>
