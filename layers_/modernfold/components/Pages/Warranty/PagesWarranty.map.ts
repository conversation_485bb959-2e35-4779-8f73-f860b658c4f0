import type { <PERSON><PERSON>toryblok, CustomerSupportWarrantyStoryblok } from '@integration/storyblok/data/components'
import { normalizeLink } from '@integration/server/utils/storyblok/normalizeLink'
import type { MappedContent } from '@integration/server/types/storyblok'
import type { StoryblokRichTextNode } from '@storyblok/richtext'
import type { WithRelationships } from '@integration/types/storyblok'
import type { PagesWarrantyProps } from './PagesWarranty.props'

export const pagesWarrantyMap = (content: WithRelationships<CustomerSupportWarrantyStoryblok>): MappedContent<'PagesWarranty', PagesWarrantyProps> => {
  return {
    component: 'PagesWarranty',
    title: content.page_fields?.[0]?.title,
    subtitle: content.page_fields?.[0]?.description as StoryblokRichTextNode,
    cards: content.links?.map((link: LinkStoryblok) => normalizeLink(link)) || [],
  }
}
