import type { AccountManagementInvoicesStoryblok } from '@integration/storyblok/data/components'
import type { MappedContent } from '@integration/server/types/storyblok'
import type { StoryblokRichTextNode } from '@storyblok/richtext'
import type { WithRelationships } from '@integration/types/storyblok'
import mapStoryblokAssets from '@integration/utils/mapStoryblokAssets'
import type { PagesAccountManagementInvoices } from './PagesAccountManagementInvoices.props'

export const pagesAccountManagementInvoicesMap = (content: WithRelationships<AccountManagementInvoicesStoryblok>): MappedContent<'PagesAccountManagementInvoices', PagesAccountManagementInvoices> => {
  const assets = mapStoryblokAssets(content.page_fields?.[0]?.assets || [])

  return {
    component: 'PagesAccountManagementInvoices',
    title: content.page_fields?.[0]?.title,
    description: content.page_fields?.[0]?.description as StoryblokRichTextNode || undefined,
    assets,
  }
}
