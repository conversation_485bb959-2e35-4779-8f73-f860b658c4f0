<script lang="ts" setup>
import ProductsLeadtimeTable from '@modernfold/components/Organisms/ProductsLeadtimeTable.vue'
import TracksLeadtimeTable from '@modernfold/components/Organisms/TracksLeadtimeTable.vue'
import type { PagesShippingScheduleProps } from './PagesShippingSchedule.props'

defineProps<PagesShippingScheduleProps>()
</script>

<template>
  <div class="container pt-padding-lg pb-padding-xxl">
    <MoleculesHeadingPage
      :breadcrumbs="breadcrumbs"
      :title="title"
      :description="description"
    />
  </div>
  <div class="bg-light">
    <div class="container py-padding-xxl flex flex-col gap-padding-xxl">
      <div>
        <p v-if="subtitle" class="subtitle-3 font-semibold mb-padding-xl">
          {{ subtitle }}
        </p>
        <div v-if="note" class="body-1 prose mb-padding-xl">
          <UtilsRichTextContent :content="note" :link-meta="{ target: '_blank' }" />
        </div>
      </div>
      <ProductsLeadtimeTable />

      <div v-if="messages.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-padding-xl">
        <OrganismsMessage
          v-for="message in messages"
          :key="message.title"
          :title="message.title"
          :description="message.description"
          :status="message.status"
          :size="message.size"
          class="max-w-none"
        />
      </div>
      <TracksLeadtimeTable />
    </div>
  </div>
</template>
