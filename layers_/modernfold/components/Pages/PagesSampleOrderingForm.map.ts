import type { MappedContent } from '@integration/server/types/storyblok'
import type { CustomerSupportSampleOrderingStoryblok } from '@integration/storyblok/data/components'
import type { WithRelationships } from '@integration/types/storyblok'
import type { PagesSampleOrderingProps } from './PagesSampleOrdering.props'

export const sampleOrderingFormMap = (content: WithRelationships<CustomerSupportSampleOrderingStoryblok>): MappedContent<'PagesSampleOrderingForm', PagesSampleOrderingProps> => {
  return {
    component: 'PagesSampleOrderingForm',
    name: content.name,
    id: content.id,
  }
}
