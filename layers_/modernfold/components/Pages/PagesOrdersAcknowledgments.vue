<script setup lang="ts">
import type { OrdersResponse } from '@modernfold/server/api/orders/[distributor_id].get'
import type { OrdersAcknowledgmentRow } from '@modernfold/components/Organisms/OrdersAcknowledgmentsTable.props'
import type { DistributorSummariesResponse } from '@modernfold/server/api/distributors/[id]/summaries.get'
import type { OrdersStatusSummaryRow } from '@modernfold/components/Organisms/OrdersStatusSummaryTable.props'
import type { PagesOrdersAcknowledgmentsWithBreadcrumbs, PagesOrdersAcknowledgmentsFilters } from './PagesOrdersAcknowledgments.props'

defineProps<PagesOrdersAcknowledgmentsWithBreadcrumbs>()

const initialFilters = {
  po: '',
}
const filters = ref<PagesOrdersAcknowledgmentsFilters>(initialFilters)
const appliedFilters = ref<PagesOrdersAcknowledgmentsFilters | null>(null)

function mapOrdersToAcknowledgmentRows(data: OrdersResponse): OrdersAcknowledgmentRow[] {
  return data.orders
    .filter(order => order.Status !== 'Field Backlog')
    .map(order => ({
      id: order['Order Number Text'],
      orderNumber: order['Order Number Text'],
      poNumber: order['Purchase Number'],
      jobName: order['Job Name'],
      distributorAnticipatedShipDate: order['Date_RQST'] ?? '',
      dateOrder: order['Date Ordered'],
      dateSchedule: order['Date Scheduled'] ?? '',
      job: order['Job Number'],
      status: order['Status'],
      gross: order['Gross'],
      net: order['Net'],
      discount: order['DiscountPer'],
      units: order['Units'],
      orderType: order['OrderType'],
      reviseOrderLink: '#',
    }))
}

function mapSummariesToTableRows(data: DistributorSummariesResponse): OrdersStatusSummaryRow[] {
  return data.orderSummaries.map(summary => ({
    id: summary.Status,
    Status: summary.Status,
    Gross: summary['Gross Sales'],
    Net: summary['Net Sales'],
    Discount: summary.DiscountPer,
  }))
}

const { selectedDistributor } = useUserSettings()

// api/v1/tcm/orders/{{distributorid}} => /api/orders/{{distributorid}}
const { data: ordersData, status: ordersDataStatus } = useFetch(`/api/orders/${selectedDistributor.value}`, {
  key: 'ordersData',
  server: false,
  transform: (apiResponse: OrdersResponse): OrdersAcknowledgmentRow[] => mapOrdersToAcknowledgmentRows(apiResponse),
})
// api/v1/tcm/distributor/{{distributorid}}/summaries => /api/distributors/{{distributorid}}/summaries
const { data: summariesData, status: summariesDataStatus } = useFetch(`/api/distributors/${selectedDistributor.value}/summaries`, {
  key: 'summariesData',
  transform: (apiResponse: DistributorSummariesResponse): OrdersStatusSummaryRow[] => mapSummariesToTableRows(apiResponse),
})

const filteredOrders = computed(() => {
  if (!appliedFilters.value || !appliedFilters.value.po) {
    return ordersData.value ?? []
  }
  return ordersData.value?.filter(order => order.poNumber.includes(appliedFilters.value!.po)) ?? []
})

function handleSubmit(data: PagesOrdersAcknowledgmentsFilters) {
  appliedFilters.value = data
}

function handleReset() {
  appliedFilters.value = null
}
</script>

<template>
  <div class="space-y-6 py-6">
    <div class="container">
      <MoleculesHeadingPage :breadcrumbs="breadcrumbs" :title="title" />

      <div v-if="assets && assets.length > 0" class="flex flex-col gap-xxs">
        <AtomsLinkAsset
          v-for="(document, index) in assets"
          :key="index"
          :to="document.to"
          :label="document.label"
        />
      </div>
    </div>
    <div class="bg-light my-padding-xxl">
      <div class="container py-padding-xxl flex flex-col gap-xxl">
        <FormKit
          id="PagesOrdersAcknowledgmentsFilters"
          v-model="filters"
          type="form"
          :actions="false"
          :submit-label="false"
          form-class="flex flex-col gap-xl"
          @submit="handleSubmit"
        >
          <FormKit
            type="text"
            name="po"
            :label="$ts('ordersAcknowledgments.form.purchaseOrderNumber')"
          />
          <div class="flex flex-col md:flex-row gap-4 col-span-1 md:col-span-2">
            <AtomsButton
              type="submit"
              class="md:w-80"
              :state="filters.po ? 'default' : 'disabled'"
            >
              {{ $ts('ordersAcknowledgments.form.search') }}
            </AtomsButton>
            <AtomsButton
              v-if="appliedFilters?.po"
              anatomy="secondary"
              @click.prevent="handleReset"
            >
              {{ $ts('ordersAcknowledgments.form.resetFilters') }}
            </AtomsButton>
          </div>
        </FormKit>
        <OrganismsOrdersAcknowledgmentsTable :data="filteredOrders ?? []" :loading="ordersDataStatus === 'pending'" />
        <h3 class="subtitle-3 font-semibold">
          {{ $ts('ordersAcknowledgments.statusSummary') }}
        </h3>
        <OrganismsOrdersStatusSummaryTable :data="summariesData ?? []" :loading="summariesDataStatus === 'pending'" />
      </div>
    </div>
    <div class="container">
      <p>{{ $ts('ordersAcknowledgments.realTime') }}</p>
    </div>
  </div>
</template>
