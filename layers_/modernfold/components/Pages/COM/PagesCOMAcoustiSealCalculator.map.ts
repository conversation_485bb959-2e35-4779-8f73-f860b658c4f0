import type { MappedContent } from '@integration/server/types/storyblok'
import type { ProjectManagementComAcoustiSealCalculatorStoryblok } from '@integration/storyblok/data/components'
import type { WithRelationships } from '@integration/types/storyblok'
import type { PagesCOMAcoustiSealCalculatorProps } from './PagesCOMAcoustiSealCalculator.props'

export const comAcoustiSealCalculatorMap = (
  content: WithRelationships<ProjectManagementComAcoustiSealCalculatorStoryblok>,
): MappedContent<'PagesCOMAcoustiSealCalculator', PagesCOMAcoustiSealCalculatorProps> => {
  return {
    component: 'PagesCOMAcoustiSealCalculator',
    title: content.page_fields?.[0]?.title ?? '',
  }
}
