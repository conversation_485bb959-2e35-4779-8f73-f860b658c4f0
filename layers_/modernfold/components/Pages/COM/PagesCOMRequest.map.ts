import type { ProjectManagementComRequestStoryblok } from '@integration/storyblok/data/components'
import type { MappedContent } from '@integration/server/types/storyblok'
import type { WithRelationships } from '@integration/types/storyblok'
import type { PagesCOMRequestProps } from './PagesCOMRequest.props'

export const pagesCOMRequestMap = (content: WithRelationships<ProjectManagementComRequestStoryblok>): MappedContent<'PagesCOMRequest', PagesCOMRequestProps> => {
  return {
    component: 'PagesCOMRequest',
    title: content.page_fields?.[0]?.title,
  }
}
