<script lang="ts" setup>
import type { ColumnDef } from '@tanstack/vue-table'
import type { ApiRequestHistory } from '@modernfold/server/api/com/request/history.get'
import type { COMRequest } from '@modernfold/server/api/com/request/detail.get'
import MoleculesTable from '@design-system/components/Molecules/Table/MoleculesTable.vue'
import AtomsIcon from '@design-system/components/Atoms/AtomsIcon.vue'
import type { PagesCOMRequestWithBreadcrumbs } from './PagesCOMRequest.props'

defineProps<PagesCOMRequestWithBreadcrumbs>()

const route = useRoute()
const { ts } = useI18n()

const comid = route.query.comid

if (!comid) {
  // Redirect to COM Requests page if comid is missing
  await navigateTo('/project-management/com/com-requests/')
}

const { data: detail, error: detailError } = await useFetch<COMRequest>(`/api/com/request/detail/?comid=${comid}`, { key: `com-request-${comid}` })

const { data: history, error: historyError } = await useFetch<ApiRequestHistory>(`/api/com/request/history/?comid=${comid}`, { key: `com-request-history-${comid}` })

type HistoryRow = ApiRequestHistory['history'][number] & { id: string | number }

const dayjs = useDayjs()
const formatData = (value: string) => dayjs(value).format('MM/DD/YYYY')

const historyColumns: ColumnDef<HistoryRow>[] = [
  {
    id: 'name',
    accessorKey: 'Name',
    header: ({ column }) => {
      return h('button', {
        class: 'flex items-center space-x-2 font-medium hover:text-opacity-80 transition-colors',
        onClick: () => column.toggleSorting(),
      }, [
        h('span', ts('com.request.history.Name')),
        h(AtomsIcon, {
          name: column.getIsSorted() === 'asc' ? 'NavArrowUp' : 'NavArrowDown',
          size: '16',
        }),
      ])
    },
    enableSorting: true,
    sortingFn: 'alphanumeric',
    sortDescFirst: false, // Start with ascending (A to Z)
  },
  {
    header: ts('com.request.history.Notes'),
    accessorKey: 'Notes',
    cell: cellData => cellData.getValue() ? cellData.getValue() : '-',

  },
  {
    header: ts('com.request.history.UserID'),
    accessorKey: 'UserID',
  },
  {
    header: ts('com.request.history.SystemTs'),
    accessorKey: 'SystemTS',
    cell: cellData => formatData(String(cellData.getValue())),
  },
]

const historyRows = computed<HistoryRow[]>(() =>
  history.value?.history.map(item => ({
    ...item,
    id: item.ComID, // ComID as unique id
  })) ?? [],
)
</script>

<template>
  <TemplatesCOMPage :title="`${title} #${route.query.comid}`" :breadcrumbs>
    <template #body>
      <div v-if="detailError">
        <p> {{ $ts(`com.request.errors.detail`) }}</p>
      </div>
      <template v-else>
        <div v-for="item in detail?.comRequest" :key="item.JobNumber" class="grid md:grid-cols-2 gap-x-padding-xl space-y-padding-xl ">
          <div v-for="(value, key) in item" :key="key" class="flex items-center justify-between gap-2">
            <div class=" flex flex-col gap-1 flex-nowrap">
              <p class="body-2 font-bold text-disabled">
                {{ $ts(`com.request.${key}`) }}
              </p>
              <p class="body-1 text-neutral">
                {{ value ? value : '-' }}
              </p>
            </div>
            <AtomsButton v-if="key === 'QuantityReq'" anatomy="tertiary" @click="console.log('Required Quantity', item.QuantityReq)">
              {{ $ts('com.request.edit') }}
            </AtomsButton>
          </div>
        </div>
      </template>
      <div v-if="historyError">
        <p>{{ $ts(`com.request.errors.history`) }}</p>
      </div>
      <MoleculesTable v-else :columns="historyColumns" :items="historyRows">
        <template #header>
          <h4 class="subtitle-3 font-semibold">
            {{ ts('com.request.history.title') }}
          </h4>
        </template>
      </MoleculesTable>
    </template>
  </TemplatesCOMPage>
</template>
