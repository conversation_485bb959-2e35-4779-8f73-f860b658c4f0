<script lang="ts" setup>
import type { PagesCOMWithBreadcrumbs } from './PagesCOM.props'

defineProps<PagesCOMWithBreadcrumbs>()
</script>

<template>
  <div class="container py-padding-md min-h-screen">
    <MoleculesHeadingPage :breadcrumbs="breadcrumbs" :title="title" :description="subtitle" />
    <div class="flex flex-col gap-padding-md pt-padding-md">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-padding-md md:gap-padding-xl xl:gap-padding-xxl">
        <OrganismsCardCOM
          v-for="card in cards"
          :key="card.label"
          :label="card.label"
          :to="card.to"
          :external="card.external"
        />
      </div>
      <div class="[&>p]:mb-0 [&>p:first-child]:mb-padding-md">
        <UtilsRichTextContent v-if="description" :content="description" />
      </div>
    </div>
  </div>
</template>
