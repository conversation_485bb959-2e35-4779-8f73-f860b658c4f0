<script setup lang='ts'>
import type { AcoustiSealCalculatorResponse } from '@modernfold/server/types/comCalculator'
import type { PagesCOMAcoustiSealCalculatorWithBreadcrumbs } from './PagesCOMAcoustiSealCalculator.props'

defineProps<PagesCOMAcoustiSealCalculatorWithBreadcrumbs>()

const res = ref<AcoustiSealCalculatorResponse>()
</script>

<template>
  <TemplatesCOMPage :title="title" :breadcrumbs="breadcrumbs">
    <template #body>
      <FormsCOMAcoustiSealCalculator @result="res = $event" />
    </template>
    <template #footer>
      <div class="grid grid-cols-2 gap-xl text-right">
        <div class="flex flex-col gap-padding-xxs">
          <p class="body-2 font-bold">
            {{ $ts('com.acoustiSealCalculator.numberOfSides') }}
          </p>
          <p class="headline-4">
            {{ res ? res.numOfSides : '--' }}
          </p>
        </div>
        <div class="flex flex-col gap-padding-xxs">
          <p class="body-2 font-bold">
            {{ $ts('com.acoustiSealCalculator.yardsRequired') }}
          </p>
          <p class="headline-4">
            {{ res ? res.yardsNeeded : '--' }}
          </p>
        </div>
      </div>
    </template>
  </TemplatesCOMPage>
</template>
