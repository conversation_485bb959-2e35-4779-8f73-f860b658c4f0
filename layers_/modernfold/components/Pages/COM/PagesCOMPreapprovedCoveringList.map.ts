import type { WithRelationships } from '@integration/types/storyblok'
import type { MappedContent } from '@integration/server/types/storyblok'
import type { ProjectManagementComPreapprovedStoryblok } from '@integration/storyblok/data/components'
import type { PagesCOMPreapprovedCoveringListProps } from './PagesCOMPreapprovedCoveringList.props'

export const pagesCOMPreapprovedCoveringListMap = (content: WithRelationships<ProjectManagementComPreapprovedStoryblok>): MappedContent<'PagesCOMPreapprovedCoveringList', PagesCOMPreapprovedCoveringListProps> => {
  return {
    component: 'PagesCOMPreapprovedCoveringList',
    title: content.page_fields?.[0]?.title || '',
  }
}
