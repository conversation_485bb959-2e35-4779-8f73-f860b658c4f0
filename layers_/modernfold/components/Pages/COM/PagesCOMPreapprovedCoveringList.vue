<script lang="ts" setup>
import type { ApprovedCovering, ApprovedCoveringRow } from '@modernfold/types/com'
import type { ColumnDef } from '@tanstack/vue-table'
import type { PagesCOMPreapprovedCoveringListWithBreadcrumbs } from './PagesCOMPreapprovedCoveringList.props'

defineProps<PagesCOMPreapprovedCoveringListWithBreadcrumbs>()

const { data } = await useFetch<ApprovedCovering[]>(`/api/com/approvedcoverings`)

const sampleColumns: ColumnDef<ApprovedCoveringRow>[] = [
  {
    id: 'coveringType',
    accessorFn: row => row.CoveringType,
    header: 'Covering Type',
    meta: { width: '160px' },
  },
  {
    id: 'manufacturerName',
    accessorFn: row => row.ManufacturerName,
    header: 'Manufacturer Name',
    meta: { width: '160px' },
  },
  {
    id: 'patternName',
    accessorFn: row => row.PatternName,
    header: 'Pattern Name',
    meta: { width: '160px' },
  },
  {
    id: 'patternNumber',
    accessorFn: row => row.PatternNumber,
    header: 'Pattern Number',
    meta: { width: '160px' },
  },
  {
    id: 'color',
    accessorFn: row => row.Color,
    header: 'Color',
    meta: { width: '160px' },
  },
  {
    id: 'thickness',
    accessorFn: row => row.Thickness,
    header: 'Thickness',
    meta: { width: '160px' },
  },
]

const items = computed<ApprovedCoveringRow[]>(() => {
  if (!data.value) return []

  return data.value.map(item => ({
    id: item.ApprovedID,
    CoveringType: item.CoveringType,
    ManufacturerName: item.ManufacturerName,
    PatternName: item.PatternName,
    PatternNumber: item.PatternNumber,
    Color: item.Color,
    Thickness: item.Thickness,
  }))
})
</script>

<template>
  <TemplatesCOMPage :title="title" :breadcrumbs>
    <template #body>
      <MoleculesTable
        :columns="sampleColumns"
        :items="items"
        paginated-table
        :fixed-first-column="false"
      />
    </template>
  </TemplatesCOMPage>
</template>
