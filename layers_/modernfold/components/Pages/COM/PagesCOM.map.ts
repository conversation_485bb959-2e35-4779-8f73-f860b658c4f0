import type { <PERSON><PERSON>toryblok, ProjectManagementComStoryblok } from '@integration/storyblok/data/components'
import { normalizeLink } from '@integration/server/utils/storyblok/normalizeLink'
import type { MappedContent } from '@integration/server/types/storyblok'
import type { StoryblokRichTextNode } from '@storyblok/richtext'
import type { WithRelationships } from '@integration/types/storyblok'
import type { PagesCOMProps } from './PagesCOM.props'

export const pagesComMap = (content: WithRelationships<ProjectManagementComStoryblok>): MappedContent<'PagesCOM', PagesCOMProps> => {
  return {
    component: 'PagesCOM',
    title: content.page_fields?.[0]?.title,
    subtitle: content.page_fields?.[0]?.description as StoryblokRichTextNode,
    cards: content.links?.map((link: LinkStoryblok) => normalizeLink(link)) || [],
    description: content.description as <PERSON>b<PERSON>kRichTextNode,
  }
}
