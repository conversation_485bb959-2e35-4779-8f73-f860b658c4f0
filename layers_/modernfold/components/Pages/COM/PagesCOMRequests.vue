<script setup lang='ts'>
import type { ColumnDef } from '@tanstack/vue-table'
import AtomsLink from '@design-system/components/Atoms/Link/AtomsLink.vue'
import AtomsIcon from '@design-system/components/Atoms/AtomsIcon.vue'
import type { COMRequestsRow } from '@modernfold/server/api/com/request/index.get'
import type { PagesCOMRequestsWithBreadcrumbs } from './PagesCOMRequests.props'

type COMRequestsData = COMRequestsRow & { id: string }

defineProps<PagesCOMRequestsWithBreadcrumbs>()

const { ts } = useI18n()
const dayjs = useDayjs()
const formatData = (value: string) => dayjs(value).format('MM/DD/YYYY')
const filterStatus = ref('All')

const { selectedDistributor } = useUserSettings()
const { data: tableData } = await useFetch<COMRequestsData[]>('/api/com/request', {
  params: {
    distributorId: selectedDistributor,
    status: filterStatus,
  },
  default: () => [] as COMRequestsData[],
  transform: (rawData: COMRequestsData[]) => {
    if (!Array.isArray(rawData)) {
      return []
    }
    return rawData.map((item: COMRequestsData) => ({
      ...item,
      id: String(item.ComID),
    }))
  },
})

const statusFilterOptions = [
  { label: ts('com.requests.filterOptions.all'), value: 'All' },
  { label: ts('com.requests.filterOptions.approved'), value: 'Approved' },
  { label: ts('com.requests.filterOptions.denied'), value: 'Denied' },
  { label: ts('com.requests.filterOptions.received'), value: 'Received' },
  { label: ts('com.requests.filterOptions.submitted'), value: 'Submitted' },
]

const currentSorting = ref<'asc' | 'desc'>('asc')
const handleSortingClick = () => currentSorting.value = currentSorting.value === 'asc' ? 'desc' : 'asc'
const items = computed(() => tableData.value?.toSorted((a, b) => currentSorting.value === 'asc' ? a.ComID - b.ComID : b.ComID - a.ComID))

const tableColumns: ColumnDef<COMRequestsData>[] = [
  {
    header: () => h('div', {
      class: 'w-24 flex justify-between',
    }, [
      h('p', {}, ts('com.requests.tableHeader.comId')),
      h(AtomsLink, {
        onClick: handleSortingClick,
      }, [
        h(AtomsIcon, {
          name: currentSorting.value === 'asc' ? 'NavArrowDown' : 'NavArrowUp',
          class: 'text-invert',
        }),
      ]),
    ]),
    accessorKey: 'ComID',
    cell: cellData => h(AtomsLink, {
      label: String((cellData.getValue())),
      to: `request?comid=${cellData.getValue()}`,
    }),
  },
  {
    header: ts('com.requests.tableHeader.status'),
    accessorKey: 'StatusDesc',

  },
  {
    header: ts('com.requests.tableHeader.requestDate'),
    accessorKey: 'RequestDate',
    cell: cellData => formatData(String(cellData.getValue())),
  },
  {
    header: ts('com.requests.tableHeader.jobName'),
    accessorKey: 'JobName',
    cell: cellData => h('p', { class: 'whitespace-nowrap' }, (String(cellData.getValue()))),
  },
  {
    header: ts('com.requests.tableHeader.estShipDate'),
    accessorKey: 'EstShipDate',
    cell: cellData => formatData(String(cellData.getValue())),
  },
  {
    header: ts('com.requests.tableHeader.manufacter'),
    accessorKey: 'Manufacturer',
    cell: cellData => h('p', { class: 'whitespace-nowrap' }, (String(cellData.getValue()))),
  },
  {
    header: ts('com.requests.tableHeader.line'),
    accessorKey: 'Line',
    cell: cellData => h('p', { class: 'whitespace-nowrap' }, (String(cellData.getValue()))),
  },
  {
    header: ts('com.requests.tableHeader.pattern'),
    accessorKey: 'Pattern',
    cell: cellData => h('p', { class: 'whitespace-nowrap' }, (String(cellData.getValue()))),
  },
  {
    header: ts('com.requests.tableHeader.color'),
    accessorKey: 'Color',
    cell: cellData => h('p', { class: 'whitespace-nowrap' }, (String(cellData.getValue()))),
  },
]
</script>

<template>
  <div>
    <TemplatesCOMPage :title="title" :breadcrumbs>
      <template #body>
        <div class="flex flex-col gap-padding-md">
          <div>
            <UtilsRichTextContent v-if="description" :content="description" />
          </div>

          <div class="grid-standard-full *:col-span-4">
            <FormKit
              v-model="filterStatus"
              name="statusFilter"
              :label="$ts('com.requests.filterLabel')"
              type="dropdown"
              :options="statusFilterOptions"
              select-icon="NavChevronDown"
              selected-icon="NavCheckIcon"
            />
          </div>

          <MoleculesTable :columns="tableColumns" :items="items" paginated-table />
        </div>
      </template>
    </TemplatesCOMPage>
  </div>
</template>
