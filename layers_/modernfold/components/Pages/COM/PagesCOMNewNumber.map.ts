import type { WithRelationships } from '@integration/types/storyblok'
import type { ProjectManagementComNewStoryblok } from '@integration/storyblok/data/components'
import type { MappedContent } from '@integration/server/types/storyblok'
import type { PagesCOMNewNumberProps } from './PagesCOMNewNumber.props'

export const pagesComNewNumberMap = (content: WithRelationships<ProjectManagementComNewStoryblok>): MappedContent<'PagesCOMNewNumber', PagesCOMNewNumberProps> => {
  return {
    component: 'PagesCOMNewNumber',
    title: content.page_fields?.[0]?.title || '',
  }
}
