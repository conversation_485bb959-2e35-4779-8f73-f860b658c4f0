import type { MappedContent } from '@integration/server/types/storyblok'
import type { ProjectManagementComRequestsStoryblok } from '@integration/storyblok/data/components'
import type { StoryblokRichTextNode } from '@storyblok/richtext'
import type { WithRelationships } from '@integration/types/storyblok'
import type { PagesCOMRequestsProps } from './PagesCOMRequests.props'

export const comRequestsMap = (content: WithRelationships<ProjectManagementComRequestsStoryblok>): MappedContent<'PagesCOMRequests', PagesCOMRequestsProps> => {
  return {
    component: 'PagesCOMRequests',
    title: content.page_fields?.[0]?.title ?? '',
    description: content.page_fields?.[0]?.description as StoryblokRichTextNode,
  }
}
