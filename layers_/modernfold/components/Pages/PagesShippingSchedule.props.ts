import type { StoryblokRichTextNode } from '@storyblok/richtext'
import type { OrganismsMessageProps } from '@design-system/components/Organisms/OrganismsMessage.props'
import type { BreadcrumbsProps } from '@integration/types/breadcrumbs'

export type PagesShippingSchedule = {
  title: string
  description: StoryblokRichTextNode
  subtitle: string
  note: StoryblokRichTextNode
  messages: OrganismsMessageProps[]
}

export type PagesShippingScheduleProps = PagesShippingSchedule & BreadcrumbsProps
