import type { AccountManagementShipmentsStoryblok } from '@integration/storyblok/data/components'
import type { MappedContent } from '@integration/server/types/storyblok'
import type { StoryblokRichTextNode } from '@storyblok/richtext'
import type { WithRelationships } from '@integration/types/storyblok'
import type { PagesAccountManagementShipmentsProps } from './PagesAccountManagementShipments.props'

export const pagesAccountManagementShipmentsMap = (content: WithRelationships<AccountManagementShipmentsStoryblok>): MappedContent<'PagesAccountManagementShipments', PagesAccountManagementShipmentsProps> => {
  return {
    component: 'PagesAccountManagementShipments',
    title: content.page_fields?.[0]?.title,
    description: content.page_fields?.[0]?.description as StoryblokRichTextNode || undefined,
    tableDescription: content.table_description,
  }
}
