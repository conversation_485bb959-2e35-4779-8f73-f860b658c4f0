<script setup lang='ts'>
import type { InvoiceRow } from '@modernfold/types/invoices'
import type { APIInvoiceResponse } from '@modernfold/server/types/invoices'
import type { PagesAccountManagementInvoicesProps } from './PagesAccountManagementInvoices.props'

defineProps<PagesAccountManagementInvoicesProps>()

const { selectedDistributor } = useUserSettings()

const { data: rawTableData } = await useFetch(`/api/distributors/${selectedDistributor.value}/invoices/DIST`, {
  transform: (apiResponse: APIInvoiceResponse): InvoiceRow[] => {
    return apiResponse.invoiceHeader.map(item => ({
      id: removeDecimal(item.ID_INVC),
      invoiceNumber: item.ID_INVC.toString(),
      invoiceDate: item.DATE_INVC,
      poNumber: item.ID_PO_CUST?.toString() || '',
      orderNumber: removeDecimal(item.ID_ORD),
      jobNumber: item.ID_JOB?.toString() || '',
      jobName: item.NAME_ORD_BY || '',
      totalUsd: formatCurrency(item.AMT_INVC_TOTAL || 0, item.Currency || 'USD'),
    }))
  },
  default: (): InvoiceRow[] => [],
})

const {
  formId,
  formData,
  filteredData: tableData,
  hasFormFilters,
  hasAppliedFilters,
  handleSearch,
  resetFilters,
} = useInvoiceFilters(rawTableData)
</script>

<template>
  <div class="flex flex-col gap-sm pt-6 pb-8">
    <div class="container">
      <MoleculesHeadingPage :breadcrumbs="breadcrumbs" :title="title" :description="description" />

      <div v-if="assets && assets.length > 0" class="flex flex-col gap-xxs">
        <AtomsLinkAsset
          v-for="(document, index) in assets"
          :key="index"
          :to="document.to"
          :label="document.label"
        />
      </div>
    </div>

    <div class="bg-light">
      <div class="container py-padding-xxl flex flex-col gap-xxl">
        <FormsInvoiceFilters
          :id="formId"
          v-model:form-data="formData"
          :has-form-filters="hasFormFilters"
          :has-applied-filters="hasAppliedFilters"
          @search="handleSearch"
          @reset="resetFilters"
        />
        <TablesInvoices :items="tableData" />
      </div>
    </div>

    <div>
      <div class="container">
        <p>{{ $ts('accountManager.invoices.realTime') }}</p>
      </div>
    </div>
  </div>
</template>
