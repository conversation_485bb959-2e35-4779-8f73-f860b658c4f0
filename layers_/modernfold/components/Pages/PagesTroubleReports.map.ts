import type { MappedContent } from '@integration/server/types/storyblok'
import type { CustomerSupportTroubleReportsStoryblok } from '@integration/storyblok/data/components'
import type { StoryblokRichTextNode } from '@storyblok/richtext'
import type { WithRelationships } from '@integration/types/storyblok'
import type { PagesTroubleReportsProps } from './PagesTroubleReports.props'

export const troubleReportsMap = (content: WithRelationships<CustomerSupportTroubleReportsStoryblok>): MappedContent<'PagesTroubleReports', PagesTroubleReportsProps> => {
  return {
    component: 'PagesTroubleReports',
    title: content.page_fields?.[0]?.title ?? '',
    description: content.page_fields?.[0]?.description as StoryblokRichTextNode,
  }
}
