import type { MappedContent } from '@integration/server/types/storyblok'
import type { CustomerSupportTroubleReportStoryblok } from '@integration/storyblok/data/components'
import type { StoryblokRichTextNode } from '@storyblok/richtext'
import type { WithRelationships } from '@integration/types/storyblok'
import type { PagesCustomerSupportTroubleReportProps } from './PagesCustomerSupportTroubleReport.props'

export const customerSupportTroubleReportMap = (content: WithRelationships<CustomerSupportTroubleReportStoryblok>): MappedContent<'PagesCustomerSupportTroubleReport', PagesCustomerSupportTroubleReportProps> => {
  return {
    component: 'PagesCustomerSupportTroubleReport',
    title: content.page_fields?.[0]?.title ?? '',
    description: content.page_fields?.[0]?.description as StoryblokRichTextNode,
  }
}
