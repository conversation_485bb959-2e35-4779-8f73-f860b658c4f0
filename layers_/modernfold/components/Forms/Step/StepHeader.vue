<script setup lang="ts">
defineProps<{
  title: string
}>()

defineEmits<{
  (e: 'click'): void
}>()
</script>

<template>
  <div class="md:col-span-2 flex items-center justify-between">
    <h3 class="subtitle-3 font-semibold">
      {{ title }}
    </h3>
    <AtomsButton
      anatomy="tertiary"
      size="m"
      as="button"
      @click.prevent="$emit('click')"
    >
      {{ $ts('forms.shared.actions.edit') }}
    </AtomsButton>
  </div>
</template>
