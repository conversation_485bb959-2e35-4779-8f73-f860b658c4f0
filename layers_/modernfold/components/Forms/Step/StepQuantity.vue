<script lang="ts" setup>
import type { ColumnDef } from '@tanstack/vue-table'
import MoleculesTable from '@design-system/components/Molecules/Table/MoleculesTable.vue'
import type { SampleProductRow, SampleCartItem } from '@modernfold/types/sampleOrdering'
import { AtomsIcon, AtomsLink, AtomsCounter } from '#components'

const { $ts } = useI18n()

const props = defineProps<{
  label: string
  vendor: string
}>()

const data = defineModel<{ Items?: SampleCartItem[] }>({
  required: true,
  default: {
    Items: [],
  },
})

const emit = defineEmits<{
  (e: 'update:quantity', item: Omit<SampleCartItem, 'Quantity'>, quantity: number): void
}>()

const updateQuantity = (item: Omit<SampleCartItem, 'Quantity'>, quantity: number) => {
  emit('update:quantity', item, quantity)
}

const { data: sampleProducts, status, error } = await useFetch<SampleProductRow[]>(
  `/api/samples/items/${props.vendor}`,
  {
    key: `sample-items-${props.vendor}`,
    default: () => [] as SampleProductRow[],
    transform: (rawData: SampleProductRow[]) => {
      if (!Array.isArray(rawData)) {
        return []
      }
      return rawData.map((item: SampleProductRow) => ({
        ...item,
        id: String(item.EMSPart),
      }))
    },
  },
)

if (status.value === 'error' && error.value) {
  throw createError({
    statusCode: error.value.statusCode || 500,
    statusMessage: error.value.message || 'Failed to fetch sample items',
  })
}

const expandedRow = ref<string | number | null>(null)

const toggleDescription = (rowId: string) => {
  expandedRow.value = expandedRow.value === rowId ? null : rowId
}

const columns: ColumnDef<SampleProductRow>[] = [
  {
    id: 'product',
    header: props.label,
    meta: { width: '50%' },
    cell: ({ row }) => {
      const isExpanded = expandedRow.value === row.original.EMSPart

      const isChargedToUser = row.original.Desc1.includes('(*)') && props.vendor === '6'

      return h('div', { class: 'md:w-[200px]' }, [
        h('h4', { class: 'body-2 font-semibold' }, row.original.Desc1),
        isChargedToUser
          ? h('p', { class: 'body-2 font-semibold text-alert' }, $ts('forms.sampleOrder.orderQuantity.chargedToYou'))
          : null,
        h('p', { class: 'body-2' }, [
          h('span', { class: '' }, `${$ts('forms.sampleOrder.orderQuantity.part')}: `),
          h('span', { class: '' }, row.original.EMSPart),
        ]),
        h('p', { class: 'body-2' }, [
          h('span', { class: '' }, `${$ts('forms.sampleOrder.orderQuantity.maxQuantity')}: `),
          h('span', { class: '' }, row.original.MaxQuan),
        ]),
        h('div', { class: 'mt-2 max-w-90' }, [
          h(AtomsLink, {
            hierarchy: 'secondary',
            onClick: () => toggleDescription(row.original.EMSPart),
          },
          {
            default: () => [
              isExpanded
                ? h(AtomsIcon, {
                    name: 'NavMinusIcon',
                  })
                : h(AtomsIcon, {
                    name: 'NavPlusIcon',
                  }),
              ` ${$ts('forms.sampleOrder.orderQuantity.description')}`,
            ] }),
          isExpanded
            ? h('div', {
                class: 'mt-1 text-sm',
              }, [
                h('p', { innerHTML: row.original.Desc2, class: 'body-2 ' }),
                h('p', { class: 'body-2 mt-1' }, [
                  h('span', { class: '' }, `${$ts('forms.sampleOrder.orderQuantity.maxQuantity')}: `),
                  h('span', { class: '' }, row.original.MaxQuan),
                ]),
              ])
            : null,
        ]),
      ])
    },
  },
  {
    id: 'quantity',
    header: $ts('forms.sampleOrder.orderQuantity.quantity'),
    meta: { width: '50%' },
    cell: ({ row }) => h('div', { style: 'width: 100%' }, [
      h(AtomsCounter, {
        'name': `${row.original.EMSPart}`,
        'value': data.value.Items?.find(item => item.EMSPart === row.original.EMSPart)?.Quantity ?? 0,
        'onUpdate:value': (value) => {
          updateQuantity({ EMSPart: row.original.EMSPart, Item: row.original.Desc1 }, value)
        },
        'minValue': 0,
        'maxValue': row.original.MaxQuan,
      }),
    ]),
  },
]
</script>

<template>
  <MoleculesTable
    :columns="columns"
    :items="sampleProducts"
    class="overflow-auto"
    paginated-table
  />
</template>
