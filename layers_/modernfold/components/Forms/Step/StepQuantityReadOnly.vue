<script setup lang="ts">
import type { SampleCartItem } from '@modernfold/types/sampleOrdering'

defineProps<{
  items?: SampleCartItem[]
  vendor: string
}>()

defineEmits<{
  (e: 'click:edit'): void
}>()
</script>

<template>
  <div v-if="items" class="rounded-extra-small overflow-hidden bg-neutral p-padding-xxl flex flex-col gap-xs">
    <FormsStepHeader
      title="Carpet"
      @click="$emit('click:edit')"
    />
    <div class="flex flex-col rounded-extra-small border border-neutral overflow-hidden">
      <div v-for="(item, index) in items" :key="index" class="flex px-padding-md py-padding-xs items-center gap-2 bg-neutral odd:bg-light justify-between">
        <div class="flex-1 *:body-2 *:text-neutral ">
          <p class="font-semibold">
            {{ item.Item }}
          </p>
          <p v-if="item.Item.includes('(*)') && vendor === '6'" class="font-semibold">
            <span class="text-alert">
              {{ $ts('forms.sampleOrder.orderQuantity.chargedToYou') }}
            </span>
          </p>
          <p v-if="item.EMSPart">
            <span>
              {{ $ts('forms.sampleOrder.orderQuantity.part') }}
            </span>
            {{ item.EMSPart }}
          </p>
        </div>
        <div>
          <div class="border border-neutral rounded-full w-6 h-6 relative *:absolute *:top-0 *:right-0 *:bottom-0 *:left-0 *:flex *:items-center *:justify-center">
            <p class="caption-1 text-neutral">
              {{ item.Quantity }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
