<script setup lang="ts">
import * as v from 'valibot'
import type { AcoustiSealCalculatorForm, AcoustiSealCalculatorResponse } from '@modernfold/server/types/comCalculator'
import { AcoustiSealCalculatorBodySchema } from '@modernfold/server/types/comCalculator'

const formId = 'acousti-seal-calculator-form'
const result = ref<AcoustiSealCalculatorResponse>()
const { ts } = useI18n()
const emit = defineEmits(['result'])

const handleSubmit = handleFormkitSubmit(
  data => v.parse(AcoustiSealCalculatorBodySchema, (data as AcoustiSealCalculatorForm)),
  async (data) => {
    result.value = await $fetch<AcoustiSealCalculatorResponse>('/api/com/calculator/acousti', {
      method: 'POST',
      body: data,
    })
  },
  _err => [[ts('com.acoustiSealCalculator.error')]],
)

watch(result, (newValue) => {
  if (newValue) {
    emit('result', newValue)
  }
})
</script>

<template>
  <div>
    <FormKit
      :id="formId"
      v-slot="{ state }"
      :config="{ validationVisibility: 'submit' }"
      type="form"
      :name="formId"
      :actions="false"
      form-class="grid gap-xl grid-cols-1 md:grid-cols-2 items-center"
      @submit="handleSubmit"
    >
      <FormKit
        name="FabricationHeight"
        type="number"
        number="integer"
        :label="$ts('com.acoustiSealCalculator.fields.height')"
        :placeholder="$ts('com.acoustiSealCalculator.fields.heightPlaceholder')"
        validation="required|min:1"
        min="1"
        required
      />
      <FormKit
        name="NumPanels"
        type="number"
        number="integer"
        :label="$ts('com.acoustiSealCalculator.fields.numPanels')"
        :placeholder="$ts('com.acoustiSealCalculator.fields.numPanelsPlaceholder')"
        validation="required|min:1"
        min="1"
        required
      />
      <FormKit
        name="CoveringSides"
        type="dropdown"
        select-icon="NavChevronDown"
        selected-icon="NavCheckIcon"
        :label="$ts('com.acoustiSealCalculator.fields.coveringSides')"
        :placeholder="$ts('com.acoustiSealCalculator.fields.coveringSidesPlaceholder')"
        :options="[{ label: '1', value: 1 }, { label: '2', value: 2 }]"
        validation="required|min:1"
        min="1"
        required
      />
      <FormKit
        name="NumExpClosures"
        type="number"
        number="integer"
        :label="$ts('com.acoustiSealCalculator.fields.numExpClosures')"
        :placeholder="$ts('com.acoustiSealCalculator.fields.numExpClosuresPlaceholder')"
        min="0"
      />
      <FormKit
        name="NumMatchingPassDoors"
        type="number"
        number="integer"
        :label="$ts('com.acoustiSealCalculator.fields.numMatchingPassDoors')"
        :placeholder="$ts('com.acoustiSealCalculator.fields.numMatchingPassDoorsPlaceholder')"
        min="0"
      />
      <FormKit
        name="NumTypeIPocketDoors"
        type="number"
        number="integer"
        :label="$ts('com.acoustiSealCalculator.fields.numTypeIPocketDoors')"
        :placeholder="$ts('com.acoustiSealCalculator.fields.numTypeIPocketDoorsPlaceholder')"
        min="0"
      />
      <FormKit
        name="NumTypeIVPocketDoors"
        type="number"
        number="integer"
        :label="$ts('com.acoustiSealCalculator.fields.numTypeIVPocketDoors')"
        :placeholder="$ts('com.acoustiSealCalculator.fields.numTypeIVPocketDoorsPlaceholder')"
        min="0"
      />

      <div class="col-span-1 col-start-1">
        <AtomsButton
          anatomy="primary"
          size="l"
          :state="state.loading ? 'loading' : state.valid ? 'default' : 'disabled'"
          class="w-full md:max-w-85"
        >
          {{ $ts('com.acoustiSealCalculator.cta') }}
        </AtomsButton>
      </div>
    </FormKit>
  </div>
</template>
