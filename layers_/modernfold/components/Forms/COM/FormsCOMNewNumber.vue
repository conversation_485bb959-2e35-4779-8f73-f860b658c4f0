<script lang="ts" setup>
const {
  formId: formName,
  formState,
  form,
  panelSkins,
  coverings,
  manufacturers,
  patterns,
  submitHandler,
  resetData,
  resetFormState,
} = await useComNewNumber()
</script>

<template>
  <FormKit
    :id="formName"
    v-slot="{ state: { valid } }"
    v-model="form.data"
    :name="formName"
    type="form"
    :actions="false"
    form-class="grid md:grid-cols-2 gap-xl"
    @submit="submitHandler"
  >
    <FormKit
      :id="`${formName}-date`"
      name="RequestDate"
      type="datepicker"
      :label="$ts('forms.comNewNumber.date.label')"
      format="YYYY/MM/DD"
      picker-only
      disabled
      outer-class="underline-appearance"
    />

    <FormKit
      :id="`${formName}-distributor`"
      name="DistributorId"
      type="text"
      :label="$ts('forms.comNewNumber.distributor.label')"
      required
      disabled
      validation="required"
      label-class="no-asterisk"
      :errors="form.data.DistributorId ? [] : [$ts('forms.comNewNumber.distributor.error')]"
    />

    <FormKit
      :id="`${formName}-submittedBy`"
      name="SubmittedBy"
      type="text"
      :label="$ts('forms.comNewNumber.submittedBy.label')"
      required
      validation="required"
      disabled
      label-class="no-asterisk"
      :errors="form.data.SubmittedBy ? [] : [$ts('forms.comNewNumber.submittedBy.error')]"
    />

    <FormKit
      :id="`${formName}-phone`"
      name="Phone"
      type="text"
      :label="$ts('forms.comNewNumber.phone.label')"
      required
      validation="required"
      disabled
      label-class="no-asterisk"
      :errors="form.data.Phone ? [] : [$ts('forms.comNewNumber.phone.error')]"
    />

    <FormKit
      :id="`${formName}-jobNumber`"
      name="JobNumber"
      type="text"
      :label="$ts('forms.comNewNumber.jobNumber.label')"
    />

    <FormKit
      :id="`${formName}-orderNumber`"
      name="OrderNumber"
      type="text"
      :label="$ts('forms.comNewNumber.orderNumber.label')"
    />

    <FormKit
      :id="`${formName}-distributorPo`"
      name="DistributorPO"
      type="text"
      :label="$ts('forms.comNewNumber.po.label')"
    />

    <FormKit
      :id="`${formName}-jobName`"
      name="JobName"
      type="text"
      :label="$ts('forms.comNewNumber.jobName.label')"
      validation="required"
      required
    />

    <FormKit
      :id="`${formName}-panelSkin`"
      name="PanelSkin"
      type="dropdown"
      :label="$ts('forms.comNewNumber.panelSkin.label')"
      :options="panelSkins ?? []"
      select-icon="NavChevronDown"
      selected-icon="NavCheckIcon"
      validation="required"
      required
    />

    <FormKit
      :id="`${formName}-ship-date`"
      name="ShipDate"
      type="datepicker"
      :label="$ts('forms.comNewNumber.estimatedShipDate.label')"
      format="YYYY/MM/DD"
      validation="required"
      required
    />

    <FormKit
      :id="`${formName}-covering`"
      name="Covering"
      type="dropdown"
      :label="$ts('forms.comNewNumber.covering.label')"
      :options="coverings ?? []"
      select-icon="NavChevronDown"
      selected-icon="NavCheckIcon"
      validation="required"
      required
    />

    <FormKit
      :id="`${formName}-manufacturer`"
      name="Manufacturer"
      type="dropdown"
      :label="$ts('forms.comNewNumber.manufacturer.label')"
      :options="manufacturers ?? []"
      select-icon="NavChevronDown"
      selected-icon="NavCheckIcon"
      validation="required"
      required
    />

    <FormKit
      :id="`${formName}-pattern`"
      name="Pattern"
      type="dropdown"
      :label="$ts('forms.comNewNumber.pattern.label')"
      :options="patterns ?? []"
      select-icon="NavChevronDown"
      selected-icon="NavCheckIcon"
      validation="required"
      required
    />

    <FormKit
      :id="`${formName}-color`"
      name="Color"
      type="text"
      :label="$ts('forms.comNewNumber.color.label')"
      :disabled="!form.data.Pattern"
    />

    <FormKit
      :id="`${formName}-line`"
      name="Line"
      type="text"
      :label="$ts('forms.comNewNumber.line.label')"
      validation="required"
      required
    />

    <FormKit
      :id="`${formName}-quantity`"
      name="Quantity"
      type="text"
      validation="number"
      validation-visibility="live"
      :label="$ts('forms.comNewNumber.quantity.label')"
    />

    <FormKit
      :id="`${formName}-part-number`"
      name="PartNumber"
      type="text"
      :label="$ts('forms.comNewNumber.partNumber.label')"
    />

    <FormKit
      :id="`${formName}-notes`"
      :placeholder="$ts('forms.comNewNumber.notes.placeholder')"
      name="Notes"
      type="textarea"
      :label="$ts('forms.comNewNumber.notes.label')"
      outer-class="min-h-50"
    />

    <div class="col-span-1 col-start-1">
      <AtomsButton
        anatomy="primary"
        size="l"
        :state="formState.status === 'submitting' ? 'loading' : (valid ? 'default' : 'disabled')"
        class="w-full md:max-w-85"
      >
        {{ $ts('forms.shared.cta.submit') }}
      </AtomsButton>
    </div>
  </FormKit>

  <FormsConfirmationDialog
    v-if="formState.status !== 'idle'"
    v-model="formState.showResultModal"
    :title="formState.status === 'success' ? $ts(`forms.confirm.success.title`) : $ts(`forms.confirm.warning.title`)"
    :message="formState.message"
    :status="formState.status === 'success' ? 'success' : 'warning'"
  >
    <template v-if="formState.status === 'error'" #actions>
      <AtomsButton
        anatomy="secondary"
        size="m"
        @click="resetData"
      >
        {{ $ts('forms.confirm.actions.cancel') }}
      </AtomsButton>
      <AtomsButton
        anatomy="primary"
        size="m"
        @click="resetFormState"
      >
        {{ $ts('forms.confirm.actions.retry') }}
      </AtomsButton>
    </template>
  </FormsConfirmationDialog>
</template>
