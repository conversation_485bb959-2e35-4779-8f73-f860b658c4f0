<script setup lang='ts'>
const { selectedDistributor } = useUserSettings()
const formValue = ref()

const { data: formOptions } = await useAsyncData(`trouble-report-options-${selectedDistributor.value}`, async () => {
  const ordersPromise = $fetch(`/api/warranty/orders/${selectedDistributor.value}`)
    .catch((e) => {
      console.error(e)
      return []
    })
  const namesPromise = $fetch(`/api/warranty/names/${selectedDistributor.value}`)
    .catch((e) => {
      console.error(e)
      return []
    })
  const numbersPromise = $fetch(`/api/warranty/numbers/${selectedDistributor.value}`)
    .catch((e) => {
      console.error(e)
      return []
    })

  const data = await Promise.all([ordersPromise, namesPromise, numbersPromise])

  return {
    orders: data?.[0]?.map(item => item?.Info) ?? [],
    names: data?.[1]?.map(item => item?.Info) ?? [],
    numbers: data?.[2]?.map(item => item.Info) ?? [],
  }
})

const isFormValid = computed(() => formValue.value && (formValue.value.selectOrder || formValue.value.selectNames || formValue.value.selectNumbers))

const currentSelectWithValue = computed(() => {
  if (formValue.value?.selectOrder) return 'selectOrder'
  else if (formValue.value?.selectNames) return 'selectNames'
  else if (formValue.value?.selectNumbers) return 'selectNumbers'
  return undefined
})

const handleSubmit = () => {
  // TODO: handle form submit
  console.log('Submit handler')
}
</script>

<template>
  <FormKit
    v-model="formValue"
    :config="{ validationVisibility: 'submit' }"
    type="form"
    :actions="false"
    form-class="py-padding-xxl grid gap-x-(--grid-system-gutter-x) gap-y-xl grid-cols-1 md:grid-cols-2"
    @submit="handleSubmit"
  >
    <FormKit
      name="selectOrder"
      type="dropdown"
      :label="$ts('forms.customerSupportTroubleReport.orders.label')"
      :placeholder="$ts('forms.customerSupportTroubleReport.orders.placeholder')"
      :options="[{ label: '', value: undefined }, ...formOptions?.orders]"
      listitem-class="!min-h-8"
      :disabled="currentSelectWithValue && currentSelectWithValue !== 'selectOrder'"
      selection-removable
      validation="require_one:selectNames,selectNumbers"
      close-icon="NavCloseIcon"
      select-icon="NavChevronDown"
      selected-icon="NavCheckIcon"
    />
    <FormKit
      name="selectNames"
      type="dropdown"
      :label="$ts('forms.customerSupportTroubleReport.names.label')"
      :placeholder="$ts('forms.customerSupportTroubleReport.names.placeholder')"
      :options="[{ label: '', value: undefined }, ...formOptions?.names]"
      listitem-class="!min-h-8"
      :disabled="currentSelectWithValue && currentSelectWithValue !== 'selectNames'"
      selection-removable
      validation="require_one:selectOrder,selectNumbers"
      close-icon="NavCloseIcon"
      select-icon="NavChevronDown"
      selected-icon="NavCheckIcon"
    />
    <FormKit
      name="selectNumbers"
      type="dropdown"
      :label="$ts('forms.customerSupportTroubleReport.numbers.label')"
      :placeholder="$ts('forms.customerSupportTroubleReport.numbers.placeholder')"
      :options="[{ label: '', value: undefined }, ...formOptions?.numbers]"
      listitem-class="!min-h-8"
      :disabled="currentSelectWithValue && currentSelectWithValue !== 'selectNumbers'"
      selection-removable
      validation="require_one:selectOrder,selectNames"
      close-icon="NavCloseIcon"
      select-icon="NavChevronDown"
      selected-icon="NavCheckIcon"
    />

    <AtomsButton
      class="col-span-full w-full md:w-72"
      anatomy="primary"
      size="l"
      :state="isFormValid ? 'default' : 'disabled'"
    >
      {{ $t('forms.customerSupportTroubleReport.cta.label') }}
    </AtomsButton>
  </FormKit>
</template>
