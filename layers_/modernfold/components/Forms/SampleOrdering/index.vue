<script setup lang="ts">
import type { FormItem } from '@forms/types/types'
import type { SampleOrderingStepId, SampleOrderingFormData } from '@modernfold/types/sampleOrdering'
import {
  FormsSampleOrderingShippingInformation,
  FormsSampleOrderingShippingMethod,
  FormsSampleOrderingQuantity,
} from '#components'

const overviewComponents: Partial<Record<SampleOrderingStepId, unknown>> = {
  'shipping-information': resolveComponent('FormsSampleOrderingShippingInformation'),
  'shipping-method': resolveComponent('FormsSampleOrderingShippingMethod'),
  'quantity': resolveComponent('FormsSampleOrderingQuantity'),
}

const props = defineProps<{
  vendorId: string
}>()

const { state, orderSteps, updateStepValidity, submitStep, resetData, resetConfirmStep } = await useSampleOrdering(props.vendorId)

const steps: Ref<Array<FormItem<SampleOrderingStepId, SampleOrderingFormData>>> = computed(() => Object.values(state))

const getSubmitButtonState = (activeStep: SampleOrderingStepId) => {
  if (activeStep === 'confirm') {
    if (state.confirm.data.sending && !state.confirm.isCompleted) return 'loading'
    else return 'default'
  }

  return state[activeStep]?.isValid ? 'default' : 'disabled'
}

const scrollToTop = () => {
  if (window)
    window.scrollTo({ top: 0, behavior: 'smooth' })
}
</script>

<template>
  <OrganismsStepper :steps="steps" animate-on-mobile @on-step-change="scrollToTop">
    <template
      #shipping-information="{ stepName, goToNextStep }"
    >
      <FormsValidationWrapper
        :form-id="`${vendorId}-${stepName}`"
        @validity-change="updateStepValidity(stepName, $event)"
      >
        <FormsSampleOrderingShippingInformation
          :id="`${vendorId}-${stepName}`"
          v-model="state[stepName].data"
          :name="stepName"
          @submit="goToNextStep"
        />
      </FormsValidationWrapper>
    </template>
    <template #shipping-method="{ stepName, goToNextStep }">
      <FormsValidationWrapper
        :form-id="`${vendorId}-${stepName}`"
        @validity-change="updateStepValidity(stepName, $event)"
      >
        <FormsSampleOrderingShippingMethod
          :id="`${vendorId}-${stepName}`"
          v-model="state[stepName].data"
          :name="stepName"
          :vendor="vendorId"
          @submit="goToNextStep"
        />
      </FormsValidationWrapper>
    </template>
    <template #quantity="{ stepName, goToNextStep }">
      <FormsValidationWrapper
        :form-id="`${vendorId}-${stepName}`"
        @validity-change="updateStepValidity(stepName, $event)"
      >
        <FormsSampleOrderingQuantity
          :id="`${vendorId}-${stepName}`"
          v-model="state[stepName].data"
          :name="stepName"
          table-label="Carpet"
          :vendor="vendorId"
          @submit="goToNextStep"
        />
      </FormsValidationWrapper>
    </template>
    <template #confirm="{ goToStepByName }">
      <div class="flex flex-col gap-xxs">
        <template
          v-for="step in orderSteps"
          :key="`${step.id}-review`"
        >
          <component
            :is="overviewComponents[step.id]"
            :id="`${vendorId}-${step.id}-review`"
            :name="`${step.id}-review`"
            :model-value="state[step.id].data"
            read-only
            :vendor="vendorId"
            @edit="goToStepByName(step.id)"
          />
        </template>
      </div>
    </template>
    <template #actions="{ activeStep }">
      <div class="contents md:flex md:justify-between">
        <p
          v-if="activeStep.id === 'shipping-information' || activeStep.id === 'shipping-method'"
          class="caption-1 text-neutral my-4 shrink-0 md:mb-0"
        >
          {{ $ts('forms.infoRequired') }}
        </p>
        <div
          class="sticky mt-auto md:mt-4 max-md:bottom-0 container -mx-(--grid-system-spacing-x) w-[calc(100%+var(--grid-system-spacing-x)*2)] max-md:py-padding-md bg-neutral md:static md:flex md:justify-end md:mx-0 md:px-0 md:pb-0 md:bg-transparent
            md:w-full
            "
        >
          <AtomsButton
            type="submit"
            anatomy="primary"
            size="l"
            :state="getSubmitButtonState(activeStep.id ?? '')"
            class="max-md:w-full md:min-w-85"
            @click="submitStep(activeStep.id)"
          >
            {{ activeStep.id === 'confirm' ? $ts('forms.steps.confirm') : $ts('forms.steps.next') }}
          </AtomsButton>
        </div>
      </div>
    </template>
  </OrganismsStepper>
  <FormsConfirmationDialog
    v-if="state.confirm"
    v-model="state.confirm.isCompleted"
    :title="state.confirm.data.status === 'success' ? $ts(`forms.confirm.success.title`) : $ts(`forms.confirm.warning.title`)"
    :message="state.confirm.data.message?.toString() ?? ''"
    :status="state.confirm.data.status === 'success' ? 'success' : 'warning'"
  >
    <template #actions>
      <template v-if="state.confirm.data.status === 'success'">
        <AtomsButton
          anatomy="secondary"
          size="m"
          @click="resetData"
        >
          {{ $ts('forms.confirm.actions.new') }}
        </AtomsButton>
        <AtomsButton
          anatomy="primary"
          size="m"
          as="link"
          to="/"
        >
          {{ $ts('forms.confirm.actions.back') }}
        </AtomsButton>
      </template>
      <template v-else>
        <AtomsButton
          anatomy="secondary"
          size="m"
          @click="resetData"
        >
          {{ $ts('forms.confirm.actions.cancel') }}
        </AtomsButton>
        <AtomsButton
          anatomy="primary"
          size="m"
          @click="resetConfirmStep"
        >
          {{ $ts('forms.confirm.actions.retry') }}
        </AtomsButton>
      </template>
    </template>
  </FormsConfirmationDialog>
</template>
