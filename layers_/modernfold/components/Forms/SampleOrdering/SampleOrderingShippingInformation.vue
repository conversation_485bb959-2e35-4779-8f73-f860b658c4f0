<script lang="ts" setup>
import countries from '@forms/data/countries.json'

defineProps<{
  id: string
  name: string
  readOnly?: boolean
}>()

defineEmits<{
  (e: 'submit' | 'edit'): void
}>()

const data = defineModel<Record<string, unknown>>({ required: true, default: { PhoneNumber: { prefix: '', number: '' } } })

const countriesOptions = computed(() => {
  return countries.map(country => ({
    label: country.name,
    value: country.name,
  }))
})
</script>

<template>
  <FormKit
    :id="id"
    v-model="data"
    :name="name"
    type="form"
    :actions="false"
    form-class="grid md:grid-cols-2 gap-xl bg-neutral p-padding-md md:p-padding-xxl rounded-sm"
    :disabled="readOnly"
    @submit="$emit('submit')"
  >
    <FormsStepHeader v-if="readOnly" :title="$ts('forms.sampleOrder.shippingInformation.title')" @click="$emit('edit')" />
    <FormKit
      id="name"
      type="text"
      name="Name"
      :label="$ts('forms.sampleOrder.shippingInformation.fullname.label')"
      :placeholder="$ts('forms.sampleOrder.shippingInformation.fullname.placeholder')"
      validation="required"
    />
    <FormKit
      id="company"
      type="text"
      name="CompanyName"
      :label="$ts('forms.sampleOrder.shippingInformation.company.label')"
      :placeholder="$ts('forms.sampleOrder.shippingInformation.company.placeholder')"
      validation="required"
    />
    <FormKit
      id="address"
      type="text"
      name="Address"
      :label="$ts('forms.sampleOrder.shippingInformation.address.label')"
      :placeholder="$ts('forms.sampleOrder.shippingInformation.address.placeholder')"
      validation="required"
    />
    <FormKit
      id="city"
      type="text"
      name="City"
      :label="$ts('forms.sampleOrder.shippingInformation.city.label')"
      :placeholder="$ts('forms.sampleOrder.shippingInformation.city.placeholder')"
      validation="required"
    />
    <FormKit
      id="state"
      type="text"
      name="State"
      :label="$ts('forms.sampleOrder.shippingInformation.state.label')"
      :placeholder="$ts('forms.sampleOrder.shippingInformation.state.placeholder')"
      validation="required"
    />
    <FormKit
      id="postal"
      type="text"
      name="Zip"
      :label="$ts('forms.sampleOrder.shippingInformation.postal.label')"
      :placeholder="$ts('forms.sampleOrder.shippingInformation.postal.placeholder')"
      validation="required"
    />
    <FormKit
      id="country"
      type="dropdown"
      name="Country"
      validation="required"
      :options="countriesOptions"
      :label="$ts('forms.sampleOrder.shippingInformation.country.label') + '*'"
      :placeholder="$ts('forms.sampleOrder.shippingInformation.country.placeholder')"
      :validation-messages="{
        required: $ts('forms.sampleOrder.shippingInformation.country.errors.required'),
      }"
    />
    <MoleculesPhoneNumber
      id="phoneNumber"
      name="__phoneNumber"
      :disabled="readOnly"
      :prefix="{
        label: $ts('forms.sampleOrder.shippingInformation.prefix.label'),
        placeholder: $ts('forms.sampleOrder.shippingInformation.prefix.placeholder'),
      }"
      :number="{
        label: $ts('forms.sampleOrder.shippingInformation.number.label'),
        placeholder: $ts('forms.sampleOrder.shippingInformation.number.placeholder'),
      }"
      validation="required"
      :show-validation-icon="!readOnly"
    />
    <FormKit
      id="email"
      type="text"
      name="Email"
      :label="$ts('forms.sampleOrder.shippingInformation.email.label')"
      :placeholder="$ts('forms.sampleOrder.shippingInformation.email.placeholder')"
      validation="required|email"
    />
    <FormKit
      v-if="!readOnly || data.poNumber"
      id="poNumber"
      type="text"
      name="PONumber"
      :label="$ts('forms.sampleOrder.shippingInformation.po.label')"
      placeholder="00-00000-00"
    />
    <FormKit
      v-if="!readOnly || data.specialShippingInstruction"
      id="specialShippingInstruction"
      type="textarea"
      name="Comments"
      :label="$ts('forms.sampleOrder.shippingInformation.special.label')"
      :placeholder="$ts('forms.sampleOrder.shippingInformation.special.placeholder')"
      outer-class="min-h-50 md:col-span-2"
    />
  </FormKit>
</template>
