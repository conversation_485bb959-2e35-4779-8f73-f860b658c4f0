<script lang="ts" setup>
import type { SampleCartItem } from '@modernfold/types/sampleOrdering'

defineProps<{
  id: string
  name: string
  readOnly?: boolean
  tableLabel?: string
  vendor: string
}>()

const data = defineModel<{ Items?: SampleCartItem[] }>({
  required: true,
  default: {
    Items: [],
  },
})

defineEmits<{ (e: 'submit' | 'edit'): void }>()

const hiddenInputValue = ref<number>(0)

const updateQuantity = (item: Omit<SampleCartItem, 'Quantity'>, newVal: number) => {
  data.value.Items ||= []
  if (typeof newVal !== 'number') newVal = 0

  const existingItem = data.value.Items.find((i) => {
    return i.EMSPart === item.EMSPart
  })

  const delta = newVal - (existingItem?.Quantity ?? 0)
  if (typeof delta !== 'number') return

  if (!existingItem) {
    data.value.Items.push({
      ...item,
      Quantity: newVal,
    })
  }
  else {
    if (newVal === 0) {
      data.value.Items = data.value.Items.filter(i => i.EMSPart !== item.EMSPart)
    }
    else {
      existingItem.Quantity = newVal
    }
  }

  hiddenInputValue.value += delta > 0 ? delta : 0
}
</script>

<template>
  <FormKit
    :id="id"
    :name="name"
    type="form"
    :actions="false"
    @submit="$emit('submit')"
  >
    <FormKit
      id="samples"
      v-model="hiddenInputValue"
      number
      type="hidden"
      name="samples"
      validation="required|min:1"
    />
    <FormsStepQuantityReadOnly
      v-if="readOnly"
      :items="data.Items"
      :vendor="vendor"
      @click:edit="$emit('edit')"
    />
    <FormsStepQuantity
      v-else
      :model-value="data"
      :label="tableLabel ?? ''"
      :vendor="vendor"
      @update:quantity="updateQuantity"
    />
  </FormKit>
</template>
