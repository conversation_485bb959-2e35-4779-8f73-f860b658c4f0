<script setup lang="ts">
import carriers from '@forms/data/carriers.json'

const { $has, $ts } = useI18n()

const props = defineProps<{
  id: string
  name: string
  vendor: string
  readOnly?: boolean
}>()

defineEmits<{
  (e: 'submit' | 'edit'): void
}>()

const data = defineModel<Record<string, unknown>>({ required: true })

const { data: shippingOptions } = await useFetch(`/api/samples/vendors/${props.vendor}/shippingoptions`, {
  transform: (_rawData) => {
    return _rawData.map(option => ({
      label: option?.Text ?? '',
      value: option?.Value ?? '',
      acct: option?.RequiresAccountNumber ?? false,
    }))
  },
})

const acctNumberRequired = computed(() => {
  if (shippingOptions?.value?.length) {
    const activeShippingOpt = shippingOptions.value.find(({ value }) => value === data?.value?.CarrierType)
    return activeShippingOpt?.acct
  }

  return false
})
</script>

<template>
  <FormKit
    :id="id"
    v-model="data"
    :name="name"
    type="form"
    :actions="false"
    :disabled="readOnly"
    form-class="grid md:grid-cols-2 gap-xl bg-neutral p-padding-md md:p-padding-xxl rounded-sm"
    @submit="$emit('submit')"
  >
    <FormsStepHeader v-if="readOnly" :title="$ts('forms.sampleOrder.shippingMethod.title')" @click="$emit('edit')" />
    <div v-else class="md:col-span-full body-1">
      <p>{{ $ts('forms.sampleOrder.shippingMethod.description.text') }}</p>
      <ul v-if="$has('forms.sampleOrder.shippingMethod.description.list')" class="list-inside list-disc">
        <li v-for="(value, index) in $t('forms.sampleOrder.shippingMethod.description.list')" :key="index">
          <i18n-t :keypath="`forms.sampleOrder.shippingMethod.description.list.${index}`" :html="true" />
        </li>
      </ul>
    </div>
    <FormKit
      v-if="shippingOptions?.length"
      id="shippingInformation"
      type="radio"
      name="CarrierType"
      :label="!readOnly ? $ts('forms.sampleOrder.shippingMethod.information.label') + '*' : ''"
      validation="required"
      outer-class="md:col-span-full"
      :options="shippingOptions ?? []"
      option-class="flex items-end data-disabled:m-0 data-disabled:p-0 [&[data-disabled]>*]:hidden [&[data-disabled]>[data-checked]]:flex"
      :validation-messages="{
        required: $ts('forms.sampleOrder.shippingMethod.information.errors.required'),
      }"
    >
      <template #optionHelp="{ option }">
        <div class="text-neutral text-body-sm ml-6 -mt-1 formkit-optionHelp flex grow leading-6 text-right">
          <span v-if="option.help" class="text-info caption-1">{{ option.help }}</span>
          <span class="text-info caption-1 ml-auto max-w-20 md:max-w-none">{{ option.acct ? $ts('forms.sampleOrder.shippingMethod.information.help.account#Required') : $ts('forms.sampleOrder.shippingMethod.information.help.account#NotRequired') }}</span>
        </div>
      </template>
    </FormKit>
    <FormKit
      v-else
      type="hidden"
      validation="required"
    />
    <FormKit
      v-if="acctNumberRequired"
      id="carrier"
      name="Carrier"
      type="dropdown"
      :label="$ts('forms.sampleOrder.shippingMethod.carrier.label') + '*'"
      :placeholder="$ts('forms.sampleOrder.shippingMethod.carrier.placeholder')"
      :options="carriers"
      validation="required"
      :validation-messages="{
        required: $ts('forms.sampleOrder.shippingMethod.carrier.errors.required'),
      }"
    />
    <FormKit
      v-if="acctNumberRequired"
      id="acct"
      type="text"
      name="CarrierAcct"
      :label="$ts('forms.sampleOrder.shippingMethod.acct.label')"
      :placeholder="$ts('forms.sampleOrder.shippingMethod.acct.placeholder')"
      validation="required"
    />
  </FormKit>
</template>
