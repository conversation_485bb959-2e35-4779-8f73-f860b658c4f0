<script setup lang='ts'>
import type { InvoiceFilters } from '@modernfold/types/invoices'
import type { InvoiceFiltersFormProps } from './FormsInvoiceFilters.props'

defineProps<InvoiceFiltersFormProps>()

const emit = defineEmits<{
  (e: 'search' | 'reset'): void
}>()

const { $ts } = useI18n()

const formData = defineModel<InvoiceFilters>('formData', { required: true })

const handleReset = () => {
  emit('reset')
}
</script>

<template>
  <FormKit
    :id="id"
    v-model="formData"
    type="form"
    :actions="false"
    form-class="gap-xl grid-cols-1 md:grid-cols-2 grid"
  >
    <FormKit
      type="text"
      name="purchaseOrderNumber"
      :label="$ts('accountManager.invoices.form.purchaseOrderNumber')"
      outer-class="col-span-1 md:col-span-2"
    />

    <FormKit
      type="datepicker"
      name="beginDate"
      :label="$ts('accountManager.invoices.form.beginDate')"
      overlay
      popover
      format="YYYY/MM/DD"
    />
    <FormKit
      type="datepicker"
      name="endDate"
      :label="$ts('accountManager.invoices.form.endDate')"
      overlay
      popover
      format="YYYY/MM/DD"
    />

    <FormKit
      type="text"
      name="jobNumber"
      :label="$ts('accountManager.invoices.form.jobNumber')"
    />
    <FormKit
      type="text"
      name="jobName"
      :label="$ts('accountManager.invoices.form.jobName')"
    />

    <FormKit
      type="text"
      name="invoiceNumber"
      :label="$ts('accountManager.invoices.form.invoiceNumber')"
    />

    <div class="flex flex-col md:flex-row gap-4 col-span-1 md:col-span-2">
      <AtomsButton
        :state="hasFormFilters ? 'default' : 'disabled'"
        class="md:min-w-80"
        @click.prevent="emit('search')"
      >
        {{ $ts('accountManager.invoices.form.search') }}
      </AtomsButton>
      <AtomsButton
        v-if="hasAppliedFilters"
        anatomy="secondary"
        @click.prevent="handleReset"
      >
        {{ $ts('accountManager.invoices.form.resetFilters') }}
      </AtomsButton>
    </div>
  </FormKit>
</template>
