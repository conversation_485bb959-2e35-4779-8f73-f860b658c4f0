<script lang="ts" setup>
import type { LeadtimeTableItem } from '@modernfold/server/types/shippingSchedule'
import type { ColumnDef } from '@tanstack/vue-table'
import AtomsIcon from '@design-system/components/Atoms/AtomsIcon.vue'

const { $ts } = useI18n()

const { data: leadtimeData, pending: leadtimePending, error: leadtimeError } = await useFetch('/api/shipping-schedule/leadtime', {
  default: () => ({ leadtimeItems: [] }),
})

if (leadtimeError.value) {
  throw createError({
    statusCode: leadtimeError.value.statusCode || 500,
    statusMessage: leadtimeError.value.message || 'Failed to fetch leadtime data',
  })
}

const leadtimeColumns: ColumnDef<LeadtimeTableItem>[] = [
  {
    id: 'product',
    accessorKey: 'Product',
    header: ({ column }) => {
      return h('button', {
        class: 'flex items-center space-x-2 font-medium hover:text-opacity-80 transition-colors',
        onClick: () => column.toggleSorting(),
      }, [
        h('span', $ts('accountManager.shipments.tableHeader.product')),
        h(AtomsIcon, {
          name: column.getIsSorted() === 'asc' ? 'NavArrowUp' : 'NavArrowDown',
          size: '16',
        }),
      ])
    },
    meta: { width: '60%' },
    cell: ({ row }) => h('div', { class: 'font-medium' }, row.original.Product),
    enableSorting: true,
    sortingFn: 'alphanumeric',
    sortDescFirst: false,
  },
  {
    id: 'booking',
    header: $ts('accountManager.shipments.tableHeader.booking'),
    meta: { width: '40%' },
    cell: ({ row }) => h('div', {}, row.original.Booking),
  },
]
</script>

<template>
  <div>
    <div v-if="leadtimeError">
      <p> {{ $ts(`accountManager.shipments.errors.leadtime`) }}</p>
    </div>
    <div v-if="leadtimeData?.leadtimeItems?.length">
      <MoleculesTable
        :columns="leadtimeColumns"
        :items="leadtimeData.leadtimeItems"
        :loading="leadtimePending"
        :initial-sorting="[{ id: 'product', desc: false }]"
        class="w-full"
      />
    </div>
  </div>
</template>
