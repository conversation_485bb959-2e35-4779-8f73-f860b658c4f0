<script lang="ts" setup>
import type { ColumnDef } from '@tanstack/vue-table'
import MoleculesTable from '@design-system/components/Molecules/Table/MoleculesTable.vue'
import AtomsIcon from '@design-system/components/Atoms/AtomsIcon.vue'
import type { TracksTableItem } from '@modernfold/server/types/shippingSchedule'

const { $ts } = useI18n()

const { data: tracksData, pending: tracksPending, error: tracksError } = await useFetch('/api/shipping-schedule/tracksleadtime', {
  default: () => ({ tracksItems: [] }),
})

if (tracksError.value) {
  throw createError({
    statusCode: tracksError.value.statusCode || 500,
    statusMessage: tracksError.value.message || 'Failed to fetch tracks leadtime data',
  })
}

const tracksColumns: ColumnDef<TracksTableItem>[] = [
  {
    id: 'trackSwitches',
    accessorKey: 'TrackSwitches',
    header: ({ column }) => {
      return h('button', {
        class: 'flex items-center space-x-2 font-medium hover:text-opacity-80 transition-colors',
        onClick: () => column.toggleSorting(),
      }, [
        h('span', $ts('accountManager.shipments.tableHeader.trackSwitches')),
        h(AtomsIcon, {
          name: column.getIsSorted() === 'asc' ? 'NavArrowUp' : 'NavArrowDown',
          size: '16',
        }),
      ])
    },
    meta: { width: '60%' },
    cell: ({ row }) => h('div', { class: 'font-medium' }, row.original.TrackSwitches),
    enableSorting: true,
    sortingFn: 'alphanumeric',
    sortDescFirst: false,
  },
  {
    id: 'booking',
    header: $ts('accountManager.shipments.tableHeader.booking'),
    meta: { width: '40%' },
    cell: ({ row }) => h('div', {}, dateFormatter(row.original.Booking) ?? ''),
  },
]

// TODO: this formatter is used only here, and it's probably will be removed once the client has clarified general date formatting style
const dateFormatter = (date: string) => {
  if (!validateDate(date)) {
    console.error(`Failed date validation: ${date}`)
    return null
  }
  return new Date(date).toLocaleDateString('en-EU', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}
</script>

<template>
  <div>
    <div v-if="tracksError">
      <p> {{ $ts(`accountManager.shipments.errors.trackleadtime`) }}</p>
    </div>
    <div v-if="tracksData?.tracksItems?.length">
      <MoleculesTable
        :columns="tracksColumns"
        :items="tracksData.tracksItems"
        :loading="tracksPending"
        :initial-sorting="[{ id: 'trackSwitches', desc: false }]"
        class="w-full"
      />
    </div>
  </div>
</template>
