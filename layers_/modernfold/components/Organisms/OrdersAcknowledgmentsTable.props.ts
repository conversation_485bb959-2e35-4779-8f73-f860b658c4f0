export interface OrdersAcknowledgmentRow {
  id: string
  orderNumber: string
  poNumber: string
  jobName: string
  distributorAnticipatedShipDate: string
  dateOrder: string
  dateSchedule: string
  job: string
  status: string
  gross: string
  net: string
  discount: string
  units: number
  orderType: string
  reviseOrderLink: string
}

export type OrdersAcknowledgmentTableProps = {
  data: OrdersAcknowledgmentRow[]
  loading?: boolean
}
