<script lang="ts" setup>
import type { OrdersStatusSummaryTableProps } from './OrdersStatusSummaryTable.props'

const props = defineProps<OrdersStatusSummaryTableProps>()

const { $ts } = useI18n()
const columns = [
  {
    header: $ts('ordersAcknowledgments.table.headers.status'),
    accessorKey: 'Status',
  },
  {
    header: $ts('ordersAcknowledgments.table.headers.gross'),
    accessor<PERSON>ey: 'Gross',
  },
  {
    header: $ts('ordersAcknowledgments.table.headers.net'),
    accessorKey: 'Net',
  },
  {
    header: $ts('ordersAcknowledgments.table.headers.discountPer'),
    accessor<PERSON>ey: 'Discount',
  },

]
</script>

<template>
  <div>
    <MoleculesTable :columns="columns" :items="props.data" :loading="props.loading" />
  </div>
</template>
