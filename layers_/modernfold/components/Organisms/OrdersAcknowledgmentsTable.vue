<script setup lang="ts">
import type { ColumnDef } from '@tanstack/vue-table'
import AtomsIcon from '@design-system/components/Atoms/AtomsIcon.vue'
import AtomsLink from '@design-system/components/Atoms/Link/AtomsLink.vue'
import type { OrdersAcknowledgmentRow, OrdersAcknowledgmentTableProps } from './OrdersAcknowledgmentsTable.props'

defineProps<OrdersAcknowledgmentTableProps>()

const { $ts } = useI18n()
const tableColumns: ColumnDef<OrdersAcknowledgmentRow>[] = [
  {
    accessorKey: 'orderNumber',
    header: ({ column }) => {
      return h('button', {
        class: 'flex items-center space-x-2 transition-colors w-full text-nowrap',
        onClick: () => column.toggleSorting(),
      }, [
        h('span', $ts('ordersAcknowledgments.table.headers.orderNumber')),
        h(AtomsIcon, {
          name: column.getIsSorted() === 'asc' ? 'NavArrowUp' : 'NavArrowDown',
          size: '16',
          class: 'shrink-0',
        }),
      ])
    },
    enableSorting: true,
    sortingFn: 'alphanumeric',
    sortDescFirst: false,
    cell: cellData => h(AtomsLink, {
      label: String(cellData.getValue()),
    }),
  },
  {
    accessorKey: 'poNumber',
    header: $ts('ordersAcknowledgments.table.headers.poNumber'),
  },
  {
    accessorKey: 'jobName',
    header: $ts('ordersAcknowledgments.table.headers.jobName'),
  },
  {
    accessorKey: 'distributorAnticipatedShipDate',
    header: $ts('ordersAcknowledgments.table.headers.distributorAnticipatedShipDate'),
  },
  {
    accessorKey: 'dateOrder',
    header: $ts('ordersAcknowledgments.table.headers.dateOrder'),
    cell: cellData => h('p', {}, formattedDate(String(cellData.getValue())) ?? ''),
  },
  {
    accessorKey: 'dateSchedule',
    header: $ts('ordersAcknowledgments.table.headers.dateSchedule'),
    cell: cellData => h('p', {}, formattedDate(String(cellData.getValue())) ?? ''),
  },
  {
    accessorKey: 'job',
    header: $ts('ordersAcknowledgments.table.headers.job'),
  },
  {
    accessorKey: 'status',
    header: $ts('ordersAcknowledgments.table.headers.status'),
  },
  {
    accessorKey: 'gross',
    header: $ts('ordersAcknowledgments.table.headers.gross'),
  },
  {
    accessorKey: 'net',
    header: $ts('ordersAcknowledgments.table.headers.net'),
  },
  {
    accessorKey: 'discount',
    header: $ts('ordersAcknowledgments.table.headers.discount'),
  },
  {
    accessorKey: 'units',
    header: $ts('ordersAcknowledgments.table.headers.units'),
  },
  {
    accessorKey: 'orderType',
    header: $ts('ordersAcknowledgments.table.headers.orderType'),
  },
  {
    accessorKey: 'reviseOrderLink',
    header: $ts('ordersAcknowledgments.table.headers.reviseOrderLink'),
    cell: cellData => h(AtomsLink, {
      label: String(cellData.getValue()),
    }),
  },
]
</script>

<template>
  <div>
    <MoleculesTable
      :columns="tableColumns"
      :items="data"
      :loading="loading"
      paginated-table
      scrollable="x"
      class="table-container"
    />
  </div>
</template>

<style scoped>
/* :deep(.table-container) {
  margin-right: min(
    calc(-1 * calc((100vw - var(--spacing-page-container)) / 2 + var(--grid-system-spacing-x) - 10px)),
    calc(-1 * var(--grid-system-spacing-x))
  );
} */
</style>
