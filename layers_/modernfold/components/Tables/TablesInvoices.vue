<script setup lang='ts'>
import type { ColumnDef } from '@tanstack/vue-table'
import AtomsIcon from '@design-system/components/Atoms/AtomsIcon.vue'
import AtomsLink from '@design-system/components/Atoms/Link/AtomsLink.vue'
import type { InvoiceRow } from '@modernfold/types/invoices'
import type { TablesInvoicesProps } from './TablesInvoices.props'

defineProps<TablesInvoicesProps>()

const { $ts } = useI18n()

const tableColumns: ColumnDef<InvoiceRow>[] = [
  {
    accessorKey: 'invoiceNumber',
    enableSorting: true,
    sortingFn: 'alphanumeric',
    sortDescFirst: false,
    meta: { width: '20%' },
    cell: cellData => h(AtomsLink, {
      label: String(cellData.getValue()),
    }),

    header: ({ column }) => {
      return h('button', {
        class: 'flex items-center space-x-2 transition-colors w-full',
        onClick: () => column.toggleSorting(),
      }, [
        h('span', $ts('accountManager.invoices.table.headers.invoiceNumber')),
        h(AtomsIcon, {
          name: column.getIsSorted() === 'asc' ? 'NavArrowUp' : 'NavArrowDown',
          size: '16',
          class: 'shrink-0',
        }),
      ])
    },
  },
  {
    header: $ts('accountManager.invoices.table.headers.invoiceDate'),
    accessorKey: 'invoiceDate',
    cell: cellData => h('p', { class: 'whitespace-nowrap' }, String(formattedDate(cellData.row.original.invoiceDate))),
  },
  {
    header: $ts('accountManager.invoices.table.headers.poNumber'),
    accessorKey: 'poNumber',
  },
  {
    header: $ts('accountManager.invoices.table.headers.orderNumber'),
    accessorKey: 'orderNumber',
  },
  {
    header: $ts('accountManager.invoices.table.headers.jobNumber'),
    accessorKey: 'jobNumber',
  },
  {
    header: $ts('accountManager.invoices.table.headers.jobName'),
    accessorKey: 'jobName',
    cell: cellData => h('p', { class: 'whitespace-nowrap' }, String(cellData.getValue())),
  },
  {
    header: $ts('accountManager.invoices.table.headers.totalUsd'),
    accessorKey: 'totalUsd',
    cell: cellData => h('p', { }, String(cellData.getValue())),
  },
]
</script>

<template>
  <MoleculesTable
    :columns="tableColumns"
    :items="items"
    paginated-table
    scrollable="x"
    class="max-md:-mr-(--grid-system-spacing-x) "
  />
</template>
