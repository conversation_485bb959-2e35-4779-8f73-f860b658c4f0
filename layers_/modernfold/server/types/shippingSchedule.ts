import * as v from 'valibot'

export const ProductLeadtimeSchema = v.object({
  Product: v.string(),
  Revision: v.number(),
  EnteredDate: v.string(),
  EnteredBy: v.string(),
  Active: v.number(),
  LeadTimeDate: v.string(),
  LeadTime_Note: v.string(),
})

export const TracksLeadtimeSchema = v.object({
  Product: v.string(),
  Revision: v.number(),
  EnteredDate: v.string(),
  EnteredBy: v.string(),
  Active: v.number(),
  LeadTimeDate: v.string(),
})

export const TracksLeadtimeResponseSchema = v.object({
  tracksLeadTime: v.array(TracksLeadtimeSchema),
})

export const ProductsLeadtimeResponseSchema = v.object({
  productsleadtime: v.array(ProductLeadtimeSchema),
})

export const LeadtimeTableItemSchema = v.object({
  id: v.string(),
  Product: v.string(),
  Booking: v.string(),
})

export const TracksTableItemSchema = v.object({
  id: v.string(),
  TrackSwitches: v.string(),
  Booking: v.string(),
})

export type ProductLeadtime = v.InferOutput<typeof ProductLeadtimeSchema>
export type TracksLeadtime = v.InferOutput<typeof TracksLeadtimeSchema>
export type LeadtimeTableItem = v.InferOutput<typeof LeadtimeTableItemSchema>
export type TracksTableItem = v.InferOutput<typeof TracksTableItemSchema>
