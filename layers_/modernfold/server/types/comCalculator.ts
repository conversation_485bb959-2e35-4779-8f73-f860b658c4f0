import * as v from 'valibot'

export const AcoustiSealCalculatorBodySchema = v.object({
  FabricationHeight: v.number(),
  NumPanels: v.number(),
  CoveringSides: v.number(),
  NumExpClosures: v.optional(v.number()),
  NumMatchingPassDoors: v.optional(v.number()),
  NumTypeIPocketDoors: v.optional(v.number()),
  NumTypeIVPocketDoors: v.optional(v.number()),
})

export const AcoustiSealCalculatorResponseSchema = v.object({
  numOfSides: v.number(),
  yardsNeeded: v.number(),
})

export type AcoustiSealCalculatorForm = v.InferInput<typeof AcoustiSealCalculatorBodySchema>
export type AcoustiSealCalculatorResponse = v.InferInput<typeof AcoustiSealCalculatorResponseSchema>
