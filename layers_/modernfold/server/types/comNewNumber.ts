import * as v from 'valibot'

export const COMNewNumberBodySchema = v.object({
  RequestDate: v.string(), // ISO date string
  SubmittedBy: v.string(),
  DistributorId: v.number(),
  Phone: v.string(),
  JobNumber: v.optional(v.string()),
  OrderNumber: v.optional(v.string()),
  DistributorPO: v.optional(v.string()),
  JobName: v.string(),
  PanelSkin: v.number(),
  ShipDate: v.string(), // ISO date string
  Covering: v.number(),
  Manufacturer: v.string(),
  Pattern: v.string(),
  Color: v.optional(v.string()),
  Line: v.string(),
  Quantity: v.nullable(v.number()),
  PartNumber: v.optional(v.string()),
  Notes: v.nullable(v.string()),
  ApprovalNotes: v.nullable(v.string()),
  Email: v.string(),
  UserId: v.string(),
})

export type COMNewNumberResponseType = v.InferInput<typeof COMNewNumberBodySchema>

export const ComNewNumberResponseSchema = v.object({
  comId: v.number(),
  success: v.boolean(),
})
