import * as v from 'valibot'

// valid for DIST and PO not sure if it's the case for INV
export const ApiInvoiceHeaderSchema = v.object({
  ID_INVC: v.number(),
  DATE_INVC: v.string(),
  ID_PO_CUST: v.optional(v.union([v.number(), v.string()])),
  ID_ORD: v.number(),
  ID_JOB: v.optional(v.union([v.number(), v.string()])),
  NAME_ORD_BY: v.optional(v.string()),
  AMT_INVC_TOTAL: v.optional(v.number()),
  AMT_INVC_TOTAL_FOR: v.optional(v.number()),
  System: v.optional(v.string()),
  Currency: v.optional(v.string()),
})

export const ApiInvoiceResponseSchema = v.object({
  invoiceHeader: v.array(ApiInvoiceHeaderSchema),
})

export type APIInvoiceResponse = v.InferOutput<typeof ApiInvoiceResponseSchema>
