import * as v from 'valibot'

export const SampleOrderingVendorSchema = v.object({
  VendorID: v.number(),
  VendorName: v.string(),
})

export const SampleOrderingVendorsResponseSchema = v.object({
  vendors: v.array(SampleOrderingVendorSchema),
})

export const SampleOrderingBodySchema = v.object({
  VendorID: v.number(),
  Name: v.string(),
  DistributorID: v.number(),
  CompanyName: v.string(),
  Address: v.string(),
  Address2: v.string(),
  City: v.string(),
  State: v.string(),
  Zip: v.string(),
  Country: v.string(),
  Phone: v.string(),
  Email: v.string(),
  Comments: v.optional(v.string()),
  CarrierType: v.string(),
  Carrier: v.optional(v.string()),
  CarrierAcct: v.optional(v.string()),
  PONumber: v.optional(v.string()),
  Items: v.array(
    v.object({ EMSPart: v.string(), Item: v.string(), Quantity: v.number() }),
  ),
})

export const SampleOrderingResponseSchema = v.object({
  success: v.boolean(),
  orderId: v.number(),
})

export type SampleOrderingResponseType = v.InferInput<typeof SampleOrderingResponseSchema>
export type SampleOrderingRequestType = v.InferInput<typeof SampleOrderingBodySchema>
