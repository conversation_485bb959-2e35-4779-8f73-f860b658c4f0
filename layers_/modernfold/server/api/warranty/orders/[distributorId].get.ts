import * as v from 'valibot'
import { ApiWarrantySelectArraySchema } from '@modernfold/server/types/warrantySelect'

export type WarrantyOrdersRow = v.InferOutput<typeof ApiWarrantySelectArraySchema>

export default defineEventHandler(async (event) => {
  const logPrefix = '[API /warranty/orders]'
  const distributorId = getRouterParam(event, 'distributorId')

  if (!distributorId) {
    throw createError({
      statusCode: 400,
      statusMessage: `${logPrefix} Missing distributorId`,
    })
  }

  try {
    const apiFetch = getApiClient()

    const rawData = await apiFetch<{ warrantyOrders: WarrantyOrdersRow[] }>(`/jobhistory/orders/${distributorId}`)

    const validatedRawOrders = v.parse(ApiWarrantySelectArraySchema, rawData)
    return validatedRawOrders.jobhistory
  }
  catch (error) {
    throw createError({
      statusCode: 500,
      statusMessage: `${logPrefix} Error fetching or processing warranty orders`,
      data: error,
    })
  }
})
