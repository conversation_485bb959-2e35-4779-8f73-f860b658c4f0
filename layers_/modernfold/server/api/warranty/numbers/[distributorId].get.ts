import * as v from 'valibot'
import { ApiWarrantySelectArraySchema } from '@modernfold/server/types/warrantySelect'

export type WarrantyNumbersRow = v.InferOutput<typeof ApiWarrantySelectArraySchema>

export default defineEventHandler(async (event) => {
  const logPrefix = '[API /warranty/numbers]'
  const distributorId = getRouterParam(event, 'distributorId')

  if (!distributorId) {
    throw createError({
      statusCode: 400,
      statusMessage: `${logPrefix} Missing distributorId`,
    })
  }

  try {
    const apiFetch = getApiClient()
    const rawData = await apiFetch<{ warrantyNumbers: WarrantyNumbersRow[] }>(`/jobhistory/numbers/${distributorId}`)

    const validatedRawNumbers = v.parse(ApiWarrantySelectArraySchema, rawData)

    return validatedRawNumbers.jobhistory
  }
  catch (error) {
    throw createError({
      statusCode: 500,
      statusMessage: `${logPrefix} Error fetching or processing warranty numbers`,
      data: error,
    })
  }
})
