import * as v from 'valibot'
import { ApiWarrantySelectArraySchema } from '@modernfold/server/types/warrantySelect'

export type WarrantyNamesRow = v.InferOutput<typeof ApiWarrantySelectArraySchema>

export default defineEventHandler(async (event) => {
  const logPrefix = '[API /warranty/names]'
  const distributorId = getRouterParam(event, 'distributorId')

  if (!distributorId) {
    throw createError({
      statusCode: 400,
      statusMessage: `${logPrefix} Missing distributorId`,
    })
  }

  try {
    const apiFetch = getApiClient()

    const rawData = await apiFetch<{ warrantyNames: WarrantyNamesRow[] }>(`/jobhistory/names/${distributorId}`)

    const validatedRawNames = v.parse(ApiWarrantySelectArraySchema, rawData)
    return validatedRawNames.jobhistory
  }
  catch (error) {
    throw createError({
      statusCode: 500,
      statusMessage: `${logPrefix} Error fetching or processing warranty names`,
      data: error,
    })
  }
})
