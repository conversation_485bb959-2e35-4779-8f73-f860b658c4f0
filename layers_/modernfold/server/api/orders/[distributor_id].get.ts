import * as v from 'valibot'
// api/v1/tcm/orders/{{distributorid}} => /api/orders/{{distributorid}}

const OrderSchema = v.object({
  'Order Number Text': v.string(),
  'Purchase Number': v.string(),
  'Job Name': v.string(),
  'Date_RQST': v.nullish(v.string()),
  'Date Ordered': v.string(),
  'Date Scheduled': v.nullish(v.string()),
  'Job Number': v.string(),
  'Status': v.string(),
  'TentativeDate': v.string(),
  'Gross': v.string(),
  'Net': v.string(),
  'DiscountPer': v.string(),
  'Units': v.number(),
  'OrderType': v.string(),
  'RepEmail': v.string(),
})

const ApiDistributorOrdersSchema = v.object({
  orders: v.array(OrderSchema),
})

export type OrdersResponse = v.InferOutput<typeof ApiDistributorOrdersSchema>

export default defineEventHandler(async (event) => {
  const logPrefix = '[API /orders/distributor_id]'

  try {
    const distributorId = getRouterParam(event, 'distributor_id')

    if (!distributorId) {
      throw createError({
        statusCode: 400,
        statusMessage: `${logPrefix} Missing 'distributorId' router param.`,
      })
    }

    const apiFetch = getApiClient()

    const data = await apiFetch(`/tcm/orders/${distributorId}`)

    const parsedResponse = v.parse(ApiDistributorOrdersSchema, data)

    return parsedResponse
  }
  catch (error) {
    console.error(`${logPrefix} Handler error`, error)

    throw createError({
      statusCode: 500,
      statusMessage: `${logPrefix} Unexpected server error.`,
    })
  }
})
