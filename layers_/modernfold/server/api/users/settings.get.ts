// TODO: authentication
import * as v from 'valibot'

export default defineEventHandler(async (event) => {
  const { user } = await requireRefreshedUserSession(event)
  // @ts-expect-error TODO: nuxt-auth-utils not typed correctly
  const username = user.email

  const apiFetch = getApiClient()
  const data = await apiFetch(`/users/settings`, {
    query: {
      username,
    },
  })
  const userSettings = v.parse(userSettingsSchema, data)
  return userSettings
})

const userSettingsSchema = v.partial(v.object({
  userSettings: v.object({
    CorporateUserId: v.nullable(v.number()),

    CreateDate: v.string(),
    DefaultSales: v.number(),
    DistributorId: v.number(),
    Email: v.nullable(v.string()),
    FirstName: v.string(),
    LastName: v.string(),

    HasCorporateAccount: v.boolean(),
    HasMPMAccount: v.boolean(),
    HasPartnerAccount: v.boolean(),

    InternalLevelId: v.number(),
    InternalUser: v.number(),
    IsDistributor: v.boolean(),
    IsSuperUser: v.boolean(),

    LastLogin: v.string(),
    LegacyUser: v.nullable(v.string()),
    ModifiedBy: v.string(),

    MPMUserId: v.string(),
    NoGlass: v.number(),
    PartnerAccountIsDisabled: v.number(),
    PartnerUserId: v.string(),
    Password: v.string(),

    SearchSort: v.number(),
    SecurityLevelId: v.number(),
    SeismicBracing: v.number(),
    SWallTrack: v.number(),

    SystemTS: v.string(),
    Units: v.number(),

    ContactId: v.number(),
    Phone: v.string(),
    Fax: v.string(),
    Cell: v.string(),
    Pager: v.string(),
    Title: v.string(),

    Admin: v.number(),
    Orders: v.number(),
    Invoices: v.number(),
    Statements: v.number(),
    Warranty: v.number(),
    Parts: v.number(),
    MPM: v.number(),
    EmailDrawings: v.number(),
  }),
}))

export type UserSettings = v.InferOutput<typeof userSettingsSchema>
