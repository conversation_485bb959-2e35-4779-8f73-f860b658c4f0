import * as v from 'valibot'

const DistributorSchema = v.object({
  distributors: v.array(
    v.object({
      Id: v.number(),
      CompanyName: v.string(),
      DisplayDesc: v.string(),
    }),
  ),
})

export type Distributor = v.InferOutput<typeof DistributorSchema>

export default defineEventHandler(async (event) => {
  const loggerPrefix = '[API /users/distributors]'

  try {
    const apiFetch = getApiClient()

    const { user } = await requireRefreshedUserSession(event)
    // @ts-expect-error TODO: nuxt-auth-utils not typed correctly
    const username = user.email

    const data = await apiFetch(`/users/distributors?username=${username}`)

    const distributors = v.parse(DistributorSchema, data)

    return distributors
  }
  catch (error) {
    console.error(`${loggerPrefix} Handler error`, error)

    throw createError({
      statusCode: 500,
      statusMessage: `${loggerPrefix} Unexpected server error.`,
    })
  }
})
