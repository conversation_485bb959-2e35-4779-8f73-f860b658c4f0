import * as v from 'valibot'

const ApiSampleItemSchema = v.object({
  VendorID: v.number(),
  EMSPart: v.string(),
  Desc1: v.string(),
  Desc2: v.string(),
  DisplayPart: v.string(),
  MaxQuan: v.number(),
})
const ApiSampleItemsArraySchema = v.array(ApiSampleItemSchema)

export type SampleProductRow = v.InferOutput<typeof ApiSampleItemSchema>

export default defineEventHandler(async (event) => {
  const logPrefix = '[API /samples/items]'
  const vendorId = getRouterParam(event, 'sampleVendorId')

  if (!vendorId) {
    throw createError({
      statusCode: 400,
      statusMessage: `${logPrefix} Missing sampleVendorId`,
    })
  }

  try {
    const apiFetch = getApiClient()

    const rawDataFromExternalAPI = await apiFetch<{ sampleItems: SampleProductRow[] }>(`/samples/items/${vendorId}`)

    const validatedRawItems = v.parse(ApiSampleItemsArraySchema, rawDataFromExternalAPI?.sampleItems)
    return validatedRawItems
  }
  catch (error) {
    throw createError({
      statusCode: 500,
      statusMessage: `${logPrefix} Error fetching or processing sample items`,
      data: error,
    })
  }
})
