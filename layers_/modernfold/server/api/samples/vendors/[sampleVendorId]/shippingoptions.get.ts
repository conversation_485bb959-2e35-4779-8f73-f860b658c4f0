import * as v from 'valibot'

const OptionSchema = v.object({
  shippingOptions: v.array(
    v.object({
      Text: v.string(),
      Value: v.string(),
      RequiresAccountNumber: v.boolean(),
    }),
  ),
})

export default defineEventHandler(async (event) => {
  const loggerPrefix = '[API /samples/shippingoptions]'

  try {
    const apiFetch = getApiClient()
    const vendorId = getRouterParam(event, 'sampleVendorId')

    if (!vendorId) {
      throw createError({
        statusCode: 400,
        statusMessage: `${loggerPrefix} Missing 'sampleVendorId' router param.`,
      })
    }

    const data = await apiFetch(`/samples/vendors/${vendorId}/shippingoptions`)

    const { shippingOptions } = v.parse(OptionSchema, data)

    return shippingOptions
  }
  catch (error) {
    console.error(`${loggerPrefix} Handler error`, error)

    throw createError({
      statusCode: 500,
      statusMessage: `${loggerPrefix} Unexpected server error.`,
    })
  }
})
