import * as v from 'valibot'
import { getApiClient } from '@modernfold/server/utils/api'
import { SampleOrderingVendorsResponseSchema } from '@modernfold/server/types/sampleordering'

export async function fetchVendors() {
  const apiFetch = getApiClient()
  const data = await apiFetch(`/samples/vendors/`)
  const { vendors } = v.parse(SampleOrderingVendorsResponseSchema, data)
  return vendors
}

export default defineEventHandler(async () => {
  const loggerPrefix = '[API /samples/vendors]'

  try {
    const vendors = await fetchVendors()

    return vendors
  }
  catch (error) {
    console.error(`${loggerPrefix} Handler error`, error)

    throw createError({
      statusCode: 500,
      statusMessage: `${loggerPrefix} Unexpected server error.`,
    })
  }
})
