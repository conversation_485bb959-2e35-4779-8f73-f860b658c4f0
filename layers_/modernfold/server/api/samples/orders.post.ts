import * as v from 'valibot'
import { SampleOrderingBodySchema, SampleOrderingResponseSchema } from '@modernfold/server/types/sampleordering'

export default defineEventHandler(async (event) => {
  const loggerPrefix = '[API /samples/orders]'
  try {
    const body = await readValidatedBody(event, body => v.parse(SampleOrderingBodySchema, body))

    const apiFetch = getApiClient()
    const data = await apiFetch('/samples/orders', {
      method: 'POST',
      body,
    })

    const sampleOrderingResponse = v.parse(SampleOrderingResponseSchema, data)
    return sampleOrderingResponse
  }
  catch (error) {
    if (error instanceof Error) {
      const maybeWithData = error as Error & { data?: { Message?: string } }

      console.error(`${loggerPrefix} Handler error`, error)

      throw createError({
        statusCode: 500,
        statusMessage: `${loggerPrefix} Unexpected server error`,
        message: maybeWithData.data?.Message ?? '',
      })
    }
  }
})
