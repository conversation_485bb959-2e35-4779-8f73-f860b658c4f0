import * as v from 'valibot'
import {
  ProductsLeadtimeResponseSchema,
  type ProductLeadtime,
  type LeadtimeTableItem,
} from '@modernfold/server/types/shippingSchedule'

function mapLeadtimeItems(productsleadtime: ProductLeadtime[]): LeadtimeTableItem[] {
  return productsleadtime
    .filter(item => item.Active === 1)
    .map((item, index) => ({
      id: `${item.Product}-${index}`,
      Product: item.Product,
      Booking: item.LeadTimeDate,
    }))
}

export default defineEventHandler(async (event) => {
  const loggerPrefix = '[API /shipping-schedule/leadtime]'

  try {
    const apiFetch = getApiClient()

    const query = getQuery(event)

    const data = await apiFetch('/products/leadtime', {
      query,
    })

    const productsLeadtime = v.parse(ProductsLeadtimeResponseSchema, data)

    const leadtimeItems = mapLeadtimeItems(productsLeadtime.productsleadtime)

    return {
      leadtimeItems,
    }
  }
  catch (error) {
    console.error(`${loggerPrefix} Handler error`, error)

    throw createError({
      statusCode: 500,
      statusMessage: `${loggerPrefix} Unexpected server error.`,
    })
  }
})
