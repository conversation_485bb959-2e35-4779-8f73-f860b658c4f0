import * as v from 'valibot'
import {
  TracksLeadtimeResponseSchema,
  type TracksLeadtime,
  type TracksTableItem,
} from '@modernfold/server/types/shippingSchedule'

function mapProductsLeadtimeToTracksItems(productsleadtime: TracksLeadtime[]): TracksTableItem[] {
  return productsleadtime
    .filter(item => item.Active === 1)
    .map((item, index) => ({
      id: `tracks-${item.Product}-${index}`,
      TrackSwitches: item.Product,
      Booking: item.LeadTimeDate,
    }))
}

export default defineEventHandler(async (event) => {
  const loggerPrefix = '[API /shipping-schedule/tracksleadtime]'

  try {
    const apiFetch = getApiClient()

    const query = getQuery(event)

    const data = await apiFetch('/products/tracksleadtime', {
      query,
    })

    const tracksLeadtime = v.parse(TracksLeadtimeResponseSchema, data)

    const tracksItems = mapProductsLeadtimeToTracksItems(tracksLeadtime.tracksLeadTime)

    return {
      tracksItems,
    }
  }
  catch (error) {
    console.error(`${loggerPrefix} Handler error`, error)

    throw createError({
      statusCode: 500,
      statusMessage: `${loggerPrefix} Unexpected server error.`,
    })
  }
})
