import * as v from 'valibot'

const OptionSchema = v.object({
  distributors: v.array(
    v.object({
      DistributorID: v.nullish(v.number()),
      Status: v.nullish(v.string()),
      SegEffDate: v.nullish(v.string()),
      SegExpDate: v.nullish(v.string()),
      SegStatus: v.nullish(v.number()),
      SalesRating: v.nullish(v.string()),
      SalesRatingDate: v.nullish(v.string()),
      RepID: v.nullish(v.number()),
      Region: v.nullish(v.number()),
      CompanyName: v.nullish(v.string()),
      City: v.nullish(v.string()),
      State: v.nullish(v.string()),
      AreaCovered: v.nullish(v.string()),
      EMail: v.nullish(v.string()),
      Class: v.nullish(v.string()),
      Address1: v.nullish(v.string()),
      Address2: v.nullish(v.string()),
      PostalPrefix: v.nullish(v.string()),
      ZipCode: v.nullish(v.string()),
      ZipCodePlus: v.nullish(v.string()),
      Country: v.nullish(v.string()),
      Phone: v.nullish(v.string()),
      Fax: v.nullish(v.string()),
      Comments: v.nullish(v.string()),
      CadCapabilities: v.nullish(v.string()),
      WebSite: v.nullish(v.string()),
      TollFreeNumber: v.nullish(v.string()),
      SystemTS: v.nullish(v.string()),
      RepFName: v.nullish(v.string()),
      RepLName: v.nullish(v.string()),
      RepInitials: v.nullish(v.string()),
      RepOffice: v.nullish(v.string()),
      RepFax: v.nullish(v.string()),
      RepEMail: v.nullish(v.string()),
      Region1: v.nullish(v.number()),
      RMMFName: v.nullish(v.string()),
      RMMLName: v.nullish(v.string()),
      RMM: v.nullish(v.string()),
      Initials: v.nullish(v.string()),
      RMMPhone: v.nullish(v.string()),
      Beeper: v.nullish(v.string()),
      Cell: v.nullish(v.string()),
      RMMFax: v.nullish(v.string()),
      RMMEmail: v.nullish(v.string()),
      UserID: v.nullish(v.string()),
      SystemTS1: v.nullish(v.string()),
      City2: v.nullish(v.string()),
      State2: v.nullish(v.string()),
      ZipCode2: v.nullish(v.string()),
      MaterialCode: v.nullish(v.string()),
      Grade: v.nullish(v.string()),
    }),
  ),
})

export default defineEventHandler(async (event) => {
  const loggerPrefix = '[API /distributors]'

  try {
    const apiFetch = getApiClient()
    const distributorId = getRouterParam(event, 'id')

    if (!distributorId) {
      throw createError({
        statusCode: 400,
        statusMessage: `${loggerPrefix} Missing 'distributorId' router param.`,
      })
    }

    const data = await apiFetch(`/distributors/${distributorId}`)

    const { distributors } = v.parse(OptionSchema, data)

    return distributors
  }
  catch (error) {
    console.error(`${loggerPrefix} Handler error`, error)

    throw createError({
      statusCode: 500,
      statusMessage: `${loggerPrefix} Unexpected server error.`,
    })
  }
})
