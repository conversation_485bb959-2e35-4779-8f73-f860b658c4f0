import * as v from 'valibot'
import { getApiClient } from '@modernfold/server/utils/api'
import { ApiInvoiceResponseSchema } from '@modernfold/server/types/invoices'

export default defineEventHandler(async (event) => {
  const logPrefix = '[API /distributors/invoices]'

  try {
    const apiFetch = getApiClient()
    const distributorId = getRouterParam(event, 'id')
    const type = getRouterParam(event, 'type')
    const searchfor = getQuery(event).searchfor

    if (!distributorId || !type) {
      throw createError({
        statusCode: 400,
        statusMessage: `${logPrefix} Missing 'distributorId' or 'type' router param.`,
      })
    }

    if (searchfor && type !== 'INV') {
      throw createError({
        statusCode: 400,
        statusMessage: `${logPrefix} 'searchfor' is not allowed for '${type}' type.`,
      })
    }

    let query = `/tcm/invoice/headers/distributor/${distributorId}?type='${type}'`

    if (searchfor) {
      query += `&searchfor=${searchfor}`
    }

    const data = await apiFetch(query)

    const validatedData = v.parse(ApiInvoiceResponseSchema, data)

    return validatedData
  }
  catch (error) {
    console.error(`${logPrefix} Handler error`, error)

    throw createError({
      statusCode: 500,
      statusMessage: `${logPrefix} Unexpected server error.`,
      data: error,
    })
  }
})
