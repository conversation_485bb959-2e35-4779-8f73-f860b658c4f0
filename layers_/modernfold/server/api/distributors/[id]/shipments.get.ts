import * as v from 'valibot'

const ApiDistributorShipmentsSchema = v.object({
  DistributorID: v.number(),
  JobName: v.string(),
  OrderNumber: v.number(),
  JobNumber: v.string(),
  Date: v.string(),
  ProNumber: v.string(),
  Pieces: v.number(),
  Weight: v.number(),
  ShipVia: v.string(),
  Rate: v.number(),
  PurchaseNumber: v.string(),
})
const ApiDistributorShipmentsArraySchema = v.object({
  shipments: v.array(ApiDistributorShipmentsSchema),
})

export type DistributorShipmentsRow = v.InferOutput<typeof ApiDistributorShipmentsSchema>

export default defineEventHandler(async (event) => {
  const logPrefix = '[API /distributors/shipments]'

  try {
    const apiFetch = getApiClient()
    const distributorId = getRouterParam(event, 'id')

    if (!distributorId) {
      throw createError({
        statusCode: 400,
        statusMessage: `${logPrefix} Missing 'distributorId' router param.`,
      })
    }

    const data = await apiFetch(`/distributors/${distributorId}/shipments`)

    const { shipments } = v.parse(ApiDistributorShipmentsArraySchema, data)
    return shipments
  }
  catch (error) {
    console.error(`${logPrefix} Handler error`, error)

    throw createError({
      statusCode: 500,
      statusMessage: `${logPrefix} Unexpected server error.`,
      data: error,
    })
  }
})
