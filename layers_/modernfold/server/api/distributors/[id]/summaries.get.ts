// api/v1/tcm/distributor/{{distributorid}}/summaries
import * as v from 'valibot'

const OrderSummarySchema = v.object({
  'Dist': v.string(),
  'Status': v.string(),
  'Gross Sales': v.number(),
  'Net Sales': v.number(),
  'DiscountPer': v.number(),
})

const ApiDistributorSummariesSchema = v.object({
  orderSummaries: v.array(OrderSummarySchema),
})

export type DistributorSummariesResponse = v.InferOutput<typeof ApiDistributorSummariesSchema>

export default defineEventHandler(async (event) => {
  const logPrefix = '[API /distributors/id/summaries]'

  try {
    const distributorId = getRouterParam(event, 'id')

    if (!distributorId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing distributorId router param.',
      })
    }
    const apiFetch = getApiClient()

    const data = await apiFetch(`/tcm/distributor/${distributorId}/summaries`)

    const parsedData = v.parse(ApiDistributorSummariesSchema, data)

    return parsedData
  }
  catch (error) {
    console.error(`${logPrefix} Handler error`, error)

    throw createError({
      statusCode: 500,
      statusMessage: `${logPrefix} Unexpected server error.`,
    })
  }
})
