import * as v from 'valibot'
import { getApiClient } from '@modernfold/server/utils/api'
import { approvedCoveringsSchema } from '@modernfold/types/com'

export const schema = v.object({
  approvedCoverings: v.array(approvedCoveringsSchema),
})

export type ApiRequesApprovedCoverings = v.InferOutput<typeof schema>

export default defineEventHandler(async () => {
  const logPrefix = '[API /com/approvedCoverings]'

  try {
    const apiFetch = getApiClient()

    const rawData = await apiFetch<ApiRequesApprovedCoverings>(`/com/approvedCoverings`)

    const { approvedCoverings } = v.parse(schema, rawData)

    return approvedCoverings
  }
  catch (error) {
    throw createError({
      statusCode: 500,
      statusMessage: `${logPrefix} Error fetching or processing com requests`,
      data: error,
    })
  }
})
