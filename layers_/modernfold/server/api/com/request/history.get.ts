import * as v from 'valibot'
import { getApiClient } from '@modernfold/server/utils/api'

const COMRequestHistorySchema = v.object({
  ComID: v.number(),
  EventID: v.number(),
  Name: v.string(),
  Notes: v.nullable(v.string()),
  UserID: v.string(),
  SystemTS: v.string(),
})

const ApiCOMRequestHistorySchema = v.object({
  history: v.array(COMRequestHistorySchema),
})

const QuerySchema = v.object({
  comid: v.pipe(
    v.string(),
    v.digits('The string contains something other than digits.'),
  ),
})

export type ApiRequestHistory = v.InferOutput<typeof ApiCOMRequestHistorySchema>

export default defineEventHandler(async (event) => {
  const query = await getValidatedQuery(event, query => v.parse(QuerySchema, query))
  const apiFetch = getApiClient()

  const rawData = await apiFetch<ApiRequestHistory>(`/com/request/${query.comid}/history`)

  const validatedRawRequest = v.parse(ApiCOMRequestHistorySchema, rawData)

  return validatedRawRequest
})
