import * as v from 'valibot'

const ApiCOMRequestsSchema = v.object({
  ComID: v.number(),
  StatusDesc: v.string(),
  RequestDate: v.string(),
  JobName: v.string(),
  EstShipDate: v.string(),
  Manufacturer: v.string(),
  Line: v.string(),
  Pattern: v.string(),
  Color: v.string(),

  // unused
  // RevisionID: v.number(),
  // Active: v.number(),
  // DistributorID: v.number(),
  // DistributorName: v.string(),
  // SubmittedBy: v.string(),
  // Status: v.number(),
  // Phone: v.string(),
  // Email: v.string(),
  // JobNumber: v.string(),
  // OrderNumber: v.string(),
  // DistributorPO: v.string(),
  // PanelSkin: v.number(),
  // Covering: v.number(),
  // PartNumber: v.string(),
  // Notes: v.string(),
  // UserID: v.string(),
  // SystemTS: v.string(),
})
const ApiCOMRequestsArraySchema = v.object({
  comRequests: v.array(ApiCOMRequestsSchema),
})

export type COMRequestsRow = v.InferOutput<typeof ApiCOMRequestsSchema>

export default defineEventHandler(async (event) => {
  const logPrefix = '[API /com/request]'
  const { distributorId, status } = getQuery(event)

  if (!distributorId) {
    throw createError({
      statusCode: 400,
      statusMessage: `${logPrefix} Missing distributorId`,
    })
  }

  try {
    const apiFetch = getApiClient()

    const rawData = await apiFetch<{ comRequests: COMRequestsRow[] }>(`/com/request`, {
      params: {
        distributorId,
        status: status ?? 'ALL',
      },
    })

    const validatedRawRequests = v.parse(ApiCOMRequestsArraySchema, rawData)
    return validatedRawRequests.comRequests
  }
  catch (error) {
    throw createError({
      statusCode: 500,
      statusMessage: `${logPrefix} Error fetching or processing com requests`,
      data: error,
    })
  }
})
