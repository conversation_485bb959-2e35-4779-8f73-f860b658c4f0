import * as v from 'valibot'
import { getApiClient } from '@modernfold/server/utils/api'
import dayjs from 'dayjs'

const COMRequestItemSchema = v.object({
  StatusDesc: v.string(),
  RequestDate: v.string(),
  DistributorName: v.string(),
  SubmittedBy: v.string(),
  Phone: v.string(),
  JobNumber: v.string(),
  OrderNumber: v.string(),
  DistributorPO: v.string(),
  JobName: v.string(),
  PanelSkinName: v.string(),
  EstShipDate: v.string(),
  CoveringName: v.string(),
  Manufacturer: v.string(),
  Line: v.string(),
  Pattern: v.string(),
  Color: v.string(),
  QuantityReq: v.number(),
  PartNumber: v.string(),
  Notes: v.nullable(v.string()),
})

const ApiCOMRequestSchema = v.object({
  comRequest: v.array(COMRequestItemSchema),
})

const QuerySchema = v.object({
  comid: v.pipe(
    v.string(),
    v.digits('The string contains something other than digits.'),
  ),
})

export type RequestItem = v.InferOutput<typeof COMRequestItemSchema>
export type COMRequest = v.InferOutput<typeof ApiCOMRequestSchema>

// Receipt Schema
const ReceiptItemSchema = v.object({
  ComID: v.number(),
  TotalReceived: v.number(),
})

const APIReceiptResponseSchema = v.object({
  receipts: v.array(ReceiptItemSchema),
})

export type COMReceipt = v.InferOutput<typeof APIReceiptResponseSchema>

const formatData = (value: string) => dayjs(value).format('MM/DD/YYYY')

export default defineEventHandler(async (event) => {
  const query = await getValidatedQuery(event, query => v.parse(QuerySchema, query))
  const apiFetch = getApiClient()

  // Request
  const requestData = await apiFetch(`/com/request/${query.comid}`)
  const validatedRequest = v.parse(ApiCOMRequestSchema, requestData)

  // Receipt
  const receiptData = await apiFetch(`/com/receipt/${query.comid}`)
  const validatedReceipts = v.parse(APIReceiptResponseSchema, receiptData)

  // Combine request and receipt data
  const combinedData = {
    comRequest: validatedRequest.comRequest.map((item: RequestItem) => ({
      StatusDesc: item.StatusDesc,
      RequestDate: formatData(item.RequestDate),
      DistributorName: item.DistributorName,
      SubmittedBy: item.SubmittedBy,
      Phone: item.Phone,
      JobNumber: item.JobNumber,
      OrderNumber: item.OrderNumber,
      DistributorPO: item.DistributorPO,
      JobName: item.JobName,
      PanelSkinName: item.PanelSkinName,
      EstShipDate: formatData(item.EstShipDate),
      CoveringName: item.CoveringName,
      Manufacturer: item.Manufacturer,
      Line: item.Line,
      Pattern: item.Pattern,
      Color: item.Color,
      QuantityReq: item.QuantityReq,
      TotalReceived: validatedReceipts.receipts[0]?.TotalReceived ?? 0,
      PartNumber: item.PartNumber,
      Notes: item.Notes ?? 'N/A',
    })),
  }

  return combinedData
})
