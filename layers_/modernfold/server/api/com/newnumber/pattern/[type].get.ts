import * as v from 'valibot'

const OptionSchema = v.object({
  patterns: v.array(
    v.object({ PatternName: v.string() }),
  ),
})

export default defineEventHandler(async (event) => {
  const loggerPrefix = '[API /pattern/manufacturers]'

  try {
    const apiFetch = getApiClient()
    const type = getRouterParam(event, 'type')

    if (!type) {
      throw createError({
        statusCode: 400,
        statusMessage: `${loggerPrefix} Missing 'manufacturerID' router param.`,
      })
    }

    const data = await apiFetch(`/com/pattern/manufacturer/${type}`)

    const { patterns } = v.parse(OptionSchema, data)

    return patterns
  }
  catch (error) {
    console.error(`${loggerPrefix} Handler error`, error)

    throw createError({
      statusCode: 500,
      statusMessage: `${loggerPrefix} Unexpected server error.`,
    })
  }
})
