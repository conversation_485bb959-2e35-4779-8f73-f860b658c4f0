import * as v from 'valibot'

const OptionSchema = v.object({
  manufacturers: v.array(
    v.object({ AppManfID: v.number(), Manufacturer: v.string() }),
  ),
})

export default defineEventHandler(async (event) => {
  const loggerPrefix = '[API /samples/manufacturers]'

  try {
    const apiFetch = getApiClient()
    const type = getRouterParam(event, 'type')

    if (!type) {
      throw createError({
        statusCode: 400,
        statusMessage: `${loggerPrefix} Missing 'sampleVendorId' router param.`,
      })
    }

    const data = await apiFetch(`/com/manufacturers/${type}`)

    const { manufacturers } = v.parse(OptionSchema, data)

    return manufacturers
  }
  catch (error) {
    console.error(`${loggerPrefix} Handler error`, error)

    throw createError({
      statusCode: 500,
      statusMessage: `${loggerPrefix} Unexpected server error.`,
    })
  }
})
