import * as v from 'valibot'

const OptionSchema = v.object({
  panelSkins: v.array(v.object({ Type: v.number(), Name: v.string() })),
})

export default defineEventHandler(async () => {
  const loggerPrefix = '[API /COM/panelskin]'

  try {
    const apiFetch = getApiClient()

    const data = await apiFetch(`/com/panelskin`)

    const { panelSkins } = v.parse(OptionSchema, data)

    return panelSkins
  }
  catch (error) {
    console.error(`${loggerPrefix} Handler error`, error)

    throw createError({
      statusCode: 500,
      statusMessage: `${loggerPrefix} Unexpected server error.`,
    })
  }
})
