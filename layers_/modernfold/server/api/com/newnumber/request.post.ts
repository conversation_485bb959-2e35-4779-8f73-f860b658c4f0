import * as v from 'valibot'
import { COMNewNumberBodySchema, ComNewNumberResponseSchema } from '@modernfold/server/types/comNewNumber'

export default defineEventHandler(async (event) => {
  const loggerPrefix = '[API /com/request]'
  try {
    const body = await readValidatedBody(event, body => v.parse(COMNewNumberBodySchema, body))

    const apiFetch = getApiClient()
    const data = await apiFetch('/com/request', {
      method: 'POST',
      body,
    })

    const newNumberResponse = v.parse(ComNewNumberResponseSchema, data)
    return newNumberResponse
  }
  catch (error) {
    if (error instanceof Error) {
      const maybeWithData = error as Error & { data?: { Message?: string } }

      console.error(`${loggerPrefix} Handler error`, error)

      throw createError({
        statusCode: 500,
        statusMessage: `${loggerPrefix} Unexpected server error`,
        message: maybeWithData.data?.Message ?? '',
      })
    }
  }
})
