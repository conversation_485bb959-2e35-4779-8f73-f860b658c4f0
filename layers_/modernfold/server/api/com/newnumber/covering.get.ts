import * as v from 'valibot'

const OptionSchema = v.object({
  coverings: v.array(v.object({ Type: v.number(), Name: v.string() })),
})

export default defineEventHandler(async () => {
  const loggerPrefix = '[API /COM/covering]'

  try {
    const apiFetch = getApiClient()

    const data = await apiFetch(`/com/covering`)

    const { coverings } = v.parse(OptionSchema, data)

    return coverings
  }
  catch (error) {
    console.error(`${loggerPrefix} Handler error`, error)

    throw createError({
      statusCode: 500,
      statusMessage: `${loggerPrefix} Unexpected server error.`,
    })
  }
})
