import * as v from 'valibot'
import { AcoustiSealCalculatorBodySchema, AcoustiSealCalculatorResponseSchema } from '@modernfold/server/types/comCalculator'

export default defineEventHandler(async (event) => {
  const loggerPrefix = '[API /com/calculator/acousti]'
  try {
    const body = await readValidatedBody(event, body => v.parse(AcoustiSealCalculatorBodySchema, body))

    const apiFetch = getApiClient()
    const data = await apiFetch('/com/calculator/acousti', {
      method: 'POST',
      body,
    })

    const response = v.parse(AcoustiSealCalculatorResponseSchema, data)
    return response
  }
  catch (error) {
    if (error instanceof Error) {
      const maybeWithData = error as Error & { data?: { Message?: string } }
      console.error(`${loggerPrefix} Handler error`, error)
      throw createError({
        statusCode: 500,
        statusMessage: `${loggerPrefix} Unexpected server error`,
        message: maybeWithData.data?.Message ?? '',
      })
    }
  }
})
