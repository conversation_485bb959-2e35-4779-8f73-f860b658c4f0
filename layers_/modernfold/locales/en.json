{"forms": {"shared": {"actions": {"edit": "Edit"}, "cta": {"submit": "Submit"}}, "sampleOrder": {"shippingInformation": {"title": "General data", "fullname": {"label": "Full name", "placeholder": "Name and surname"}, "company": {"label": "Company", "placeholder": "Insert company name"}, "address": {"label": "Address", "placeholder": "Street address"}, "city": {"label": "City", "placeholder": "City or Town"}, "state": {"label": "State", "placeholder": "State"}, "postal": {"label": "Postal code", "placeholder": "Postal code"}, "country": {"label": "Country", "placeholder": "Select country", "errors": {"required": "Please select a country"}}, "prefix": {"label": "Prefix", "placeholder": "0"}, "number": {"label": "Number", "placeholder": "111-222-3333"}, "email": {"label": "Email", "placeholder": "Email"}, "po": {"label": "PO number"}, "special": {"label": "Special shipping instruction", "placeholder": "Notes and comments"}}, "shippingMethod": {"title": "Shipping method", "description": {"text": "Shipping information:", "list": ["<b>Non RUSH</b> orders are shipped via FedEx Ground (approx. 4-7 days) - no charge", "If <b>overnight rush</b> delivery is selected, your <b>shipping account number must be provided and you will be responsible for delivery charges.</b>", "NOTE: <b>Rush orders must be submitted before 2PM EST</b> in order to be shipped same day, otherwise the order will be shipped the next work day."]}, "information": {"label": "Shipping information", "errors": {"required": "Please select a shipping method"}, "help": {"account#Required": "Acct # required", "account#NotRequired": "No charge"}}, "carrier": {"label": "Carrier", "placeholder": "Select a carrier", "errors": {"required": "Please select a carrier"}}, "acct": {"label": "Acct #", "placeholder": "Acct"}}, "orderQuantity": {"chargedToYou": "Charged to you", "part": "Part#", "maxQuantity": "Max. Quantity", "description": "Description", "loadMore": "Load More ({quantity})", "quantity": "Quantity"}}, "customerSupportTroubleReport": {"orders": {"label": "Select by Order#", "placeholder": "Select something"}, "names": {"label": "Select by Job Names#", "placeholder": "Select something"}, "numbers": {"label": "Select by Job#", "placeholder": "Select something"}, "cta": {"label": "Go"}}, "comNewNumber": {"date": {"label": "Date"}, "distributor": {"label": "Distributor", "error": "An error occurred and the distributor could not be loaded"}, "submittedBy": {"label": "Submitted by", "error": "An error occurred and the user could not be loaded"}, "phone": {"label": "Phone", "error": "An error occurred and the user could not be loaded"}, "jobNumber": {"label": "Job number"}, "orderNumber": {"label": "Order number"}, "po": {"label": "Distributor PO#"}, "jobName": {"label": "Job name"}, "panelSkin": {"label": "Panel skin"}, "estimatedShipDate": {"label": "Est. Ship Date"}, "covering": {"label": "Covering"}, "manufacturer": {"label": "Manufacturer"}, "pattern": {"label": "Pattern"}, "color": {"label": "Color"}, "line": {"label": "Line"}, "quantity": {"label": "Distributor Calculated Quantity (in yds)"}, "partNumber": {"label": "Part Number:"}, "notes": {"label": "Notes", "placeholder": "Notes and comments"}}}, "modernfold": {"index": {"title": "Modernfold"}, "multipleDistributors": {"title": "Multiple distributors access", "description": "Your account has access to multiple distributors. Please select the distributor information you wish to access from the list below. You will be able to switch accounts anytime by using the list located in the menu.", "selectDistributor": "Select a distributor", "go": "Go"}}, "footer": {"mini": {"copyrightStatement": "© Modernfold Inc. {year}. All rights reserved. No reproduction allowed without the consent of Modernfold Inc."}}, "assets": {"backTo": "Back to", "searchPlaceholder": "Search in this folder"}, "ordersAcknowledgments": {"form": {"purchaseOrderNumber": "Purchase order number", "search": "Search", "resetFilters": "Reset Filters"}, "table": {"headers": {"orderNumber": "Order #", "poNumber": "PO #", "jobName": "Job Name", "distributorAnticipatedShipDate": "Distributor Anticipated Ship Date", "dateOrder": "Date Ordered", "dateSchedule": "Date Scheduled", "job": "Job", "status": "Status", "gross": "Gross", "net": "Net", "discount": "Discount", "units": "Units", "orderType": "Order Type", "reviseOrderLink": "Revise Order", "discountPer": "Discount"}}, "statusSummary": "Status Summary", "realTime": "All data in realtime"}, "com": {"backTo": "Back to", "goBack": "Go back", "requests": {"seeMoreItems": "See 10 more ({totalItems})", "seeLess": "See less", "backTo": "Back to COM", "filterLabel": "Filter status", "filterOptions": {"all": "All", "approved": "Approved", "denied": "Denied", "received": "Received", "submitted": "Submitted"}, "tableHeader": {"comId": "COM#", "status": "Status", "requestDate": "Request date", "jobName": "Job name", "estShipDate": "Est. Ship Date", "manufacter": "Manufacter", "line": "Line", "pattern": "Pattern", "color": "Color"}}, "request": {"edit": "Edit", "StatusDesc": "Status Description", "RequestDate": "Request Date", "DistributorName": "Distributor Name", "SubmittedBy": "Submitted By", "Phone": "Phone", "JobNumber": "Job Number", "OrderNumber": "Order Number", "DistributorPO": "Distributor PO", "JobName": "Job Name", "PanelSkinName": "Panel Skin Name", "EstShipDate": "Estimated Shipping Date", "CoveringName": "Covering Name", "Manufacturer": "Manufacturer", "Line": "Line", "Pattern": "Pattern", "Color": "Color", "QuantityReq": "Quantity Requested", "TotalReceived": "Quantity Of Material Received To Date", "PartNumber": "Part Number", "Notes": "Notes", "history": {"title": "History", "Name": "Event", "Notes": "Comments", "UserID": "User", "SystemTs": "Date"}, "submit": {"successMessage": "COM new number {comId} submitted successfully", "errorMessage": "Failed to submit COM new number"}, "errors": {"detail": "Error loading detail", "history": "Error loading history"}}, "acoustiSealCalculator": {"title": "Acousti-Seal COM Calculator", "numberOfSides": "Number of sides", "yardsRequired": "Yards of special covering required", "fields": {"height": "Fabrication Height (in feet)", "heightPlaceholder": "Enter height in feet", "numPanels": "Number of Panels", "numPanelsPlaceholder": "Enter number of panels", "coveringSides": "Covering Sides (1 or 2)", "coveringSidesPlaceholder": "Select number of sides", "numExpClosures": "Number of Expandable Closures", "numExpClosuresPlaceholder": "Enter number of expandable closures", "numMatchingPassDoors": "Number of Matching Pass Doors", "numMatchingPassDoorsPlaceholder": "Enter number of matching pass doors", "numTypeIPocketDoors": "Number of Type I Pocket Doors", "numTypeIPocketDoorsPlaceholder": "Enter number of Type I pocket doors", "numTypeIVPocketDoors": "Number of Type II, III, IV Pocket Doors", "numTypeIVPocketDoorsPlaceholder": "Enter number of Type II, III, IV pocket doors"}, "cta": "Calculate", "reset": "Reset", "results": {"numSides": "Number of sides", "yardsNeeded": "Yards of special covering required"}, "success": "Calculation completed successfully!", "error": "An error occurred and the calculation could not be completed"}}, "accountManager": {"shipments": {"tableHeader": {"orderNumber": "Order#", "shipDate": "Ship Date", "jobNumber": "Job#", "jobName": "Job Name", "purchaseNumber": "PO#", "carrier": "Carrier", "tracking": "Tracking#", "weight": "Weight", "pieces": "Pieces", "shippingCharges": "Shipping Charges", "product": "Product", "booking": "Booking", "trackSwitches": "Track & Switches", "status": "Status"}, "errors": {"trackleadtime": "Error loading track leadtime data", "leadtime": "Error loading leadtime data"}}, "invoices": {"form": {"purchaseOrderNumber": "Purchase Order Number", "beginDate": "Begin date (Ship date)", "endDate": "End date (Ship date)", "jobNumber": "Job number", "jobName": "Job Name", "invoiceNumber": "Invoice number", "search": "Search", "resetFilters": "Reset Filters"}, "table": {"headers": {"invoiceNumber": "Invoice number", "invoiceDate": "Invoice Date", "poNumber": "Po Number", "orderNumber": "Order Number", "jobNumber": "Job Number", "jobName": "Job Name", "totalUsd": "Total (USD)"}}, "realTime": "All data in in real time"}}}