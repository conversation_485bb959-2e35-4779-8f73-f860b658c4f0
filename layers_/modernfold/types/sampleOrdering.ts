import type { SampleProductRow as ApiSampleProductRow } from '@modernfold/server/api/samples/items/[sampleVendorId].get'

export interface SampleProductRow extends ApiSampleProductRow {
  id: string
}

export type SampleCartItem = {
  EMSPart: string
  Item: string
  Quantity: number
}

export type SampleOrderingStepId = 'shipping-information' | 'shipping-method' | 'quantity' | 'confirm'

export type SampleOrderingFormData = {
  Items?: SampleCartItem[]
  __phoneNumber?: {
    prefix: string
    number: string
  }
  [key: string]: unknown
}
