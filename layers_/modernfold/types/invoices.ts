interface BaseInvoiceFields {
  invoiceNumber: string
  invoiceDate: string
  poNumber: string
  orderNumber: string
  jobNumber: string
  jobName: string
  totalUsd: string
}
export interface InvoiceRow extends BaseInvoiceFields {
  id: string
}

type FilterableInvoiceFields = Pick<BaseInvoiceFields, 'invoiceNumber' | 'jobNumber' | 'jobName'>

export interface InvoiceFilters extends FilterableInvoiceFields {
  purchaseOrderNumber: string
  beginDate?: string
  endDate?: string
  [key: string]: string | undefined
}
