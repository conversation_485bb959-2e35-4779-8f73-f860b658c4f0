import * as v from 'valibot'

export const approvedCoveringsSchema = v.object({
  ApprovedID: v.number(),
  Type: v.number(),
  Manufacturer: v.number(),
  PatternName: v.string(),
  PatternNumber: v.string(),
  Color: v.string(),
  Effdate: v.string(),
  ExpDate: v.string(),
  Thickness: v.nullable(v.number()),
  CoveringType: v.string(),
  ManufacturerName: v.string(),
})

export type ApprovedCovering = v.InferOutput<typeof approvedCoveringsSchema>
export type ApprovedCoveringRow = Partial<ApprovedCovering> & { id: number }
