// https://nuxt.com/docs/api/configuration/nuxt-config
import { fileURLToPath } from 'url'
import { env, sites } from './env'

export default defineNuxtConfig({
  extends: [`./layers_/${env.SITE}`],
  modules: ['@nuxt/eslint', '@nuxthub/core', '@atoms-studio/nuxt-swiftsearch', '@vueuse/nuxt', '@nuxt/image', 'dayjs-nuxt'],
  devtools: { enabled: true },
  app: {
    head: {
      link: ['regular', '600', '700'].map(w => ({
        rel: 'preload', href: `/fonts/host-grotesk-v4-latin-${w}.woff2`, as: 'font', type: 'font/woff2', crossorigin: 'anonymous',
      })),
    },
  },
  runtimeConfig: {
    api: {
      baseUrl: env.API_BASE_URL,
      xApiKey: env.API_KEY,
    },
    sendgrid: {
      apiKey: env.SENDGRID_API_KEY,
    },
    public: {
      site: env.SITE,
      algolia: {
        apiKey: process.env.ALGOLIA_API_KEY,
        applicationId: process.env.ALGOLIA_APP_ID,
      },
    },
    storybookBuild: process.env.STORYBOOK_BUILD === 'true',
  },
  alias: {
    '@design-system': fileURLToPath(new URL('./layers_/design-system', import.meta.url)),
    '@root': fileURLToPath(new URL('./', import.meta.url)),
  },
  future: {
    compatibilityVersion: 4,
  },
  compatibilityDate: '2024-11-01',
  typescript: {
    strict: true,
    tsConfig: {
      compilerOptions: {
        types: ['vitest/globals'],
      },
      exclude: sites
        .filter(site => site !== env.SITE)
        .map(site => fileURLToPath(new URL(`./layers_/${site}`, import.meta.url))),
    },
  },
  eslint: {
    config: {
      stylistic: true,
    },
  },
  image: {
    quality: 75,
    format: ['webp', 'jpeg'],
    screens: {
      xs: 320,
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
      xxl: 1536,
    },
  },
  storybook: {
    enabled: false,
  },
})
