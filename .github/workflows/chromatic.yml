name: "Chromatic"

on:
  push:
    branches:
      - main

  pull_request:
    branches:
      - main

  # Trigger the workflow manually
  workflow_dispatch:

jobs:
  chromatic:
    runs-on: ubuntu-latest

    env:
      CHROMATIC_PROJECT_TOKEN: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
      STORYBOOK_BUILD: true
      SITE: modernfold
      API_BASE_URL: ${{ secrets.API_BASE_URL }}
      API_KEY: ${{ secrets.API_KEY }}
      FORMKIT_PRO_KEY: ${{ secrets.FORMKIT_PRO_KEY }}
      STORYBLOK_ACCESS_TOKEN: ${{ secrets.STORYBLOK_ACCESS_TOKEN }}
      STORYBLOK_MANAGMENT_API_ACCESS_TOKEN: ${{ secrets.STORYBLOK_MANAGMENT_API_ACCESS_TOKEN }}
      STORYBLOK_SPACE_ID: 326620
      NUXT_SESSION_PASSWORD: ${{ secrets.NUXT_SESSION_PASSWORD }}
      AUTH0_CLIENT_ID: ${{ secrets.AUTH0_CLIENT_ID }}
      AUTH0_CLIENT_SECRET: ${{ secrets.AUTH0_CLIENT_SECRET }}
      AUTH0_DOMAIN: ${{ secrets.AUTH0_DOMAIN }}
      AUTH0_AUDIENCE: ${{ secrets.AUTH0_AUDIENCE }}
      BASE_URL: ${{ secrets.BASE_URL }}
      SENDGRID_API_KEY: ${{ secrets.SENDGRID_API_KEY }}
      AIA_FORM_MAIL_TO: ${{ secrets.AIA_FORM_MAIL_TO }}
      AIA_FORM_SENDGRID_TEMPLATE_ID: ${{ secrets.AIA_FORM_SENDGRID_TEMPLATE_ID }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🥟 Set up Bun environment
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: 1.2.2

      - name: 🔧 Setup bun cache
        uses: actions/cache@v4
        with:
          path: ~/.bun
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lock') }}
          restore-keys: |
            ${{ runner.os }}-bun-

      - name: 📦 Cache node modules
        id: cache-node-modules
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-node-modules-${{ hashFiles('**/bun.lock') }}
          restore-keys: |
            ${{ runner.os }}-node-modules-

      - name: ⬇️ Install dependencies
        run: |
          if [ "${{ steps.cache-node-modules.outputs.cache-hit }}" != "true" ]; then
            echo "⚠️ Dependencies are not cached, installing..."
            bun i --frozen-lockfile
          else
            echo "✅ Dependencies are already cached"
            bun run postinstall
          fi

      - name: Run Chromatic
        uses: chromaui/action@latest
        with:
          projectToken: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
          exitOnceUploaded: true
