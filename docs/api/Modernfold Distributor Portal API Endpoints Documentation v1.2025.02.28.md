# Modernfold Distributor Portal Endpoints Documentation v1.2025.02.28

## Table of Contents

1. [ComController Endpoints](https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/AppData/Local/Programs/Windsurf/resources/app/extensions/windsurf/dist/media#comcontroller-endpoints)
2. [DistributorsController Endpoints](https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/AppData/Local/Programs/Windsurf/resources/app/extensions/windsurf/dist/media#distributorscontroller-endpoints)
3. [OrdersController Endpoints](https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/AppData/Local/Programs/Windsurf/resources/app/extensions/windsurf/dist/media#orderscontroller-endpoints)
4. [PricingController Endpoints](https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/AppData/Local/Programs/Windsurf/resources/app/extensions/windsurf/dist/media#pricingcontroller-endpoints)
5. [ProductController Endpoints](https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/AppData/Local/Programs/Windsurf/resources/app/extensions/windsurf/dist/media#productcontroller-endpoints)
6. [ProductsController Endpoints](https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/AppData/Local/Programs/Windsurf/resources/app/extensions/windsurf/dist/media#productscontroller-endpoints)
7. [ProjectController Endpoints](https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/AppData/Local/Programs/Windsurf/resources/app/extensions/windsurf/dist/media#projectcontroller-endpoints)
8. [TCMController Endpoints](https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/AppData/Local/Programs/Windsurf/resources/app/extensions/windsurf/dist/media#tcmcontroller-endpoints)
9. [UsersController Endpoints](https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/AppData/Local/Programs/Windsurf/resources/app/extensions/windsurf/dist/media#userscontroller-endpoints)
10. [WarrantyController Endpoints](https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/AppData/Local/Programs/Windsurf/resources/app/extensions/windsurf/dist/media#warrantycontroller-endpoints)

---

## ComController Endpoints

### GET api/v1/com/accordion-calculator

Calculates accordion covering requirements based on provided parameters.

**Request Parameters:**

- `width` (string, required): Width of the accordion
- `modelId` (integer, required): Model ID
- `materialType` (string, required): Material type

**Response:**

json



`{   "calculationResult": [     {       // DataTable result from stored procedure     }   ],   "metadata": {     "width": "string",     "modelId": 0,     "materialType": "string"   } }`

**Error Responses:**

- 400 Bad Request: If required parameters are missing
- 404 Not Found: If no calculation results are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/com/acousti-calculator

Calculates acoustical covering requirements based on provided parameters.

**Request Parameters:**

- `numPanels` (integer, required): Number of panels
- `height` (double, required): Height
- `coveringSides` (integer, required): Number of sides to be covered
- `typeIPocketDoors` (integer, optional): Number of Type I pocket doors
- `typeIVPocketDoors` (integer, optional): Number of Type IV pocket doors
- `expClosures` (integer, optional): Number of expandable closures
- `matchingPassDoors` (integer, optional): Number of matching pass doors
- `oversizedJambs` (integer, optional): Number of oversized jambs
- `panelsUnder24` (integer, optional): Number of panels under 24"

**Response:**

json



`{   "calculationResult": {     "numberOfSides": 0,     "yardsNeeded": 0.0,     "exactYardageRequired": 0.0,     "extraYardageRequired": 0.0   },   "metadata": {     "numPanels": 0,     "height": 0.0,     "coveringSides": 0,     "typeIPocketDoors": 0,     "typeIVPocketDoors": 0,     "expClosures": 0,     "matchingPassDoors": 0,     "oversizedJambs": 0,     "panelsUnder24": 0   } }`

**Error Responses:**

- 400 Bad Request: If required parameters are missing
- 500 Internal Server Error: If an exception occurs

---

## DistributorsController Endpoints

### GET api/v1/distributors/{distributorId}/shipments

Retrieves shipment information for a specific distributor.

**Request Parameters:**

- `distributorId` (integer, required): The ID of the distributor

**Response:**

json



`{   "shipments": [     {       // DataTable result from stored procedure spvwOrderShipments_Select     }   ] }`

**Error Responses:**

- 400 Bad Request: If distributorId is invalid
- 404 Not Found: If no shipments are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/distributors/{distributorId}/projects/{projectId}/files

Retrieves files associated with a specific project for a distributor.

**Request Parameters:**

- `distributorId` (integer, required): The ID of the distributor
- `projectId` (integer, required): The ID of the project

**Response:**

json



`{   "projectfiles": [     {       "FileName": "string",       "SecureUrl": "string",       "FileExtension": "string",       "DirectoryPath": "string"     }   ] }`

**Error Responses:**

- 400 Bad Request: If distributorId or projectId is invalid
- 404 Not Found: If no files are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/distributors/{distributorId}/orders/schedules

Retrieves scheduled orders for a specific distributor.

**Request Parameters:**

- `distributorId` (integer, required): The ID of the distributor

**Response:**

json



`{   "orderSchedules": [     {       // DataTable result from stored procedure spGetScheduledOrders_Select     }   ] }`

**Error Responses:**

- 400 Bad Request: If distributorId is invalid
- 404 Not Found: If no scheduled orders are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/distributors/{distributorId}

Retrieves details for a specific distributor.

**Request Parameters:**

- `distributorId` (integer, required): The ID of the distributor

**Response:**

json



`{   "distributors": [     {       // DataTable result from stored procedure spTblDistributors_Select_ByDistributorID     }   ] }`

**Error Responses:**

- 400 Bad Request: If distributorId is invalid
- 404 Not Found: If no distributor is found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/distributors/countries

Retrieves a list of countries.

**Response:**

json



`{   "countries": [     {       // DataTable result from stored procedure spTblLookupCountries_Select     }   ] }`

**Error Responses:**

- 404 Not Found: If no countries are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/distributors/{distributorId}/contacts

Retrieves contacts for a specific distributor.

**Request Parameters:**

- `distributorId` (integer, required): The ID of the distributor

**Response:**

json



`{   "contacts": [     {       // DataTable result from stored procedure spTblDistributorsContacts_Select     }   ] }`

**Error Responses:**

- 400 Bad Request: If distributorId is invalid
- 404 Not Found: If no contacts are found
- 500 Internal Server Error: If an exception occurs

---

## OrdersController Endpoints

### GET api/v1/orders/{orderId}/samples

Retrieves sample information for a specific order.

**Request Parameters:**

- `orderId` (integer, required): The ID of the order

**Response:**

json



`{   "samples": [     {       // DataTable result from stored procedure spTblSamplesOrders_Select     }   ] }`

**Error Responses:**

- 400 Bad Request: If orderId is invalid
- 404 Not Found: If no samples are found
- 500 Internal Server Error: If an exception occurs

---

## PricingController Endpoints

### GET api/v2/projects/{projectId}/walls/{wallId}/pricing

Retrieves pricing information for a specific wall in a project.

**Request Parameters:**

- `projectId` (integer, required): The ID of the project
- `wallId` (integer, required): The ID of the wall

**Response:**

json



`{   "wallpricing": {     // PricingAcoustiSeal object properties   } }`

**Error Responses:**

- 400 Bad Request: If projectId or wallId is invalid
- 500 Internal Server Error: If an exception occurs

---

## ProductController Endpoints

### GET api/v1/products/{projectId}/walls/{wallId}/acoustiseal

Retrieves AcoustiSeal information for a specific wall in a project.

**Request Parameters:**

- `projectId` (integer, required): The ID of the project
- `wallId` (integer, required): The ID of the wall

**Response:**

json



`{   "acoustiseal": {     // AcoustiSealInfo object properties   } }`

**Error Responses:**

- 400 Bad Request: If projectId or wallId is invalid
- 404 Not Found: If no AcoustiSeal information is found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/products/leadtime

Retrieves lead time information for products.

**Response:**

json



`{   "productsleadtime": [     {       // DataTable result from stored procedure sp_GetProductLeadTime_Select     }   ] }`

**Error Responses:**

- 404 Not Found: If no lead time information is found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/products/trackleadtime

Retrieves lead time information for tracks.

**Response:**

json



`{   "tracksLeadTime": [     {       // DataTable result from stored procedure sp_GetTrackLeadTime_Select     }   ] }`

**Error Responses:**

- 500 Internal Server Error: If an exception occurs

---

## ProductsController Endpoints

### GET api/v1/products/{projectId}/walls/{wallId}/acoustiseal

Retrieves AcoustiSeal information for a specific wall in a project.

**Request Parameters:**

- `projectId` (integer, required): The ID of the project
- `wallId` (integer, required): The ID of the wall

**Response:**

json



`{   "acoustiseal": {     // AcoustiSealInfo object properties   } }`

**Error Responses:**

- 400 Bad Request: If projectId or wallId is invalid
- 404 Not Found: If no AcoustiSeal information is found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/products/leadtime

Retrieves lead time information for products.

**Response:**

json



`{   "productsleadtime": [     {       // DataTable result from stored procedure sp_GetProductLeadTime_Select     }   ] }`

**Error Responses:**

- 404 Not Found: If no lead time information is found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/products/tracksleadtime

Retrieves lead time information for tracks.

**Response:**

json



`{   "tracksLeadTime": [     {       // DataTable result from stored procedure sp_GetTrackLeadTime_Select     }   ] }`

**Error Responses:**

- 500 Internal Server Error: If an exception occurs

---

## ProjectController Endpoints

### GET api/v1/projects/{projectId}

Retrieves information for a specific project.

**Request Parameters:**

- `projectId` (integer, required): The ID of the project

**Response:**

json



`{   "project": {     // ProjectInfo object properties   } }`

**Error Responses:**

- 400 Bad Request: If projectId is invalid
- 404 Not Found: If no project is found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/projects/search

Searches for projects based on various criteria.

**Request Parameters:**

- `username` (string, required): The username
- `distributorid` (integer, required): The ID of the distributor
- `jobNumber` (string, optional): Job number filter
- `projectName` (string, optional): Project name filter
- `projectDescription` (string, optional): Project description filter
- `architect` (string, optional): Architect filter
- `contractor` (string, optional): Contractor filter
- `sort` (integer, optional, default=1): Sort order
- `source` (string, optional): Source filter
- `city` (string, optional): City filter
- `userProjects` (boolean, optional, default=false): Whether to show only user projects
- `glassMode` (boolean, optional, default=false): Whether to show only glass mode projects
- `statusId` (integer, optional, default=0): Status ID filter

**Response:**

json



`{   "projects": [     {       // DataTable result from ProjectInfo.GetProjectSearch     }   ] }`

**Error Responses:**

- 404 Not Found: If no projects are found
- 500 Internal Server Error: If an exception occurs

---

## TCMController Endpoints

### GET api/v1/tcm/distributor/{distributorId}/headers

Retrieves order headers for a specific distributor.

**Request Parameters:**

- `distributorId` (integer, required): The ID of the distributor
- `type` (string, required): Type of headers to retrieve (DIST, PO, or ORDER)

**Response:**

json



`{   "orderHeaders": [     {       // DataTable result from stored procedure spTCMOrderHeader_Select     }   ] }`

**Error Responses:**

- 400 Bad Request: If distributorId is invalid or type is not one of DIST, PO, or ORDER
- 404 Not Found: If no order headers are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/tcm/distributor/{distributorId}/summaries

Retrieves order summaries for a specific distributor.

**Request Parameters:**

- `distributorId` (integer, required): The ID of the distributor

**Response:**

json



`{   "orderSummaries": [     {       // DataTable result from stored procedure spTCMOrderSummary_Select     }   ] }`

**Error Responses:**

- 400 Bad Request: If distributorId is invalid
- 404 Not Found: If no order summaries are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/tcm/distributor/{distributorid}/statements

Retrieves statements for a specific distributor.

**Request Parameters:**

- `distributorid` (integer, required): The ID of the distributor

**Response:**

json



`{   "tcmstatements": [     {       // DataTable result from stored procedure spTCMStatement_NewSelect     }   ] }`

**Error Responses:**

- 400 Bad Request: If distributorid is invalid
- 404 Not Found: If no statements are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/tcm/distributor/{distributorid}/groupings

Retrieves groupings for a specific distributor.

**Request Parameters:**

- `distributorid` (integer, required): The ID of the distributor

**Response:**

json



`{   "groupings": [     {       // DataTable result from stored procedure spTCMNewSelect_sp_GroupingSets     }   ] }`

**Error Responses:**

- 404 Not Found: If no groupings are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/tcm/invoices/{invoiceid}/comments/seqline/{seqline}/seqrec/{seqrec}

Retrieves comments for a specific invoice.

**Request Parameters:**

- `invoiceid` (integer, required): The ID of the invoice
- `seqline` (integer, required): The sequence line
- `seqrec` (integer, required): The sequence record

**Response:**

json



`{   "invoiceComments": [     {       // DataTable result from stored procedure spTCMInvoiceComments_Select     }   ] }`

**Error Responses:**

- 400 Bad Request: If invoiceid is invalid
- 404 Not Found: If no comments are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/tcm/invoices/distributor/{distributorid}/externalrate

Retrieves external rate information for a specific distributor.

**Request Parameters:**

- `distributorid` (integer, required): The ID of the distributor
- `type` (string, required): Type (INV, DIST, or PO)
- `searchfor` (string, optional): Search term

**Response:**

json



`{   "externalRate": [     {       // DataTable result from stored procedure spTCMInvoiceHeaderExternalRate_Select     }   ] }`

**Error Responses:**

- 400 Bad Request: If distributorid is invalid, type is invalid, or searchfor is not numeric for INV type
- 404 Not Found: If no external rate information is found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/tcm/invoice/headers/distributor/{distributorid}

Retrieves invoice headers for a specific distributor.

**Request Parameters:**

- `distributorid` (integer, required): The ID of the distributor
- `type` (string, required): Type (INV, DIST, or PO)
- `searchfor` (string, optional): Search term

**Response:**

json



`{   "invoiceHeader": [     {       // DataTable result from stored procedure spTCMInvoiceHeader_Select     }   ] }`

**Error Responses:**

- 400 Bad Request: If distributorid is invalid, type is invalid, or searchfor is not numeric for INV type
- 404 Not Found: If no invoice headers are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/tcm/invoices/{invoiceid}/options/line/{seqline}

Retrieves options for a specific invoice line.

**Request Parameters:**

- `invoiceid` (integer, required): The ID of the invoice
- `seqline` (integer, required): The sequence line

**Response:**

json



`{   "invoiceOptions": [     {       // DataTable result from stored procedure spTCMInvoiceOption_Select     }   ] }`

**Error Responses:**

- 400 Bad Request: If invoiceid or seqline is invalid
- 404 Not Found: If no options are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/tcm/distributor/{distributorid}/names

Retrieves names for a specific distributor.

**Request Parameters:**

- `distributorid` (integer, required): The ID of the distributor

**Response:**

json



`{   "names": [     {       // DataTable result from stored procedure spTCMNewSelect_sp_Name     }   ] }`

**Error Responses:**

- 404 Not Found: If no names are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/tcm/orders/acknowledgements

Retrieves order acknowledgements.

**Response:**

json



`{   "acknowledgements": [     {       // DataTable result from stored procedure spTCMOrderAcknowledge_Select     }   ] }`

**Error Responses:**

- 404 Not Found: If no acknowledgements are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/tcm/orders/{orderId}/detail

Retrieves details for a specific order.

**Request Parameters:**

- `orderId` (integer, required): The ID of the order

**Response:**

json



`{   "order": [     {       // DataTable result from stored procedure spTCMOrderDetail_Select     }   ] }`

**Error Responses:**

- 400 Bad Request: If orderId is invalid
- 404 Not Found: If no order details are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/tcm/orders/{orderId}/lines/{lineId}

Retrieves a specific line for a specific order.

**Request Parameters:**

- `orderId` (integer, required): The ID of the order
- `lineId` (integer, required): The ID of the line

**Response:**

json



`{   "orderLine": [     {       // DataTable result from stored procedure spTCMOrderDetaiLine_Select     }   ] }`

**Error Responses:**

- 400 Bad Request: If orderId or lineId is invalid
- 404 Not Found: If no order line is found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/tcm/orders/{orderId}/line/{lineId}/options

Retrieves options for a specific order line.

**Request Parameters:**

- `orderId` (integer, required): The ID of the order
- `lineId` (integer, required): The ID of the line

**Response:**

json



`{   "orderOptions": [     {       // DataTable result from stored procedure     }   ] }`

**Error Responses:**

- 400 Bad Request: If orderId is invalid
- 404 Not Found: If no options are found
- 500 Internal Server Error: If an exception occurs

---

## UsersController Endpoints

### GET api/v1/users

Retrieves information for a legacy user.

**Request Parameters:**

- `legacyuser` (string, required): The legacy username (URL encoded)

**Response:**

json



`{   "userDetails": {     // UserDetails object properties   } }`

**Error Responses:**

- 400 Bad Request: If legacyuser is invalid or contains disallowed characters
- 404 Not Found: If no user is found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/users/distributors

Retrieves distributors for a specific user.

**Request Parameters:**

- `username` (string, required): The username (URL encoded)

**Response:**

json



`{   "distributors": [     {       "Id": 0,       "CompanyName": "string",       "DisplayDesc": "string"     }   ] }`

**Error Responses:**

- 400 Bad Request: If username is invalid or contains disallowed characters
- 404 Not Found: If no distributors are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/users/settings

Retrieves settings for a specific user.

**Request Parameters:**

- `username` (string, required): The username (URL encoded)

**Response:**

json



`{   "userSettings": {     // UserSettings object properties   } }`

**Error Responses:**

- 400 Bad Request: If username is invalid or contains disallowed characters
- 500 Internal Server Error: If an exception occurs

---

## WarrantyController Endpoints

### GET api/v1/jobhistory/orders/{distributorId}

Retrieves job history for orders of a specific distributor.

**Request Parameters:**

- `distributorId` (string, required): The ID of the distributor

**Response:**

json



`{   "jobhistory": [     {       // DataTable result from stored procedure spviewJob_History_Select     }   ] }`

**Error Responses:**

- 400 Bad Request: If distributorId is invalid
- 404 Not Found: If no job history is found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/jobhistory/names/{distributorId}

Retrieves job history for names of a specific distributor.

**Request Parameters:**

- `distributorId` (string, required): The ID of the distributor

**Response:**

json



`{   "jobhistory": [     {       // DataTable result from stored procedure spviewJob_History_Select     }   ] }`

**Error Responses:**

- 400 Bad Request: If distributorId is invalid
- 404 Not Found: If no job history is found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/jobhistory/numbers/{distributorId}

Retrieves job history for numbers of a specific distributor.

**Request Parameters:**

- `distributorId` (string, required): The ID of the distributor

**Response:**

json



`{   "jobhistory": [     {       // DataTable result from stored procedure spviewJob_History_Select     }   ] }`

**Error Responses:**

- 400 Bad Request: If distributorId is invalid
- 404 Not Found: If no job history is found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/warranty/{distributorId}/troublereports/all

Retrieves all trouble reports for a specific distributor.

**Request Parameters:**

- `distributorId` (integer, required): The ID of the distributor

**Response:**

json



`{   "troubleReports": [     {       // DataTable result from stored procedure spWarrantyAllTRByDistributor     }   ] }`

**Error Responses:**

- 400 Bad Request: If distributorId is invalid
- 404 Not Found: If no trouble reports are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/warranty/{distributorId}/troublereports/closed

Retrieves closed trouble reports for a specific distributor.

**Request Parameters:**

- `distributorId` (integer, required): The ID of the distributor

**Response:**

json



`{   "troubleReports": [     {       // DataTable result from stored procedure spWarrantyClosedTRByDistributor     }   ] }`

**Error Responses:**

- 400 Bad Request: If distributorId is invalid
- 404 Not Found: If no trouble reports are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/warranty/{distributorId}/troublereports/open

Retrieves open trouble reports for a specific distributor.

**Request Parameters:**

- `distributorId` (integer, required): The ID of the distributor

**Response:**

json



`{   "troubleReports": [     {       // DataTable result from stored procedure spWarrantyOpenTRByDistributor     }   ] }`

**Error Responses:**

- 400 Bad Request: If distributorId is invalid
- 404 Not Found: If no trouble reports are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/warranty/labor

Retrieves labor types for warranty.

**Response:**

json



`{   "labortypes": [     {       "id": "string",       "value": "string"     }   ] }`

### GET api/v1/warranty/materials

Retrieves material types for warranty.

**Response:**

json



`{   "materialtypes": [     {       "id": "string",       "value": "string"     }   ] }`

### GET api/v1/warranty/models

Retrieves models for warranty.

**Response:**

json



`{   "models": [     {       // DataTable result from stored procedure sptblModelInfo_Select     }   ] }`

**Error Responses:**

- 404 Not Found: If no models are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/warranty/requests/{requestId}

Retrieves a specific warranty request.

**Request Parameters:**

- `requestId` (string, required): The ID of the request

**Response:**

json



`{   "warrantyrequestdetail": [     {       // DataTable result from stored procedure sptblWarrantyRequest_Select_ByRequestID     }   ] }`

**Error Responses:**

- 400 Bad Request: If requestId is invalid
- 404 Not Found: If no warranty request is found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/warranty/histories/{requestId}

Retrieves history for a specific warranty request.

**Request Parameters:**

- `requestId` (string, required): The ID of the request

**Response:**

json



`{   "warrantyHistory": [     {       // DataTable result from stored procedure sptblWarrantyRequestHistory_Select_2020     }   ] }`

**Error Responses:**

- 400 Bad Request: If requestId is invalid
- 404 Not Found: If no warranty history is found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/warranty/status

Retrieves status types for warranty.

**Response:**

json



`{   "statustypes": [     {       "id": "string",       "value": "string"     }   ] }`

### GET api/v1/warranty/tracks

Retrieves tracks for warranty.

**Response:**

json



`{   "tracks": [     {       // DataTable result from stored procedure sptblModelTrack_Select     }   ] }`

**Error Responses:**

- 404 Not Found: If no tracks are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/warranty/backcharges/{requestId}/maxchargeid

Retrieves the maximum charge ID for a specific warranty request.

**Request Parameters:**

- `requestId` (string, required): The ID of the request

**Response:**

json



`{   "maxChargeId": 0 }`

**Error Responses:**

- 400 Bad Request: If requestId is invalid
- 500 Internal Server Error: If an exception occurs

### GET api/v1/warranty/backcharges/{requestId}/freight/{chargeId}

Retrieves freight details for a specific backcharge.

**Request Parameters:**

- `requestId` (string, required): The ID of the request
- `chargeId` (integer, required): The ID of the charge

**Response:**

json



`{   "freightDetails": [     {       // DataTable result from stored procedure     }   ] }`

**Error Responses:**

- 400 Bad Request: If requestId or chargeId is invalid
- 404 Not Found: If no freight details are found
- 500 Internal Server Error: If an exception occurs

### GET api/v1/warranty/backcharges/{requestId}/miscellaneous/{chargeId}

Retrieves miscellaneous details for a specific backcharge.

**Request Parameters:**

- `requestId` (string, required): The ID of the request
- `chargeId` (integer, required): The ID of the charge

**Response:**

json



`{   "miscellaneousDetails": [     {       // DataTable result from stored procedure     }   ] }`

**Error Responses:**

- 400 Bad Request: If requestId or chargeId is invalid
- 404 Not Found: If no miscellaneous details are found
- 500 Internal Server Error: If an exception occurs

DoneFeedback has b