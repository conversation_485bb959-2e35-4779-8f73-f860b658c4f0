<script setup lang='ts'>
const config = useRuntimeConfig()
const site = config.public.site
</script>

<template>
  <div
    class="min-h-screen relative"
  >
    <NuxtImg
      :src="`/images/${site}-hp-background.jpg`"
      :alt="`${site} homepage background`"
      class="absolute inset-0 w-full h-full object-cover object-center -z-1"
      loading="eager"
    />

    <SiteOrganismsHeader :anatomy="'transparent'" hide-search />
    <div class="min-h-page flex flex-col">
      <slot />
    </div>
    <OrganismsFooter />
  </div>
</template>
