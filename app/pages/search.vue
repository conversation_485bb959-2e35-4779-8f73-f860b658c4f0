<script lang="ts" setup>
import type { SuggestionsType } from '@design-system/mock/searchSuggestions'

const route = useRoute()
const query = computed(() => route.query.q)
const { localePath } = useI18n()

if (typeof query.value !== 'string' || !query.value) navigateTo(localePath('/'))

const page = ref(1)
watch(query, () => {
  page.value = 1
})
const { data: searchSuggestions } = await useSearch(query as ComputedRef<string>, page)

const hasSuggestions = (suggestions: SuggestionsType) => {
  return suggestions.categories.some(category => category.suggestions.length > 0)
}
</script>

<template>
  <div class="pt-5">
    <div class="container">
      <h1 v-if="searchSuggestions" class="headline-1">
        {{ $tn(searchSuggestions.results_number.total) }} {{ $ts('search.results') }}
      </h1>
      <h2 class="headline-4">
        {{ $ts('search.youSearchedFor') }} "{{ route.query.q }}"
      </h2>
    </div>

    <div>
      <OrganismsSearchSuggestions class="static shadow-none border-none rounded-none !max-h-none">
        <template v-if="searchSuggestions && hasSuggestions(searchSuggestions)">
          <template v-for="category in searchSuggestions.categories">
            <OrganismsSearchSuggestionsGroup
              v-if="category.suggestions?.length > 0"
              :key="category.id"
              :title="category.name"
              size="large"
            >
              <OrganismsSearchSuggestionItem
                v-for="suggestion in category.suggestions"
                :key="suggestion.id"
                :to="suggestion.to"
                size="large"
                :aria-label="$ts('search.goTo', { label: suggestion.name })"
              >
                <template #prefix>
                  <AtomsIcon v-if="suggestion.prefixIcon" :name="suggestion.prefixIcon" class="text-6xl" />
                </template>
                <AtomsTag v-if="suggestion.tag" :label="suggestion.tag" />
                <template #title>
                  {{ suggestion.name }}
                </template>
                <template #description>
                  {{ suggestion.description }}
                </template>
                <template #suffix>
                  <AtomsIcon name="NavArrowRight" class="text-3xl" />
                </template>
              </OrganismsSearchSuggestionItem>
            </OrganismsSearchSuggestionsGroup>
          </template>
        </template>
        <template v-else>
          <div class="container">
            <OrganismsSearchSuggestionsNoResults class="pl-padding-xs" />
          </div>
        </template>
      </OrganismsSearchSuggestions>
    </div>

    <div v-if="searchSuggestions && hasSuggestions(searchSuggestions)" class="max-w-[45rem] container pt-6 pb-11">
      <MoleculesPagination
        v-model="page"
        size="M"
        :total="Math.max(...searchSuggestions.categories.map(c => c.total))"
        :items-per-page="6 / searchSuggestions.categories.length"
      />
    </div>
  </div>
</template>
