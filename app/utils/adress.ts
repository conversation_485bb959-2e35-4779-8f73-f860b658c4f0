type Address = {
  city: string
  country_code: string
  line_1: string
  line_2?: string
  state_code: string
  zip_code: string
}

export const formatAddress = (address?: Address): string => {
  if (!address) return ''

  const {
    line_1,
    line_2,
    city,
    state_code,
    zip_code,
    country_code,
  } = address

  return `${line_1}${line_2 ? ', ' + line_2 : ''}, ${city}, ${state_code} ${zip_code}, ${country_code}`
}
