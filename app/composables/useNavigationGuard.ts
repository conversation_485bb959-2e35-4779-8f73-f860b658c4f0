type NavigationGuardState = {
  destination: string
  showModal: boolean
  confirmed: boolean
}

export const useNavigationGuard = () => {
  const state = useState<NavigationGuardState>('useNavigationGuard__state', () => ({
    destination: '',
    showModal: false,
    confirmed: false,
  }))

  const destinationPath = computed(() => state.value.destination)
  const isGuardVisible = computed(() => state.value.showModal)
  const isGuardConfirmed = computed(() => state.value.confirmed)

  const setDestinationGuard = (destination: string) => {
    state.value.destination = destination
  }

  const showGuard = () => {
    state.value.showModal = true
  }

  const closeGuard = () => {
    state.value.showModal = false
  }

  const resetGuard = () => {
    state.value.destination = ''
    state.value.showModal = false
    state.value.confirmed = false
  }

  const confirmGuardNavigation = (abortNavigation = false) => {
    const to = state.value.destination
    state.value.confirmed = true
    state.value.showModal = false
    state.value.destination = ''
    if (!abortNavigation) {
      navigateTo(to)
    }
  }

  watch(() => state.value.showModal, (newVal) => {
    if (!newVal) {
      state.value.destination = ''
    }
  })

  return {
    state,
    destinationPath,
    isGuardVisible,
    isGuardConfirmed,
    setDestinationGuard,
    showGuard,
    closeGuard,
    resetGuard,
    confirmGuardNavigation,
  }
}
