// This is a temporary global footer, ideally this logic will be moved in the already created middleware
export default defineNuxtRouteMiddleware((to, from) => {
  const { setDestinationGuard, showGuard, resetGuard, isGuardConfirmed } = useNavigationGuard()
  const pagesWithExitGuard = ['/customer-support/sample-ordering', '/aia-form/program-completion']

  const hasPageExitGuard = pagesWithExitGuard.some(page => from.path.includes(page))

  if (hasPageExitGuard) {
    if (to.fullPath === from.fullPath) return

    if (!isGuardConfirmed.value) {
      setDestinationGuard(to.path)
      showGuard()
      return abortNavigation()
    }
  }
  else {
    resetGuard()
  }
})
