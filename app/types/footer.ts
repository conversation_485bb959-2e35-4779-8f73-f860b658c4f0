import type { AtomsFooterButtonProps } from '@design-system/components/Atoms/AtomsFooterButton.props'
import type { Link } from './link'
import type { SiteType } from '~~/env'

export interface FooterLink {
  title: string | Link
  description?: string
  links?: FooterLink[]
  linkedTags?: AtomsFooterButtonProps[]
  disabled?: boolean
}

export interface FooterColumn {
  links: FooterLink[]
  isSubsection?: boolean
  brand?: SiteType
}
