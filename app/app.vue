<script setup lang='ts'>
import type { SiteType } from '~~/env'

const config = useRuntimeConfig()
const site: SiteType = config.public.site as SiteType

const theme = computed(() => {
  return {
    'theme-skyfold': site === 'skyfold',
    'theme-modernfold': site === 'modernfold',
  }
})

useHead({
  bodyAttrs: {
    'class': theme.value,
    'data-theme': site,
  },
})
</script>

<template>
  <div>
    <NuxtLoadingIndicator color="var(--color-text-accent)" />
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
    <NavigationGuard />
  </div>
</template>
