<script lang="ts" setup>
const { state, closeGuard, confirmGuardNavigation } = useNavigationGuard()
</script>

<template>
  <FormsConfirmationDialog
    v-model="state.showModal"
    :title="$ts('exitGuard.title')"
    :message="$ts('exitGuard.message')"
    status="info"
  >
    <template #actions>
      <AtomsButton
        anatomy="secondary"
        size="m"
        :to="state.destination"
        as="link"
        @click.prevent="confirmGuardNavigation"
      >
        {{ $ts('exitGuard.confirm') }}
      </AtomsButton>
      <AtomsButton
        anatomy="primary"
        size="m"
        @click="closeGuard"
      >
        {{ $ts('exitGuard.cancel') }}
      </AtomsButton>
    </template>
  </FormsConfirmationDialog>
</template>
